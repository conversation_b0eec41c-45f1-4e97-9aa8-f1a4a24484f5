<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.10.1" type="baseline" client="gradle" dependencies="false" name="AGP (8.10.1)" variant="all" version="8.10.1">

    <issue
        id="PropertyEscape"
        message="Windows file separators (`\`) and drive letter separators (&apos;:&apos;) must be escaped (`\\`) in property files; use C\\:/Program Files/Java/jdk-23"
        errorLine1="org.gradle.java.home=C:/Program Files/Java/jdk-23"
        errorLine2="                      ~">
        <location
            file="../gradle.properties"
            line="53"
            column="23"/>
    </issue>

    <issue
        id="RedundantLabel"
        message="Redundant label can be removed"
        errorLine1="                android:label=&quot;@string/app_name&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="21"
            column="17"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        message="A newer version of Gradle than 8.13 is available: 8.14.3"
        errorLine1="distributionUrl=https\://services.gradle.org/distributions/gradle-8.13-bin.zip"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="../gradle/wrapper/gradle-wrapper.properties"
            line="3"
            column="17"/>
    </issue>

    <issue
        id="AutoboxingStateCreation"
        message="Prefer `mutableFloatStateOf` instead of `mutableStateOf`"
        errorLine1="    private var currentLightLevel = mutableStateOf(0f)"
        errorLine2="                                    ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/myapplication5/MainActivity.kt"
            line="41"
            column="37"/>
    </issue>

    <issue
        id="AutoboxingStateCreation"
        message="Prefer `mutableFloatStateOf` instead of `mutableStateOf`"
        errorLine1="    private var currentBrightness = mutableStateOf(0.5f)"
        errorLine2="                                    ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/myapplication5/MainActivity.kt"
            line="43"
            column="37"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_200` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_200&quot;>#FFBB86FC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_500&quot;>#FF6200EE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_700&quot;>#FF3700B3&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="5"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_200` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_200&quot;>#FF03DAC5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="6"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_700&quot;>#FF018786&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="7"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.black` appears to be unused"
        errorLine1="    &lt;color name=&quot;black&quot;>#FF000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="8"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.white` appears to be unused"
        errorLine1="    &lt;color name=&quot;white&quot;>#FFFFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="9"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.brightness_control_title` appears to be unused"
        errorLine1="    &lt;string name=&quot;brightness_control_title&quot;>光感自动亮度调节&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="3"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.sensor_info_title` appears to be unused"
        errorLine1="    &lt;string name=&quot;sensor_info_title&quot;>传感器信息&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="4"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.current_status_title` appears to be unused"
        errorLine1="    &lt;string name=&quot;current_status_title&quot;>当前状态&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="5"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.control_options_title` appears to be unused"
        errorLine1="    &lt;string name=&quot;control_options_title&quot;>控制选项&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="6"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.light_intensity` appears to be unused"
        errorLine1="    &lt;string name=&quot;light_intensity&quot;>光照强度&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="7"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.current_brightness` appears to be unused"
        errorLine1="    &lt;string name=&quot;current_brightness&quot;>当前亮度&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="8"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.sensor_status` appears to be unused"
        errorLine1="    &lt;string name=&quot;sensor_status&quot;>传感器状态&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="9"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.auto_brightness_toggle` appears to be unused"
        errorLine1="    &lt;string name=&quot;auto_brightness_toggle&quot;>自动亮度调节&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="10"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.manual_brightness_control` appears to be unused"
        errorLine1="    &lt;string name=&quot;manual_brightness_control&quot;>手动亮度调节&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="11"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.reset_brightness` appears to be unused"
        errorLine1="    &lt;string name=&quot;reset_brightness&quot;>重置亮度&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="12"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.request_permission` appears to be unused"
        errorLine1="    &lt;string name=&quot;request_permission&quot;>申请权限&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="13"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.permission_granted` appears to be unused"
        errorLine1="    &lt;string name=&quot;permission_granted&quot;>权限授予成功！&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="14"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.permission_required` appears to be unused"
        errorLine1="    &lt;string name=&quot;permission_required&quot;>需要系统设置权限才能调节亮度&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="15"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.permission_request_prompt` appears to be unused"
        errorLine1="    &lt;string name=&quot;permission_request_prompt&quot;>请先授予系统设置权限&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="16"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.sensor_not_supported` appears to be unused"
        errorLine1="    &lt;string name=&quot;sensor_not_supported&quot;>设备不支持光传感器&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="17"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.auto_brightness_enabled` appears to be unused"
        errorLine1="    &lt;string name=&quot;auto_brightness_enabled&quot;>自动亮度已开启&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="18"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.auto_brightness_disabled` appears to be unused"
        errorLine1="    &lt;string name=&quot;auto_brightness_disabled&quot;>自动亮度已关闭&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="19"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.sensor_start_failed` appears to be unused"
        errorLine1="    &lt;string name=&quot;sensor_start_failed&quot;>启动光传感器失败&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="20"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.permission_already_granted` appears to be unused"
        errorLine1="    &lt;string name=&quot;permission_already_granted&quot;>已有系统设置权限&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="21"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.sensor_available` appears to be unused"
        errorLine1="    &lt;string name=&quot;sensor_available&quot;>可用&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="22"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.sensor_unavailable` appears to be unused"
        errorLine1="    &lt;string name=&quot;sensor_unavailable&quot;>不可用&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="23"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.lux_unit` appears to be unused"
        errorLine1="    &lt;string name=&quot;lux_unit&quot;>lux&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.percent_unit` appears to be unused"
        errorLine1="    &lt;string name=&quot;percent_unit&quot;>%&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="25"
            column="13"/>
    </issue>

    <issue
        id="UseKtx"
        message="Use the KTX extension function `String.toUri` instead?"
        errorLine1="                data = Uri.parse(&quot;package:$packageName&quot;)"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/myapplication5/MainActivity.kt"
            line="317"
            column="24"/>
    </issue>

    <issue
        id="UseKtx"
        message="Use the KTX extension function `String.toUri` instead?"
        errorLine1="                data = Uri.parse(&quot;package:$packageName&quot;)"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/myapplication5/MainActivity.kt"
            line="317"
            column="24"/>
    </issue>

</issues>
