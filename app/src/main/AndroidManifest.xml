<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          xmlns:tools="http://schemas.android.com/tools">

    <!-- 系统设置权限 -->
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    
    <!-- 后台运行相关权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_HEALTH" />
    <uses-permission android:name="android.permission.BODY_SENSORS" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    
    <!-- 开机自启权限 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.QUICKBOOT_POWERON" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    
    <!-- 电池优化豁免权限 -->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <application
            android:allowBackup="true"
            android:dataExtractionRules="@xml/data_extraction_rules"
            android:fullBackupContent="@xml/backup_rules"
            android:icon="@mipmap/ic_launcher"
            android:label="@string/app_name"
            android:roundIcon="@mipmap/ic_launcher"
            android:supportsRtl="true"
            android:theme="@style/Theme.MyApplication5"
            tools:targetApi="31">
        
        <!-- 主Activity -->
        <activity
                android:name=".MainActivity"
                android:exported="true"
                android:label="@string/app_name"
                android:theme="@style/Theme.MyApplication5"
                android:launchMode="singleTop">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        
        <!-- 个性化亮度设置Activity -->
        <activity
                android:name=".PersonalizedBrightnessActivity"
                android:exported="false"
                android:label="个性化亮度设置"
                android:theme="@style/Theme.MyApplication5"
                android:parentActivityName=".MainActivity">
            <meta-data
                    android:name="android.support.PARENT_ACTIVITY"
                    android:value=".MainActivity" />
        </activity>
        
        <!-- 诊断工具Activity -->
        <activity
                android:name=".DiagnosticActivity"
                android:exported="false"
                android:label="护眼应用诊断工具"
                android:theme="@style/Theme.MyApplication5"
                android:parentActivityName=".MainActivity">
            <meta-data
                    android:name="android.support.PARENT_ACTIVITY"
                    android:value=".MainActivity" />
        </activity>
        
        <!-- 护眼后台服务 -->
        <service
                android:name=".EyeCareBackgroundService"
                android:enabled="true"
                android:exported="false"
                android:foregroundServiceType="health" />
        
        <!-- 开机自启接收器 -->
        <receiver
                android:name=".BootReceiver"
                android:enabled="true"
                android:exported="true"
                android:permission="android.permission.RECEIVE_BOOT_COMPLETED">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />
                <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
        </receiver>
        
        <!-- 通知点击接收器 -->
        <receiver
                android:name=".NotificationReceiver"
                android:enabled="true"
                android:exported="false">
            <intent-filter>
                <action android:name="com.example.myapplication5.STOP_SERVICE" />
                <action android:name="com.example.myapplication5.OPEN_APP" />
            </intent-filter>
        </receiver>

    </application>

</manifest>