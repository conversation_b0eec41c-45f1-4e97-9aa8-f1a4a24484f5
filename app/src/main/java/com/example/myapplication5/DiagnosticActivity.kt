package com.example.myapplication5

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.example.myapplication5.ui.theme.MyApplication5Theme
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * 诊断工具专用活动页面
 * 
 * 功能特色：
 * 1. 专业的诊断界面设计
 * 2. 实时状态监控
 * 3. 一键操作按钮
 * 4. 详细的诊断报告
 * 5. 美观的卡片布局
 * 
 * <AUTHOR> v1.0
 * @since 1.0 - 专业诊断工具界面
 */
class DiagnosticActivity : ComponentActivity() {
    
    private lateinit var brightnessController: BrightnessController
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 初始化亮度控制器
        brightnessController = BrightnessController(this)
        
        setContent {
            MyApplication5Theme {
                DiagnosticScreen()
            }
        }
    }
    
    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    fun DiagnosticScreen() {
        var isLoading by remember { mutableStateOf(false) }
        var lastUpdateTime by remember { mutableStateOf("") }
        var serviceStatus by remember { mutableStateOf("检查中...") }
        var permissionStatus by remember { mutableStateOf("检查中...") }
        var brightnessInfo by remember { mutableStateOf("检查中...") }
        var systemAutoStatus by remember { mutableStateOf("检查中...") }
        var diagnosticResult by remember { mutableStateOf("") }
        var diagnosticResults by remember { mutableStateOf(mutableListOf<String>()) }
        
        val context = LocalContext.current
        val scope = rememberCoroutineScope()
        
        // 初始化时进行状态检查
        LaunchedEffect(Unit) {
            updateStatus(
                onServiceStatus = { serviceStatus = it },
                onPermissionStatus = { permissionStatus = it },
                onBrightnessInfo = { brightnessInfo = it },
                onSystemAutoStatus = { systemAutoStatus = it },
                onLastUpdateTime = { lastUpdateTime = it }
            )
        }
        
        Scaffold(
            topBar = {
                TopAppBar(
                    title = { 
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Filled.Settings,
                                contentDescription = "诊断工具",
                                tint = MaterialTheme.colorScheme.onSurface
                            )
                            Text("护眼应用诊断工具") 
                        }
                    },
                    navigationIcon = {
                        IconButton(
                            onClick = { finish() }
                        ) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "返回"
                            )
                        }
                    },
                    actions = {
                        IconButton(
                            onClick = {
                                scope.launch {
                                    isLoading = true
                                    updateStatus(
                                        onServiceStatus = { serviceStatus = it },
                                        onPermissionStatus = { permissionStatus = it },
                                        onBrightnessInfo = { brightnessInfo = it },
                                        onSystemAutoStatus = { systemAutoStatus = it },
                                        onLastUpdateTime = { lastUpdateTime = it }
                                    )
                                    isLoading = false
                                }
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Filled.Refresh,
                                contentDescription = "刷新状态"
                            )
                        }
                    }
                )
            }
        ) { paddingValues ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(16.dp)
                    .verticalScroll(rememberScrollState()),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                
                // 实时状态卡片
                StatusCard(
                    isLoading = isLoading,
                    lastUpdateTime = lastUpdateTime,
                    serviceStatus = serviceStatus,
                    permissionStatus = permissionStatus,
                    brightnessInfo = brightnessInfo,
                    systemAutoStatus = systemAutoStatus
                )
                
                // 快速操作卡片
                QuickActionsCard(
                    onRestartService = {
                        scope.launch {
                            try {
                                if (EyeCareBackgroundService.isServiceRunning) {
                                    stopBackgroundService()
                                }
                                startBackgroundService()
                                Toast.makeText(this@DiagnosticActivity, "✅ 服务重启成功", Toast.LENGTH_SHORT).show()
                                delay(1000)
                                updateStatus(
                                    onServiceStatus = { serviceStatus = it },
                                    onPermissionStatus = { permissionStatus = it },
                                    onBrightnessInfo = { brightnessInfo = it },
                                    onSystemAutoStatus = { systemAutoStatus = it },
                                    onLastUpdateTime = { lastUpdateTime = it }
                                )
                            } catch (e: Exception) {
                                Toast.makeText(this@DiagnosticActivity, "❌ 服务重启失败: ${e.message}", Toast.LENGTH_SHORT).show()
                            }
                        }
                    },
                    onOpenPermissionSettings = {
                        try {
                            val intent = Intent(Settings.ACTION_MANAGE_WRITE_SETTINGS)
                            intent.data = Uri.parse("package:$packageName")
                            startActivity(intent)
                        } catch (e: Exception) {
                            Toast.makeText(this@DiagnosticActivity, "❌ 无法打开权限设置", Toast.LENGTH_SHORT).show()
                        }
                    },
                    onOpenSystemSettings = {
                        try {
                            val intent = Intent(Settings.ACTION_DISPLAY_SETTINGS)
                            startActivity(intent)
                        } catch (e: Exception) {
                            Toast.makeText(this@DiagnosticActivity, "❌ 无法打开显示设置", Toast.LENGTH_SHORT).show()
                        }
                    }
                )
                
                // 专业诊断工具卡片
                ProfessionalToolsCard(
                    onRunDiagnostic = {
                        scope.launch {
                            try {
                                isLoading = true
                                val result = ServiceTestHelper.getDiagnosticInfo(this@DiagnosticActivity)
                                diagnosticResult = result
                                isLoading = false
                            } catch (e: Exception) {
                                diagnosticResult = "诊断失败: ${e.message}"
                                isLoading = false
                            }
                        }
                    },
                    onPermissionTest = {
                        scope.launch {
                            try {
                                isLoading = true
                                val result = ServiceTestHelper.testWriteSettingsPermission(this@DiagnosticActivity)
                                diagnosticResult = result
                                isLoading = false
                            } catch (e: Exception) {
                                diagnosticResult = "权限测试失败: ${e.message}"
                                isLoading = false
                            }
                        }
                    },
                    onStabilityTest = {
                        scope.launch {
                            try {
                                isLoading = true
                                val result = ServiceTestHelper.testBackendServiceStability(this@DiagnosticActivity)
                                diagnosticResult = result
                                isLoading = false
                            } catch (e: Exception) {
                                diagnosticResult = "稳定性测试失败: ${e.message}"
                                isLoading = false
                            }
                        }
                    },
                    onHealthCheck = {
                        scope.launch {
                            try {
                                isLoading = true
                                val result = ServiceMonitor.performHealthCheck(this@DiagnosticActivity)
                                diagnosticResult = result
                                isLoading = false
                            } catch (e: Exception) {
                                diagnosticResult = "健康检查失败: ${e.message}"
                                isLoading = false
                            }
                        }
                    },
                    onSensorDiagnostic = {
                        scope.launch {
                            try {
                                isLoading = true
                                val result = performSensorDiagnostic()
                                diagnosticResult = result
                                isLoading = false
                            } catch (e: Exception) {
                                diagnosticResult = "传感器诊断失败: ${e.message}"
                                isLoading = false
                            }
                        }
                    },
                    onEmergencyRecovery = {
                        scope.launch {
                            try {
                                isLoading = true
                                val success = ServiceStabilityEnhancer.emergencyRecovery(this@DiagnosticActivity)
                                diagnosticResult = if (success) {
                                    "✅ 紧急恢复成功，传感器已重新激活"
                                } else {
                                    "❌ 紧急恢复失败，请手动重启应用"
                                }
                                isLoading = false
                                if (success) {
                                    delay(1000)
                                    updateStatus(
                                        onServiceStatus = { serviceStatus = it },
                                        onPermissionStatus = { permissionStatus = it },
                                        onBrightnessInfo = { brightnessInfo = it },
                                        onSystemAutoStatus = { systemAutoStatus = it },
                                        onLastUpdateTime = { lastUpdateTime = it }
                                    )
                                }
                            } catch (e: Exception) {
                                diagnosticResult = "紧急恢复失败: ${e.message}"
                                isLoading = false
                            }
                        }
                    },
                    onStabilityReport = {
                        scope.launch {
                            try {
                                isLoading = true
                                val result = ServiceStabilityEnhancer.getStabilityReport(this@DiagnosticActivity)
                                diagnosticResult = result
                                isLoading = false
                            } catch (e: Exception) {
                                diagnosticResult = "稳定性报告失败: ${e.message}"
                                isLoading = false
                            }
                        }
                    },
                    onOutdoorTest = {
                        scope.launch {
                            try {
                                isLoading = true
                                val result = performOutdoorSensorTest()
                                diagnosticResult = result
                                isLoading = false
                            } catch (e: Exception) {
                                diagnosticResult = "户外环境检测失败: ${e.message}"
                                isLoading = false
                            }
                        }
                    }
                )
                
                // 诊断结果显示卡片
                if (diagnosticResult.isNotEmpty()) {
                    DiagnosticResultCard(
                        result = diagnosticResult,
                        onClear = { diagnosticResult = "" }
                    )
                }
                
                // 加载指示器
                if (isLoading) {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                        )
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            horizontalArrangement = Arrangement.Center,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(24.dp),
                                strokeWidth = 2.dp
                            )
                            Spacer(modifier = Modifier.width(16.dp))
                            Text(
                                text = "正在执行诊断...",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurface
                            )
                        }
                    }
                }
            }
        }
    }
    
    @Composable
    fun StatusCard(
        isLoading: Boolean,
        lastUpdateTime: String,
        serviceStatus: String,
        permissionStatus: String,
        brightnessInfo: String,
        systemAutoStatus: String
    ) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
            )
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Info,
                            contentDescription = "状态信息",
                            tint = MaterialTheme.colorScheme.primary
                        )
                        Text(
                            text = "实时状态监控",
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                    }
                    
                    if (isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            strokeWidth = 2.dp
                        )
                    }
                }
                
                if (lastUpdateTime.isNotEmpty()) {
                    Text(
                        text = "📅 最后更新：$lastUpdateTime",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                StatusItem(
                    icon = Icons.Filled.Build,
                    title = "后台服务",
                    status = serviceStatus
                )
                
                StatusItem(
                    icon = Icons.Filled.Lock,
                    title = "系统权限",
                    status = permissionStatus
                )
                
                StatusItem(
                    icon = Icons.Filled.Phone,
                    title = "屏幕亮度",
                    status = brightnessInfo
                )
                
                StatusItem(
                    icon = Icons.Filled.Warning,
                    title = "系统自动亮度",
                    status = systemAutoStatus
                )
            }
        }
    }
    
    @Composable
    fun StatusItem(
        icon: ImageVector,
        title: String,
        status: String
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.size(20.dp)
            )
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = status,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
    
    @Composable
    fun QuickActionsCard(
        onRestartService: () -> Unit,
        onOpenPermissionSettings: () -> Unit,
        onOpenSystemSettings: () -> Unit
    ) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
            )
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Filled.PlayArrow,
                        contentDescription = "快速操作",
                        tint = MaterialTheme.colorScheme.secondary
                    )
                    Text(
                        text = "快速操作",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Button(
                        onClick = onRestartService,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.primary
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Refresh,
                            contentDescription = "重启服务",
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("重启服务")
                    }
                    
                    OutlinedButton(
                        onClick = onOpenPermissionSettings,
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Lock,
                            contentDescription = "权限设置",
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("权限设置")
                    }
                }
                
                OutlinedButton(
                    onClick = onOpenSystemSettings,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        imageVector = Icons.Filled.Settings,
                        contentDescription = "显示设置",
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("打开显示设置（关闭系统自动亮度）")
                }
            }
        }
    }
    
    @Composable
    fun ProfessionalToolsCard(
        onRunDiagnostic: () -> Unit,
        onPermissionTest: () -> Unit,
        onStabilityTest: () -> Unit,
        onHealthCheck: () -> Unit,
        onSensorDiagnostic: () -> Unit,
        onEmergencyRecovery: () -> Unit,
        onStabilityReport: () -> Unit,
        onOutdoorTest: () -> Unit
    ) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.tertiaryContainer.copy(alpha = 0.3f)
            )
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Filled.Build,
                        contentDescription = "专业工具",
                        tint = MaterialTheme.colorScheme.tertiary
                    )
                    Text(
                        text = "专业诊断工具",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
                
                val tools = listOf(
                    Triple("🔍 运行诊断", "获取详细系统信息", onRunDiagnostic),
                    Triple("🔑 权限测试", "测试系统权限状态", onPermissionTest),
                    Triple("🛡️ 稳定性测试", "检查后台服务稳定性", onStabilityTest),
                    Triple("❤️ 健康检查", "全面健康状态检查", onHealthCheck),
                    Triple("🧠 传感器诊断", "智能传感器健康检查", onSensorDiagnostic),
                    Triple("🚨 紧急恢复", "强制重启所有服务", onEmergencyRecovery),
                    Triple("📋 稳定性报告", "生成详细稳定性报告", onStabilityReport),
                    Triple("🌞 户外环境检测", "检测户外环境光传感器响应", onOutdoorTest)
                )
                
                tools.chunked(2).forEach { rowTools ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        rowTools.forEach { (title, description, action) ->
                            OutlinedCard(
                                onClick = action,
                                modifier = Modifier.weight(1f),
                                colors = CardDefaults.outlinedCardColors(
                                    containerColor = MaterialTheme.colorScheme.surface
                                )
                            ) {
                                Column(
                                    modifier = Modifier.padding(16.dp),
                                    verticalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    Text(
                                        text = title,
                                        style = MaterialTheme.typography.titleSmall,
                                        fontWeight = FontWeight.Bold,
                                        color = MaterialTheme.colorScheme.onSurface,
                                        textAlign = TextAlign.Center,
                                        modifier = Modifier.fillMaxWidth()
                                    )
                                    Text(
                                        text = description,
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                                        textAlign = TextAlign.Center,
                                        modifier = Modifier.fillMaxWidth()
                                    )
                                }
                            }
                        }
                        
                        // 如果这一行只有一个工具，添加空的占位符
                        if (rowTools.size == 1) {
                            Spacer(modifier = Modifier.weight(1f))
                        }
                    }
                }
            }
        }
    }
    
    @Composable
    fun DiagnosticResultCard(
        result: String,
        onClear: () -> Unit
    ) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
            )
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Filled.CheckCircle,
                            contentDescription = "诊断结果",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = "诊断结果",
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                    }
                    
                    IconButton(onClick = onClear) {
                        Icon(
                            imageVector = Icons.Filled.Clear,
                            contentDescription = "清除结果"
                        )
                    }
                }
                
                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp),
                    color = MaterialTheme.colorScheme.surface,
                    tonalElevation = 2.dp
                ) {
                    Text(
                        text = result,
                        modifier = Modifier.padding(16.dp),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedButton(
                        onClick = {
                            try {
                                Log.i("DiagnosticResult", result)
                                Toast.makeText(this@DiagnosticActivity, "结果已复制到日志", Toast.LENGTH_SHORT).show()
                            } catch (e: Exception) {
                                Toast.makeText(this@DiagnosticActivity, "复制失败", Toast.LENGTH_SHORT).show()
                            }
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Share,
                            contentDescription = "复制到日志",
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("复制到日志")
                    }
                }
            }
        }
    }
    
    /**
     * 执行传感器诊断
     */
    private suspend fun performSensorDiagnostic(): String {
        return try {
            val currentTime = System.currentTimeMillis()
            val report = StringBuilder()
            
            report.appendLine("🧠 智能传感器诊断报告")
            report.appendLine()
            report.appendLine("📅 检查时间：${SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(currentTime)}")
            report.appendLine()
            
            // 尝试获取传感器信息
            try {
                // 创建临时传感器管理器进行检查
                val tempSensorManager = LightSensorManager(this) { }
                
                // 基础传感器信息
                val sensorInfo = tempSensorManager.getSensorInfo()
                report.appendLine("📱 传感器硬件信息：")
                report.appendLine(sensorInfo)
                report.appendLine()
                
                // 传感器可用性检查
                val isAvailable = tempSensorManager.isLightSensorAvailable()
                report.appendLine("🔍 传感器可用性：${if (isAvailable) "✅ 可用" else "❌ 不可用"}")
                
                if (isAvailable) {
                    // 启动传感器进行测试
                    val startSuccess = tempSensorManager.startListening()
                    report.appendLine("🚀 传感器启动测试：${if (startSuccess) "✅ 成功" else "❌ 失败"}")
                    
                    if (startSuccess) {
                        // 等待一段时间收集数据
                        delay(3000)
                        
                        // 获取健康状态
                        val health = tempSensorManager.getSensorHealth()
                        val lastUpdate = tempSensorManager.getLastUpdateTime()
                        val timeSinceUpdate = (currentTime - lastUpdate) / 1000
                        val needsRecovery = tempSensorManager.needsRecovery()
                        
                        report.appendLine("💚 传感器健康状态：$health")
                        report.appendLine("⏱️ 距离上次数据更新：${timeSinceUpdate}秒")
                        report.appendLine("🔧 是否需要恢复：${if (needsRecovery) "⚠️ 是" else "✅ 否"}")
                        
                        // 获取详细健康报告
                        report.appendLine()
                        report.appendLine("📊 详细健康报告：")
                        report.appendLine(tempSensorManager.getHealthReport())
                        
                        // 传感器响应性测试
                        report.appendLine()
                        report.appendLine("🧪 传感器响应性测试：")
                        val currentLevel = tempSensorManager.getCurrentLightLevel()
                        if (currentLevel > 0) {
                            report.appendLine("✅ 传感器有数据输出：${String.format("%.1f", currentLevel)} lux")
                            report.appendLine("🌍 检测到的环境：${tempSensorManager.getCurrentEnvironment()}")
                        } else {
                            report.appendLine("⚠️ 传感器暂无数据输出")
                        }
                        
                        // 建议和解决方案
                        report.appendLine()
                        report.appendLine("💡 诊断建议：")
                        when (health) {
                            LightSensorManager.SensorHealth.HEALTHY -> {
                                report.appendLine("✅ 传感器工作完全正常，无需任何操作")
                            }
                            LightSensorManager.SensorHealth.SLOW_RESPONSE -> {
                                report.appendLine("⚠️ 传感器响应较慢，建议：")
                                report.appendLine("   • 检查手机是否过热")
                                report.appendLine("   • 清理后台应用释放内存")
                                report.appendLine("   • 使用诊断工具的'紧急恢复'功能")
                            }
                            LightSensorManager.SensorHealth.DATA_STALE -> {
                                report.appendLine("⚠️ 传感器数据更新延迟，建议：")
                                report.appendLine("   • 重启护眼应用")
                                report.appendLine("   • 使用'紧急恢复'功能")
                                report.appendLine("   • 检查系统电池优化设置")
                            }
                            LightSensorManager.SensorHealth.DEADLOCK -> {
                                report.appendLine("❌ 传感器假死状态，建议：")
                                report.appendLine("   • 立即使用'紧急恢复'功能")
                                report.appendLine("   • 关闭电池优化")
                                report.appendLine("   • 重启手机")
                            }
                            LightSensorManager.SensorHealth.HARDWARE_ERROR -> {
                                report.appendLine("❌ 传感器硬件错误，建议：")
                                report.appendLine("   • 重启手机")
                                report.appendLine("   • 检查其他应用是否能正常使用传感器")
                                report.appendLine("   • 如问题持续，可能是硬件故障")
                            }
                            else -> {
                                report.appendLine("🔍 传感器状态未知，正在初始化中")
                            }
                        }
                        
                        // 停止测试传感器
                        tempSensorManager.stopListening()
                    }
                } else {
                    report.appendLine("❌ 设备不支持光传感器或传感器硬件故障")
                    report.appendLine()
                    report.appendLine("💡 建议：")
                    report.appendLine("   • 使用手动亮度调节模式")
                    report.appendLine("   • 检查设备规格确认是否支持光传感器")
                }
                
            } catch (e: Exception) {
                report.appendLine("❌ 传感器诊断过程中发生错误：${e.message}")
                report.appendLine()
                report.appendLine("💡 建议：")
                report.appendLine("   • 重启应用后重新尝试")
                report.appendLine("   • 使用'紧急恢复'功能")
                report.appendLine("   • 重启手机")
            }
            
            report.toString()
        } catch (e: Exception) {
            "传感器诊断失败：${e.message}"
        }
    }
    
    /**
     * 户外环境传感器检测 - 专门检测户外环境下的传感器响应
     */
    private suspend fun performOutdoorSensorTest(): String {
        val results = mutableListOf<String>()
        results.add("🌞 开始户外环境传感器检测...")
        
        try {
            val lightSensorManager = LightSensorManager(this) { lightLevel ->
                // 这个回调在测试中不会被调用，仅用于创建实例
            }
            
            if (!lightSensorManager.isLightSensorAvailable()) {
                results.add("❌ 设备不支持光传感器")
                return results.joinToString("\n")
            }
            
            // 检测传感器基本信息
            val sensorInfo = lightSensorManager.getSensorInfo()
            results.add("✅ 光传感器可用")
            results.add("📊 传感器信息: $sensorInfo")
            
            // 检测当前环境
            val currentLightLevel = lightSensorManager.getCurrentLightLevel()
            val currentEnvironment = lightSensorManager.getCurrentEnvironment()
            
            results.add("🌍 当前环境: $currentEnvironment")
            results.add("💡 当前光照: ${currentLightLevel} lux")
            
            // 户外环境特定检测
            if (currentEnvironment == LightSensorManager.EnvironmentType.OUTDOOR) {
                results.add("✅ 已检测到户外环境")
                
                // 检查户外环境参数
                val outdoorThreshold = 15.0f  // 户外环境阈值
                val outdoorDelay = 2500L      // 户外环境延迟
                
                results.add("📏 户外变化阈值: ${outdoorThreshold} lux")
                results.add("⏱️ 户外更新延迟: ${outdoorDelay}ms")
                
                // 模拟户外环境变化检测
                results.add("🧪 模拟户外环境变化检测...")
                
                // 检查传感器响应性
                val lastUpdateTime = lightSensorManager.getLastUpdateTime()
                val timeSinceUpdate = System.currentTimeMillis() - lastUpdateTime
                
                if (timeSinceUpdate < 10000) {  // 10秒内有更新
                    results.add("✅ 传感器响应正常")
                } else {
                    results.add("⚠️ 传感器响应较慢: ${timeSinceUpdate}ms")
                }
                
            } else {
                results.add("ℹ️ 当前非户外环境，建议在户外测试")
                results.add("💡 户外环境阈值: >300 lux")
            }
            
            // 检查传感器健康状态
            val sensorHealth = lightSensorManager.getSensorHealth()
            results.add("🏥 传感器健康状态: $sensorHealth")
            
            if (sensorHealth == LightSensorManager.SensorHealth.HEALTHY) {
                results.add("✅ 传感器工作正常")
            } else {
                results.add("⚠️ 传感器状态异常，建议重启")
            }
            
        } catch (e: Exception) {
            results.add("❌ 户外传感器检测失败: ${e.message}")
        }
        
        results.add("🌞 户外环境传感器检测完成")
        return results.joinToString("\n")
    }
    
    private suspend fun updateStatus(
        onServiceStatus: (String) -> Unit,
        onPermissionStatus: (String) -> Unit,
        onBrightnessInfo: (String) -> Unit,
        onSystemAutoStatus: (String) -> Unit,
        onLastUpdateTime: (String) -> Unit
    ) {
        try {
            // 更新时间
            val currentTime = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(System.currentTimeMillis())
            onLastUpdateTime(currentTime)
            
            // 检查服务状态
            val serviceRunning = EyeCareBackgroundService.isServiceRunning
            onServiceStatus(if (serviceRunning) "✅ 运行中" else "❌ 已停止")
            
            // 检查权限状态
            val hasPermission = try {
                brightnessController.hasWriteSettingsPermission()
            } catch (e: Exception) {
                false
            }
            onPermissionStatus(if (hasPermission) "✅ 已授权" else "❌ 未授权")
            
            // 检查亮度信息
            val currentBrightness = try {
                (brightnessController.getCurrentBrightness() * 100).toInt()
            } catch (e: Exception) {
                0
            }
            onBrightnessInfo("${currentBrightness}%")
            
            // 检查系统自动亮度
            val systemAutoEnabled = try {
                brightnessController.isSystemAutoBrightnessEnabled()
            } catch (e: Exception) {
                false
            }
            onSystemAutoStatus(if (systemAutoEnabled) "⚠️ 已开启（建议关闭）" else "✅ 已关闭")
            
        } catch (e: Exception) {
            Log.e("DiagnosticActivity", "更新状态失败: ${e.message}")
        }
    }
    
    private fun startBackgroundService() {
        try {
            val serviceIntent = Intent(this, EyeCareBackgroundService::class.java)
            startForegroundService(serviceIntent)
        } catch (e: Exception) {
            Log.e("DiagnosticActivity", "启动服务失败: ${e.message}")
        }
    }
    
    private fun stopBackgroundService() {
        try {
            val serviceIntent = Intent(this, EyeCareBackgroundService::class.java)
            stopService(serviceIntent)
        } catch (e: Exception) {
            Log.e("DiagnosticActivity", "停止服务失败: ${e.message}")
        }
    }
} 