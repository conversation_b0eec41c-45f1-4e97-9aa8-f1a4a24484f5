package com.example.myapplication5

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.util.Log
import kotlin.math.abs

/**
 * 光传感器管理器类 - 专为干眼症患者设计
 * 负责监听环境光传感器数据并触发亮度调节
 * 
 * 核心优化：
 * 1. 户外环境检测和特殊处理
 * 2. 防闪频机制 - 避免亮度频繁跳动
 * 3. 数据平滑处理 - 减少传感器噪声
 * 4. 智能更新频率 - 根据环境动态调整
 * 5. 增强的光照阈值 - 减少不必要的调节
 * 6. 智能假死检测 - 检测传感器数据流中断
 * 7. 多级恢复策略 - 自动恢复传感器工作状态
 * 
 * <AUTHOR> v2.1
 * @since 2.1 - 新增传感器假死检测和智能恢复机制
 */
class LightSensorManager(
    private val context: Context,
    private val onLightChanged: (Float) -> Unit
) : SensorEventListener {

    companion object {
        private const val TAG = "LightSensorManager"
        
        /** 基础传感器更新延迟时间 (毫秒) - 大幅提高响应性 */
        private const val BASE_SENSOR_DELAY = 1000L     // 降低到1秒，大幅提高响应性
        /** 户外环境下的更新延迟 (毫秒) - 户外快速响应 */
        private const val OUTDOOR_SENSOR_DELAY = 1200L  // 降低到1.2秒，提高户外响应
        /** 夜间环境下的更新延迟 (毫秒) - 夜间快速响应 */
        private const val NIGHT_SENSOR_DELAY = 1500L    // 降低到1.5秒，提高夜间响应
        /** 极暗环境更新延迟 (毫秒) - 快速响应 */
        private const val ULTRA_DARK_SENSOR_DELAY = 2000L // 降低到2秒，提高响应性
        
        /** 微小光照变化阈值 - 提高敏感度 */
        private const val MICRO_LIGHT_THRESHOLD = 1.0f  // 提高敏感度
        /** 基础光照变化阈值 - 提高响应性 */
        private const val BASE_LIGHT_THRESHOLD = 3.0f   // 提高敏感度，大幅提高响应性
        /** 户外环境光照阈值 - 户外变化更敏感 */
        private const val OUTDOOR_LIGHT_THRESHOLD = 10.0f // 降低阈值，大幅提高户外响应
        /** 夜间环境光照阈值 - 夜间更敏感 */
        private const val NIGHT_LIGHT_THRESHOLD = 0.8f  // 降低阈值，提高夜间响应
        
        /** 户外环境光照强度阈值 (lux) - 更早识别户外 */
        private const val OUTDOOR_LIGHT_THRESHOLD_LUX = 200.0f  // 降低阈值，更早识别户外
        /** 夜间环境光照强度阈值 (lux) */
        private const val NIGHT_LIGHT_THRESHOLD_LUX = 10.0f     // 降低阈值，更早识别夜间
        
        /** 数据平滑窗口大小 - 减少平滑提高响应性 */
        private const val SMOOTHING_WINDOW_SIZE = 3      // 降低到3点平滑，大幅提高响应性
        /** 异常值检测阈值 - 放宽过滤提高响应性 */
        private const val OUTLIER_THRESHOLD = 0.50f     // 50%变化率 (大幅放宽)
        /** 极暗环境异常值阈值 - 放宽过滤 */
        private const val ULTRA_DARK_OUTLIER_THRESHOLD = 0.30f  // 30%变化率
        
        // 新增：智能假死检测参数
        /** 传感器数据超时阈值 (毫秒) - 缩短超时时间 */
        private const val SENSOR_TIMEOUT_THRESHOLD = 8000L   // 降低到8秒，快速检测问题
        /** 传感器响应性测试间隔 (毫秒) */
        private const val RESPONSIVENESS_CHECK_INTERVAL = 10000L  // 降低到10秒，快速检测
        /** 最大重启尝试次数 */
        private const val MAX_RESTART_ATTEMPTS = 3
        /** 重启尝试间隔 (毫秒) */
        private const val RESTART_ATTEMPT_INTERVAL = 1000L  // 降低到1秒，快速恢复
    }

    private val sensorManager: SensorManager = 
        context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
    private val lightSensor: Sensor? = 
        sensorManager.getDefaultSensor(Sensor.TYPE_LIGHT)
    
    /** 当前光照强度 */
    private var currentLightLevel: Float = 0f
    /** 平滑后的光照强度 */
    private var smoothedLightLevel: Float = 0f
    /** 最后更新时间 */
    private var lastUpdateTime: Long = 0L
    /** 是否正在监听传感器 */
    private var isListening: Boolean = false
    
    /** 数据平滑处理的历史数据 */
    private val lightLevelHistory = mutableListOf<Float>()
    /** 当前环境类型 */
    private var currentEnvironment = EnvironmentType.INDOOR
    /** 上次环境检测时间 */
    private var lastEnvironmentCheckTime = 0L
    /** 环境检测间隔 (毫秒) - 大幅缩短提高响应性 */
    private val ENVIRONMENT_CHECK_INTERVAL = 3000L  // 降低到3秒，大幅提高环境切换响应性

    // 新增：智能假死检测相关字段
    /** 传感器启动时间 */
    private var sensorStartTime: Long = 0L
    /** 上次响应性检查时间 */
    private var lastResponsivenessCheck: Long = 0L
    /** 重启尝试次数 */
    private var restartAttempts: Int = 0
    /** 传感器健康状态 */
    private var sensorHealth: SensorHealth = SensorHealth.UNKNOWN
    /** 数据更新计数器 */
    private var dataUpdateCount: Long = 0L
    /** 上次数据更新计数检查时间 */
    private var lastUpdateCountCheck: Long = 0L

    /**
     * 环境类型枚举
     */
    enum class EnvironmentType {
        INDOOR,     // 室内环境
        OUTDOOR,    // 户外环境
        NIGHT       // 夜间环境
    }

    /**
     * 传感器健康状态枚举
     */
    enum class SensorHealth {
        UNKNOWN,        // 未知状态
        HEALTHY,        // 健康状态
        SLOW_RESPONSE,  // 响应缓慢
        DATA_STALE,     // 数据过时
        DEADLOCK,       // 假死状态
        HARDWARE_ERROR  // 硬件错误
    }

    /**
     * 开始监听光传感器 - 支持户外快速响应模式
     *
     * @param fastMode 是否启用快速模式（户外环境使用）
     * @return true表示开始监听成功，false表示设备不支持光传感器
     */
    fun startListening(fastMode: Boolean = false): Boolean {
        if (lightSensor == null) {
            Log.w(TAG, "设备不支持光传感器")
            sensorHealth = SensorHealth.HARDWARE_ERROR
            return false
        }

        if (isListening) {
            Log.d(TAG, "光传感器已在监听中")
            return true
        }

        // 根据模式选择传感器采样率
        val sensorDelay = if (fastMode) {
            SensorManager.SENSOR_DELAY_GAME  // 更快的采样率用于户外环境
        } else {
            SensorManager.SENSOR_DELAY_NORMAL
        }

        val success = sensorManager.registerListener(
            this,
            lightSensor,
            sensorDelay
        )

        if (success) {
            isListening = true
            sensorStartTime = System.currentTimeMillis()
            lastResponsivenessCheck = sensorStartTime
            lastUpdateCountCheck = sensorStartTime
            dataUpdateCount = 0L
            restartAttempts = 0
            sensorHealth = SensorHealth.UNKNOWN
            val modeText = if (fastMode) "快速模式（户外优化）" else "标准模式"
            Log.d(TAG, "开始监听光传感器 - $modeText - 已启用防闪频优化和智能检测")
        } else {
            Log.e(TAG, "注册光传感器监听器失败")
            sensorHealth = SensorHealth.HARDWARE_ERROR
        }

        return success
    }

    /**
     * 停止监听光传感器
     */
    fun stopListening() {
        if (!isListening) {
            return
        }

        sensorManager.unregisterListener(this)
        isListening = false
        lightLevelHistory.clear()
        Log.d(TAG, "停止监听光传感器")
    }

    /**
     * 检查设备是否支持光传感器
     * 
     * @return true表示支持，false表示不支持
     */
    fun isLightSensorAvailable(): Boolean {
        return lightSensor != null
    }

    /**
     * 检查传感器是否正在监听
     * 
     * @return true表示正在监听，false表示未监听
     */
    fun isListening(): Boolean {
        return isListening
    }

    /**
     * 获取最后更新时间
     * 
     * @return 最后更新时间戳
     */
    fun getLastUpdateTime(): Long {
        return lastUpdateTime
    }

    /**
     * 强制重启传感器监听 - 增强版智能恢复
     *
     * @param fastMode 是否启用快速模式（户外环境使用）
     * @return true表示重启成功，false表示重启失败
     */
    fun forceRestartListening(fastMode: Boolean = false): Boolean {
        return try {
            val modeText = if (fastMode) "快速模式" else "智能恢复模式"
            Log.d(TAG, "强制重启传感器监听 ($modeText)")

            // 先停止监听
            stopListening()

            // 清空历史数据和重置所有状态
            lightLevelHistory.clear()
            currentLightLevel = 0f
            smoothedLightLevel = 0f
            lastUpdateTime = 0L
            dataUpdateCount = 0L

            // 延迟一点时间后重新启动
            Thread.sleep(500)

            // 重新启动监听
            val success = startListening(fastMode)
            
            if (success) {
                // 重置恢复相关状态
                restartAttempts = 0
                sensorHealth = SensorHealth.UNKNOWN
                Log.d(TAG, "传感器监听强制重启成功，已重置智能检测状态")
            } else {
                Log.e(TAG, "传感器监听强制重启失败")
                sensorHealth = SensorHealth.HARDWARE_ERROR
            }
            
            success
        } catch (e: Exception) {
            Log.e(TAG, "强制重启传感器监听异常: ${e.message}")
            sensorHealth = SensorHealth.HARDWARE_ERROR
            false
        }
    }

    /**
     * 获取当前光照强度
     * 
     * @return 当前光照强度值 (lux)
     */
    fun getCurrentLightLevel(): Float {
        return smoothedLightLevel
    }

    /**
     * 获取当前环境类型
     */
    fun getCurrentEnvironment(): EnvironmentType {
        return currentEnvironment
    }

    /**
     * 检测环境类型 - 增强版户外环境检测
     */
    private fun detectEnvironment(lightLevel: Float): EnvironmentType {
        // 使用更敏感的环境检测逻辑，提高响应性
        return when {
            lightLevel < NIGHT_LIGHT_THRESHOLD_LUX -> {
                // 夜间环境：< 10 lux (降低阈值，更早识别夜间)
                EnvironmentType.NIGHT
            }
            lightLevel > OUTDOOR_LIGHT_THRESHOLD_LUX -> {
                // 户外环境：> 200 lux (降低阈值，更早识别户外)
                EnvironmentType.OUTDOOR
            }
            lightLevel > 80.0f -> {
                // 明亮室内环境：80-200 lux，也按户外处理 (降低阈值)
                EnvironmentType.OUTDOOR
            }
            else -> {
                // 室内环境：10-80 lux (调整范围)
                EnvironmentType.INDOOR
            }
        }
    }

    /**
     * 获取当前环境的传感器延迟 - 优化户外响应性
     */
    private fun getCurrentSensorDelay(): Long {
        return when {
            // 极暗环境使用较长延迟
            currentLightLevel < 1.0f -> ULTRA_DARK_SENSOR_DELAY
            // 户外环境使用较短延迟，提高响应性
            currentEnvironment == EnvironmentType.OUTDOOR -> OUTDOOR_SENSOR_DELAY
            // 夜间环境使用较长延迟
            currentEnvironment == EnvironmentType.NIGHT -> NIGHT_SENSOR_DELAY
            // 暗光环境使用较长延迟
            currentLightLevel < 10.0f -> NIGHT_SENSOR_DELAY
            // 明亮环境使用较短延迟
            currentLightLevel > 200.0f -> BASE_SENSOR_DELAY
            else -> BASE_SENSOR_DELAY
        }
    }

    /**
     * 获取当前环境的光照阈值 - 优化户外敏感度
     */
    private fun getCurrentLightThreshold(): Float {
        return when {
            // 极暗环境使用微小阈值
            currentLightLevel < 0.5f -> MICRO_LIGHT_THRESHOLD * 0.5f  // 0.75 lux
            currentLightLevel < 2.0f -> MICRO_LIGHT_THRESHOLD         // 1.5 lux
            // 户外环境使用较低阈值，提高响应性
            currentEnvironment == EnvironmentType.OUTDOOR -> OUTDOOR_LIGHT_THRESHOLD
            // 夜间环境使用较小阈值
            currentEnvironment == EnvironmentType.NIGHT -> NIGHT_LIGHT_THRESHOLD
            // 暗光环境使用较小阈值
            currentLightLevel < 20.0f -> NIGHT_LIGHT_THRESHOLD
            // 明亮环境使用较低阈值
            currentLightLevel > 200.0f -> BASE_LIGHT_THRESHOLD * 0.8f  // 4 lux
            else -> BASE_LIGHT_THRESHOLD
        }
    }

    /**
     * 数据平滑处理 - 优化户外环境响应性
     */
    private fun smoothLightLevel(rawLevel: Float): Float {
        // 添加新数据到历史记录
        lightLevelHistory.add(rawLevel)
        
        // 保持窗口大小
        if (lightLevelHistory.size > SMOOTHING_WINDOW_SIZE) {
            lightLevelHistory.removeAt(0)
        }
        
        // 如果历史数据不足，直接返回原值
        if (lightLevelHistory.size < 3) {
            Log.v(TAG, "历史数据不足，返回原值: ${rawLevel}")
            return rawLevel
        }
        
        // 户外环境使用更轻的平滑处理
        if (currentEnvironment == EnvironmentType.OUTDOOR) {
            // 户外环境：使用3点移动平均，减少平滑
            val recentValues = lightLevelHistory.takeLast(3)
            val outdoorAverage = recentValues.average().toFloat()
            
            // 户外环境异常值检测更宽松
            val deviation = if (outdoorAverage > 0.1f) {
                abs(rawLevel - outdoorAverage) / outdoorAverage
            } else {
                abs(rawLevel - outdoorAverage) / 0.1f
            }
            
            return if (deviation > 0.5f) {  // 50%变化率才认为是异常值
                // 异常值处理：使用加权平均
                outdoorAverage * 0.7f + rawLevel * 0.3f
            } else {
                // 正常值：使用加权平均，偏重当前值
                outdoorAverage * 0.4f + rawLevel * 0.6f
            }
        }
        
        // 室内和夜间环境使用原有平滑算法
        // 计算移动平均（去除最高最低值的三均值）
        val sortedHistory = lightLevelHistory.sorted()
        val trimmedAverage = when {
            sortedHistory.size >= 5 -> {
                // 去除最高最低值后计算平均
                sortedHistory.drop(1).dropLast(1).average().toFloat()
            }
            else -> {
                // 数据不足时使用普通平均
                sortedHistory.average().toFloat()
            }
        }
        
        // 选择合适的异常值阈值
        val outlierThreshold = when {
            currentLightLevel < 2.0f -> ULTRA_DARK_OUTLIER_THRESHOLD  // 极暗环境更严格
            currentLightLevel < 20.0f -> OUTLIER_THRESHOLD * 0.7f     // 暗光环境较严格
            else -> OUTLIER_THRESHOLD                                  // 标准阈值
        }
        
        // 异常值检测
        val deviation = if (trimmedAverage > 0.1f) {
            abs(rawLevel - trimmedAverage) / trimmedAverage
        } else {
            abs(rawLevel - trimmedAverage) / 0.1f  // 防止除零
        }
        
        val smoothedValue = if (deviation > outlierThreshold) {
            Log.v(TAG, "检测到异常值: 原值=${rawLevel}, 平均=${trimmedAverage}, 偏差=${(deviation*100).toInt()}%")
            // 使用渐进调整而非直接使用平均值
            val adjustmentFactor = 0.1f  // 10%的调整幅度
            trimmedAverage * (1 - adjustmentFactor) + rawLevel * adjustmentFactor
        } else {
            // 使用双重加权平均 - 历史平均 + 最近趋势
            val recentAverage = if (lightLevelHistory.size >= 3) {
                lightLevelHistory.takeLast(3).average().toFloat()
            } else {
                rawLevel
            }
            
            // 根据环境选择权重
            val (historyWeight, recentWeight, currentWeight) = when {
                currentLightLevel < 5.0f -> Triple(0.6f, 0.25f, 0.15f)   // 极暗环境偏重历史
                currentLightLevel > 500.0f -> Triple(0.3f, 0.4f, 0.3f)   // 户外环境偏重当前和最近
                else -> Triple(0.5f, 0.3f, 0.2f)                         // 标准环境平衡
            }
            
            trimmedAverage * historyWeight + recentAverage * recentWeight + rawLevel * currentWeight
        }
        
        Log.v(TAG, "平滑处理: 原值=${rawLevel} -> 平滑值=${smoothedValue} (偏差=${(deviation*100).toInt()}%)")
        
        return smoothedValue.coerceAtLeast(0.0f)
    }

    /**
     * 传感器数据发生变化时的回调 - 增强防闪频处理和智能健康检测
     */
    override fun onSensorChanged(event: SensorEvent?) {
        if (event?.sensor?.type != Sensor.TYPE_LIGHT) {
            return
        }

        val currentTime = System.currentTimeMillis()
        val newRawLightLevel = event.values[0]
        
        // 新增：更新数据计数器和健康状态
        dataUpdateCount++
        updateSensorHealth(currentTime)
        
        // 定期检测环境类型
        if (currentTime - lastEnvironmentCheckTime > ENVIRONMENT_CHECK_INTERVAL) {
            val newEnvironment = detectEnvironment(newRawLightLevel)
            if (newEnvironment != currentEnvironment) {
                Log.d(TAG, "环境类型变化: $currentEnvironment -> $newEnvironment")
                currentEnvironment = newEnvironment
            }
            lastEnvironmentCheckTime = currentTime
        }
        
        // 新增：定期进行响应性检查
        if (currentTime - lastResponsivenessCheck > RESPONSIVENESS_CHECK_INTERVAL) {
            performResponsivenessCheck(currentTime)
            lastResponsivenessCheck = currentTime
        }
        
        // 检测户外环境并启用快速响应模式
        val isOutdoorMode = OutdoorBrightnessEnhancer.detectAndEnableOutdoorMode(newRawLightLevel)

        // 获取当前环境的更新延迟（户外环境使用更短延迟）
        val currentDelay = if (isOutdoorMode) {
            OutdoorBrightnessEnhancer.getOutdoorResponseDelay()
        } else {
            getCurrentSensorDelay()
        }

        // 限制更新频率，避免过于频繁的调节
        if (currentTime - lastUpdateTime < currentDelay) {
            return
        }

        // 数据平滑处理
        val smoothedLevel = smoothLightLevel(newRawLightLevel)

        // 获取当前环境的光照阈值（户外环境使用更低阈值）
        val currentThreshold = if (isOutdoorMode) {
            OutdoorBrightnessEnhancer.getOutdoorBrightnessThreshold() * 100 // 转换为光照阈值
        } else {
            getCurrentLightThreshold()
        }

        // 只有光照变化超过阈值时才触发回调
        if (abs(smoothedLevel - smoothedLightLevel) > currentThreshold) {
            currentLightLevel = newRawLightLevel
            smoothedLightLevel = smoothedLevel
            lastUpdateTime = currentTime

            val modeText = if (isOutdoorMode) " (户外快速模式)" else ""
            Log.d(TAG, "光照强度变化: ${smoothedLevel} lux (环境: $currentEnvironment, 阈值: $currentThreshold, 健康状态: $sensorHealth)$modeText")

            // 触发亮度调节回调
            onLightChanged(smoothedLevel)
        }
    }

    /**
     * 传感器精度发生变化时的回调
     */
    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        when (accuracy) {
            SensorManager.SENSOR_STATUS_ACCURACY_HIGH -> 
                Log.d(TAG, "光传感器精度: 高")
            SensorManager.SENSOR_STATUS_ACCURACY_MEDIUM -> 
                Log.d(TAG, "光传感器精度: 中")
            SensorManager.SENSOR_STATUS_ACCURACY_LOW -> 
                Log.d(TAG, "光传感器精度: 低")
            SensorManager.SENSOR_STATUS_UNRELIABLE -> 
                Log.w(TAG, "光传感器精度: 不可靠")
        }
    }

    /**
     * 更新传感器健康状态
     */
    private fun updateSensorHealth(currentTime: Long) {
        val timeSinceStart = currentTime - sensorStartTime
        val timeSinceLastUpdate = currentTime - lastUpdateTime
        
        sensorHealth = when {
            timeSinceStart < 5000L -> SensorHealth.UNKNOWN  // 启动5秒内状态未定
            timeSinceLastUpdate > SENSOR_TIMEOUT_THRESHOLD -> SensorHealth.DEADLOCK  // 数据超时
            timeSinceLastUpdate > 10000L -> SensorHealth.DATA_STALE  // 数据过时
            timeSinceLastUpdate > 5000L -> SensorHealth.SLOW_RESPONSE  // 响应缓慢
            else -> SensorHealth.HEALTHY  // 健康状态
        }
    }

    /**
     * 执行传感器响应性检查
     */
    private fun performResponsivenessCheck(currentTime: Long) {
        val timeSinceLastUpdate = currentTime - lastUpdateTime
        val updateCountInterval = currentTime - lastUpdateCountCheck
        val updateRate = if (updateCountInterval > 0) {
            (dataUpdateCount * 1000.0 / updateCountInterval).toFloat()
        } else {
            0f
        }
        
        Log.d(TAG, "传感器响应性检查: 更新率=${updateRate}次/秒, 距上次更新=${timeSinceLastUpdate/1000}秒, 健康状态=$sensorHealth")
        
        // 如果检测到问题，尝试自动恢复
        when {
            timeSinceLastUpdate > SENSOR_TIMEOUT_THRESHOLD -> {
                Log.w(TAG, "检测到传感器假死状态，尝试自动恢复")
                attemptAutoRecovery("数据超时")
            }
            updateRate < 0.01f && updateCountInterval > 60000L -> {
                Log.w(TAG, "检测到传感器更新率过低，尝试自动恢复")
                attemptAutoRecovery("更新率过低")
            }
            sensorHealth == SensorHealth.DATA_STALE -> {
                Log.w(TAG, "检测到传感器数据过时，尝试轻度恢复")
                attemptLightRecovery()
            }
        }
        
        // 重置计数器
        dataUpdateCount = 0L
        lastUpdateCountCheck = currentTime
    }

    /**
     * 尝试自动恢复传感器
     */
    private fun attemptAutoRecovery(reason: String) {
        if (restartAttempts >= MAX_RESTART_ATTEMPTS) {
            Log.e(TAG, "传感器恢复尝试次数已达上限，标记为硬件错误")
            sensorHealth = SensorHealth.HARDWARE_ERROR
            return
        }
        
        restartAttempts++
        Log.w(TAG, "自动恢复传感器 (第${restartAttempts}次尝试, 原因: $reason)")
        
        Thread {
            try {
                Thread.sleep(RESTART_ATTEMPT_INTERVAL)
                forceRestartListening()
            } catch (e: Exception) {
                Log.e(TAG, "自动恢复异常: ${e.message}")
            }
        }.start()
    }

    /**
     * 尝试轻度恢复（不完全重启）
     */
    private fun attemptLightRecovery() {
        Log.d(TAG, "执行轻度恢复：清理历史数据")
        lightLevelHistory.clear()
        smoothedLightLevel = currentLightLevel
        lastUpdateTime = System.currentTimeMillis()
    }

    /**
     * 获取传感器健康状态
     */
    fun getSensorHealth(): SensorHealth {
        return sensorHealth
    }

    /**
     * 获取传感器健康报告
     */
    fun getHealthReport(): String {
        val currentTime = System.currentTimeMillis()
        val timeSinceStart = currentTime - sensorStartTime
        val timeSinceLastUpdate = currentTime - lastUpdateTime
        val updateCountInterval = currentTime - lastUpdateCountCheck
        val updateRate = if (updateCountInterval > 0) {
            (dataUpdateCount * 1000.0 / updateCountInterval).toFloat()
        } else {
            0f
        }
        
        return """
            📊 传感器健康报告
            
            • 健康状态：$sensorHealth
            • 运行时间：${timeSinceStart/1000}秒
            • 距上次更新：${timeSinceLastUpdate/1000}秒
            • 数据更新率：${"%.2f".format(updateRate)}次/秒
            • 重启尝试次数：$restartAttempts
            • 当前环境：$currentEnvironment
            • 光照强度：${"%.1f".format(smoothedLightLevel)} lux
            
            🔍 状态说明：
            • HEALTHY：传感器工作正常
            • SLOW_RESPONSE：响应较慢但可用
            • DATA_STALE：数据更新延迟
            • DEADLOCK：假死状态，需要重启
            • HARDWARE_ERROR：硬件故障
        """.trimIndent()
    }

    /**
     * 检查传感器是否需要恢复
     */
    fun needsRecovery(): Boolean {
        val currentTime = System.currentTimeMillis()
        val timeSinceLastUpdate = currentTime - lastUpdateTime
        
        return when {
            sensorHealth == SensorHealth.DEADLOCK -> true
            sensorHealth == SensorHealth.HARDWARE_ERROR -> true
            timeSinceLastUpdate > SENSOR_TIMEOUT_THRESHOLD -> true
            !isListening -> true
            else -> false
        }
    }

    /**
     * 获取传感器信息
     * 
     * @return 传感器信息字符串
     */
    fun getSensorInfo(): String {
        return if (lightSensor != null) {
            "传感器名称: ${lightSensor.name}\n" +
            "厂商: ${lightSensor.vendor}\n" +
            "版本: ${lightSensor.version}\n" +
            "最大值: ${lightSensor.maximumRange} lux\n" +
            "功耗: ${lightSensor.power} mA\n" +
            "当前环境: $currentEnvironment\n" +
            "健康状态: $sensorHealth\n" +
            "防闪频优化: 已启用\n" +
            "智能检测: 已启用"
        } else {
            "设备不支持光传感器"
        }
    }
} 