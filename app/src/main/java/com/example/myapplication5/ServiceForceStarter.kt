package com.example.myapplication5

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import java.util.concurrent.TimeUnit

/**
 * 服务强制启动器 - 确保后台服务稳定运行
 * 
 * 功能：
 * 1. 强制启动后台服务
 * 2. 检查服务运行状态
 * 3. 重启失败的服务
 * 4. 清理僵尸服务进程
 * 
 * <AUTHOR>
 * @since 2.5
 */
object ServiceForceStarter {
    
    private const val TAG = "ServiceForceStarter"
    
    /**
     * 强制启动后台服务
     */
    fun forceStartService(context: Context): Boolean {
        return try {
            Log.d(TAG, "开始强制启动后台服务")
            
            // 先停止可能存在的服务
            forceStopService(context)
            
            // 等待一秒确保服务完全停止
            Thread.sleep(1000)
            
            // 检查权限
            if (!hasRequiredPermissions(context)) {
                Log.e(TAG, "缺少必要权限，无法启动服务")
                return false
            }
            
            // 创建启动Intent
            val serviceIntent = Intent(context, EyeCareBackgroundService::class.java).apply {
                putExtra("eye_care_mode", "STANDARD")
                putExtra("auto_adjustment", true)
                putExtra("force_start", true)
                putExtra("start_time", System.currentTimeMillis())
            }
            
            // 启动服务
            val success = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
                true
            } else {
                context.startService(serviceIntent) != null
            }
            
            if (success) {
                Log.d(TAG, "服务启动命令已发送")
                
                // 等待服务启动
                waitForServiceStart(context)
                
                return true
            } else {
                Log.e(TAG, "服务启动失败")
                return false
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "强制启动服务异常: ${e.message}")
            false
        }
    }
    
    /**
     * 强制停止服务
     */
    fun forceStopService(context: Context): Boolean {
        return try {
            Log.d(TAG, "强制停止后台服务")
            
            val serviceIntent = Intent(context, EyeCareBackgroundService::class.java)
            context.stopService(serviceIntent)
            
            // 等待服务停止
            waitForServiceStop(context)
            
            Log.d(TAG, "服务停止命令已发送")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "强制停止服务异常: ${e.message}")
            false
        }
    }
    
    /**
     * 检查服务是否正在运行
     */
    fun isServiceRunning(context: Context): Boolean {
        return try {
            // 使用更现代的方法检查服务状态
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                            // Android 8.0+ 使用新的API
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            @Suppress("DEPRECATION")
            val runningServices = activityManager.getRunningServices(Integer.MAX_VALUE)
                
                for (service in runningServices) {
                    if (service.service.className == EyeCareBackgroundService::class.java.name) {
                        Log.d(TAG, "服务正在运行: ${service.service.className}")
                        return true
                    }
                }
            } else {
                // 兼容旧版本
                val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
                @Suppress("DEPRECATION")
                val runningServices = activityManager.getRunningServices(Integer.MAX_VALUE)
                
                for (service in runningServices) {
                    if (service.service.className == EyeCareBackgroundService::class.java.name) {
                        Log.d(TAG, "服务正在运行: ${service.service.className}")
                        return true
                    }
                }
            }
            
            Log.d(TAG, "服务未运行")
            false
            
        } catch (e: Exception) {
            Log.e(TAG, "检查服务状态异常: ${e.message}")
            false
        }
    }
    
    /**
     * 等待服务启动
     */
    private fun waitForServiceStart(context: Context): Boolean {
        return try {
            var attempts = 0
            val maxAttempts = 10
            
            while (attempts < maxAttempts) {
                if (isServiceRunning(context)) {
                    Log.d(TAG, "服务启动成功")
                    return true
                }
                
                Thread.sleep(500) // 等待500毫秒
                attempts++
                Log.d(TAG, "等待服务启动... 尝试 $attempts/$maxAttempts")
            }
            
            Log.w(TAG, "服务启动超时")
            false
            
        } catch (e: Exception) {
            Log.e(TAG, "等待服务启动异常: ${e.message}")
            false
        }
    }
    
    /**
     * 等待服务停止
     */
    private fun waitForServiceStop(context: Context): Boolean {
        return try {
            var attempts = 0
            val maxAttempts = 10
            
            while (attempts < maxAttempts) {
                if (!isServiceRunning(context)) {
                    Log.d(TAG, "服务停止成功")
                    return true
                }
                
                Thread.sleep(500) // 等待500毫秒
                attempts++
                Log.d(TAG, "等待服务停止... 尝试 $attempts/$maxAttempts")
            }
            
            Log.w(TAG, "服务停止超时")
            false
            
        } catch (e: Exception) {
            Log.e(TAG, "等待服务停止异常: ${e.message}")
            false
        }
    }
    
    /**
     * 检查必要权限
     */
    private fun hasRequiredPermissions(context: Context): Boolean {
        return try {
            val brightnessController = BrightnessController(context, null)
            val hasPermission = brightnessController.hasWriteSettingsPermission()
            
            if (!hasPermission) {
                Log.w(TAG, "缺少系统设置权限")
            }
            
            hasPermission
            
        } catch (e: Exception) {
            Log.e(TAG, "检查权限异常: ${e.message}")
            false
        }
    }
    
    /**
     * 重启服务
     */
    fun restartService(context: Context): Boolean {
        return try {
            Log.d(TAG, "重启后台服务")
            
            // 先停止服务
            forceStopService(context)
            
            // 等待2秒
            Thread.sleep(2000)
            
            // 重新启动服务
            forceStartService(context)
            
        } catch (e: Exception) {
            Log.e(TAG, "重启服务异常: ${e.message}")
            false
        }
    }
    
    /**
     * 获取服务详细信息
     */
    fun getServiceDetails(context: Context): String {
        return try {
            val isRunning = isServiceRunning(context)
            val hasPermission = hasRequiredPermissions(context)
            
            """
                系统服务状态: ${if (isRunning) "正在运行" else "未运行"}
                系统设置权限: ${if (hasPermission) "已授权" else "未授权"}
                服务健康状态: ${if (isRunning) "正常" else "异常"}
            """.trimIndent()
            
        } catch (e: Exception) {
            "获取服务详情失败: ${e.message}"
        }
    }
    
    /**
     * 清理僵尸服务
     */
    fun cleanupZombieServices(context: Context): Boolean {
        return try {
            Log.d(TAG, "清理僵尸服务")
            
            // 检查服务状态
            val isRunning = isServiceRunning(context)
            
            if (isRunning) {
                Log.w(TAG, "检测到服务正在运行，尝试停止")
                forceStopService(context)
                return true
            }
            
            Log.d(TAG, "无需清理")
            false
            
        } catch (e: Exception) {
            Log.e(TAG, "清理僵尸服务异常: ${e.message}")
            false
        }
    }
} 