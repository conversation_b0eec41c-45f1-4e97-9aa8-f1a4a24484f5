package com.example.myapplication5

import android.content.Context
import android.util.Log

/**
 * 护眼设置管理器 - 用于保存和恢复用户设置
 * 
 * 功能：
 * 1. 保存和恢复用户护眼设置
 * 2. 开机自启设置管理
 * 3. 护眼模式设置管理
 * 4. 自动调节设置管理
 * 5. 亮度偏移设置管理
 * 6. 主题设置管理
 * 
 * <AUTHOR>
 * @since 2.4
 */
object EyeCareSettingsManager {
    
    private const val TAG = "EyeCareSettings"
    private const val PREFS_NAME = "eye_care_settings"
    
    // 设置键名
    const val KEY_AUTO_START = "auto_start_enabled"
    const val KEY_EYE_CARE_MODE = "eye_care_mode"
    const val KEY_AUTO_ADJUSTMENT = "auto_adjustment_enabled"
    const val KEY_BRIGHTNESS_OFFSET = "brightness_offset"
    const val KEY_USE_GREEN_THEME = "use_green_theme"
    const val KEY_BACKGROUND_SERVICE = "background_service_enabled"
    const val KEY_BACKGROUND_MODE_CONFIGURED = "background_mode_configured"
    const val KEY_PERSONALIZED_BRIGHTNESS = "personalized_brightness_enabled"
    const val KEY_LEARNING_ENABLED = "learning_enabled"
    const val KEY_AUTO_APPLY_LEARNING_ENABLED = "auto_apply_learning_enabled"
    const val KEY_SYSTEM_BRIGHTNESS_MONITORING_ENABLED = "system_brightness_monitoring_enabled"
    const val KEY_NATIVE_BRIGHTNESS_COMPARISON_ENABLED = "native_brightness_comparison_enabled"
    
    /**
     * 保存护眼设置
     */
    fun saveSettings(
        context: Context,
        autoStartEnabled: Boolean = false,
        eyeCareMode: String = "STANDARD",
        autoAdjustment: Boolean = true,
        brightnessOffset: Float = 0.0f,
        useGreenTheme: Boolean = true,
        backgroundServiceEnabled: Boolean = false,
        backgroundModeConfigured: Boolean = false,
        personalizedBrightnessEnabled: Boolean = false,
        learningEnabled: Boolean = true,
        autoApplyLearningEnabled: Boolean = false,
        systemBrightnessMonitoringEnabled: Boolean = false,
        nativeBrightnessComparisonEnabled: Boolean = true
    ) {
        try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit().apply {
                putBoolean(KEY_AUTO_START, autoStartEnabled)
                putString(KEY_EYE_CARE_MODE, eyeCareMode)
                putBoolean(KEY_AUTO_ADJUSTMENT, autoAdjustment)
                putFloat(KEY_BRIGHTNESS_OFFSET, brightnessOffset)
                putBoolean(KEY_USE_GREEN_THEME, useGreenTheme)
                putBoolean(KEY_BACKGROUND_SERVICE, backgroundServiceEnabled)
                putBoolean(KEY_BACKGROUND_MODE_CONFIGURED, backgroundModeConfigured)
                putBoolean(KEY_PERSONALIZED_BRIGHTNESS, personalizedBrightnessEnabled)
                putBoolean(KEY_LEARNING_ENABLED, learningEnabled)
                putBoolean(KEY_AUTO_APPLY_LEARNING_ENABLED, autoApplyLearningEnabled)
                putBoolean(KEY_SYSTEM_BRIGHTNESS_MONITORING_ENABLED, systemBrightnessMonitoringEnabled)
                putBoolean(KEY_NATIVE_BRIGHTNESS_COMPARISON_ENABLED, nativeBrightnessComparisonEnabled)
                apply()
            }
            
            Log.d(TAG, "护眼设置已保存: 自启=$autoStartEnabled, 模式=$eyeCareMode, 自动调节=$autoAdjustment, 后台配置=$backgroundModeConfigured, 个性化=$personalizedBrightnessEnabled, 学习=$learningEnabled, 自动应用学习=$autoApplyLearningEnabled, 系统亮度监听=$systemBrightnessMonitoringEnabled, 原生亮度对比=$nativeBrightnessComparisonEnabled")
        } catch (e: Exception) {
            Log.e(TAG, "保存设置失败: ${e.message}")
        }
    }
    
    /**
     * 获取开机自启状态
     */
    fun isAutoStartEnabled(context: Context): Boolean {
        return try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.getBoolean(KEY_AUTO_START, false)
        } catch (e: Exception) {
            Log.e(TAG, "获取自启设置失败: ${e.message}")
            false
        }
    }
    
    /**
     * 获取护眼模式
     */
    fun getEyeCareMode(context: Context): String {
        return try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.getString(KEY_EYE_CARE_MODE, "STANDARD") ?: "STANDARD"
        } catch (e: Exception) {
            Log.e(TAG, "获取护眼模式失败: ${e.message}")
            "STANDARD"
        }
    }
    
    /**
     * 获取自动调节状态
     */
    fun isAutoAdjustmentEnabled(context: Context): Boolean {
        return try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.getBoolean(KEY_AUTO_ADJUSTMENT, true)
        } catch (e: Exception) {
            Log.e(TAG, "获取自动调节设置失败: ${e.message}")
            true
        }
    }
    
    /**
     * 获取亮度偏移
     */
    fun getBrightnessOffset(context: Context): Float {
        return try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.getFloat(KEY_BRIGHTNESS_OFFSET, 0.0f)
        } catch (e: Exception) {
            Log.e(TAG, "获取亮度偏移失败: ${e.message}")
            0.0f
        }
    }
    
    /**
     * 获取主题设置
     */
    fun isGreenThemeEnabled(context: Context): Boolean {
        return try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.getBoolean(KEY_USE_GREEN_THEME, true)
        } catch (e: Exception) {
            Log.e(TAG, "获取主题设置失败: ${e.message}")
            true
        }
    }
    
    /**
     * 获取后台服务状态
     */
    fun isBackgroundServiceEnabled(context: Context): Boolean {
        return try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.getBoolean(KEY_BACKGROUND_SERVICE, false)
        } catch (e: Exception) {
            Log.e(TAG, "获取后台服务设置失败: ${e.message}")
            false
        }
    }
    
    /**
     * 获取后台模式配置状态
     */
    fun isBackgroundModeConfigured(context: Context): Boolean {
        return try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.getBoolean(KEY_BACKGROUND_MODE_CONFIGURED, false)
        } catch (e: Exception) {
            Log.e(TAG, "获取后台模式配置状态失败: ${e.message}")
            false
        }
    }
    
    /**
     * 获取个性化亮度设置状态
     */
    fun isPersonalizedBrightnessEnabled(context: Context): Boolean {
        return try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.getBoolean(KEY_PERSONALIZED_BRIGHTNESS, false)
        } catch (e: Exception) {
            Log.e(TAG, "获取个性化亮度设置状态失败: ${e.message}")
            false
        }
    }
    
    /**
     * 获取智能学习开启状态
     */
    fun isLearningEnabled(context: Context): Boolean {
        return try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.getBoolean(KEY_LEARNING_ENABLED, true)
        } catch (e: Exception) {
            Log.e(TAG, "获取智能学习设置状态失败: ${e.message}")
            true
        }
    }
    
    /**
     * 获取自动应用学习结果状态
     */
    fun isAutoApplyLearningEnabled(context: Context): Boolean {
        return try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.getBoolean(KEY_AUTO_APPLY_LEARNING_ENABLED, false)
        } catch (e: Exception) {
            Log.e(TAG, "获取自动应用学习结果设置状态失败: ${e.message}")
            false
        }
    }
    
    /**
     * 设置后台模式配置状态
     */
    fun setBackgroundModeConfigured(context: Context, configured: Boolean) {
        try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit().apply {
                putBoolean(KEY_BACKGROUND_MODE_CONFIGURED, configured)
                apply()
            }
            Log.d(TAG, "后台模式配置状态已更新: $configured")
        } catch (e: Exception) {
            Log.e(TAG, "设置后台模式配置状态失败: ${e.message}")
        }
    }
    
    /**
     * 清除所有设置
     */
    fun clearSettings(context: Context) {
        try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit().clear().apply()
            Log.d(TAG, "所有设置已清除")
        } catch (e: Exception) {
            Log.e(TAG, "清除设置失败: ${e.message}")
        }
    }
    
    /**
     * 获取设置摘要
     */
    fun getSettingsSummary(context: Context): String {
        return try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val autoStart = prefs.getBoolean(KEY_AUTO_START, false)
            val mode = prefs.getString(KEY_EYE_CARE_MODE, "STANDARD") ?: "STANDARD"
            val autoAdjustment = prefs.getBoolean(KEY_AUTO_ADJUSTMENT, true)
            val offset = prefs.getFloat(KEY_BRIGHTNESS_OFFSET, 0.0f)
            val greenTheme = prefs.getBoolean(KEY_USE_GREEN_THEME, true)
            val backgroundService = prefs.getBoolean(KEY_BACKGROUND_SERVICE, false)
            val personalizedBrightness = prefs.getBoolean(KEY_PERSONALIZED_BRIGHTNESS, false)
            
            """
                开机自启: ${if (autoStart) "已启用" else "已禁用"}
                护眼模式: $mode
                自动调节: ${if (autoAdjustment) "已启用" else "已禁用"}
                亮度偏移: ${if (offset > 0) "+" else ""}${(offset * 100).toInt()}%
                主题色彩: ${if (greenTheme) "舒缓绿色" else "温暖橙色"}
                后台服务: ${if (backgroundService) "已启用" else "已禁用"}
                个性化亮度: ${if (personalizedBrightness) "已启用" else "已禁用"}
            """.trimIndent()
        } catch (e: Exception) {
            Log.e(TAG, "获取设置摘要失败: ${e.message}")
            "设置获取失败"
        }
    }
    
    /**
     * 获取系统亮度监听启用状态
     */
    fun isSystemBrightnessMonitoringEnabled(context: Context): Boolean {
        return try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.getBoolean(KEY_SYSTEM_BRIGHTNESS_MONITORING_ENABLED, false)
        } catch (e: Exception) {
            Log.e(TAG, "获取系统亮度监听状态失败: ${e.message}")
            false
        }
    }
    
    /**
     * 🆕 获取原生亮度对比启用状态
     */
    fun isNativeBrightnessComparisonEnabled(context: Context): Boolean {
        return try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.getBoolean(KEY_NATIVE_BRIGHTNESS_COMPARISON_ENABLED, true)
        } catch (e: Exception) {
            Log.e(TAG, "获取原生亮度对比状态失败: ${e.message}")
            true
        }
    }
} 