package com.example.myapplication5

import android.app.*
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.os.PowerManager
import android.util.Log
import androidx.core.app.NotificationCompat
import kotlinx.coroutines.*

/**
 * 护眼后台服务 - 专为干眼症患者设计
 * 
 * 功能特点：
 * 1. 持续监控环境光照并自动调节亮度
 * 2. 前台服务确保不被系统杀死
 * 3. 电量优化，智能休眠机制
 * 4. 支持开机自启和持久运行
 * 5. 提供服务状态通知
 * 6. 增强的后台权限处理
 * 7. 服务自动恢复机制
 * 
 * <AUTHOR>
 * @since 2.5 - 强化后台服务稳定性
 */
class EyeCareBackgroundService : Service() {
    
    companion object {
        private const val TAG = "EyeCareBackgroundService"
        const val NOTIFICATION_ID = 1001
        const val CHANNEL_ID = "eye_care_service_channel"
        const val ACTION_STOP_SERVICE = "com.example.myapplication5.STOP_SERVICE"
        const val ACTION_OPEN_APP = "com.example.myapplication5.OPEN_APP"
        
        /** 服务更新间隔 (毫秒) - 比前台更长，节省电量 */
        private const val SERVICE_UPDATE_INTERVAL = 2000L  // 缩短间隔，提高响应性
        /** 夜间模式下的更新间隔 (毫秒) - 进一步延长 */
        private const val NIGHT_UPDATE_INTERVAL = 3000L
        /** 屏幕关闭时的更新间隔 (毫秒) - 最长间隔 */
        private const val SCREEN_OFF_UPDATE_INTERVAL = 5000L
        
        /** 服务运行状态 */
        var isServiceRunning = false
            private set
    }
    
    private lateinit var brightnessController: BrightnessController
    private lateinit var lightSensorManager: LightSensorManager
    private lateinit var notificationManager: NotificationManager
    private lateinit var wakeLock: PowerManager.WakeLock
    
    /** 协程作用域 */
    private val serviceScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    /** 服务监控任务 */
    private var monitoringJob: Job? = null
    /** 亮度调节任务 */
    private var brightnessAdjustmentJob: Job? = null
    /** 服务恢复任务 */
    private var recoveryJob: Job? = null
    /** 传感器健康检查任务 */
    private var sensorHealthCheckJob: Job? = null
    
    /** 当前护眼模式 */
    private var currentEyeCareMode = BrightnessController.EyeCareMode.STANDARD
    /** 自动调节是否启用 */
    private var autoAdjustmentEnabled = true
    /** 当前亮度值 */
    private var currentBrightness = 0.25f
    /** 当前光照强度 */
    private var currentLightLevel = 0f
    /** 服务启动时间 */
    private var serviceStartTime = 0L
    /** 调节次数统计 */
    private var adjustmentCount = 0
    /** 上次调节时间 */
    private var lastAdjustmentTime = 0L
    /** 服务健康状态 */
    private var serviceHealthStatus = "正常"
    /** 传感器状态 */
    private var sensorStatus = "未初始化"
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "护眼后台服务创建")
        
        try {
            // 创建通知渠道
            createNotificationChannel()
            
            // 初始化通知管理器
            notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            
            // 初始化WakeLock - 使用更持久的锁
            val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
            wakeLock = powerManager.newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK,
                "EyeCare::BackgroundService"
            ).apply {
                setReferenceCounted(false)  // 防止重复获取
            }
            
            // 初始化亮度控制器（Service模式）
            try {
                brightnessController = BrightnessController(this, null)
                Log.d(TAG, "亮度控制器初始化成功")
            } catch (e: Exception) {
                Log.e(TAG, "亮度控制器初始化失败: ${e.message}")
                serviceHealthStatus = "亮度控制器初始化失败"
                return
            }
            
            // 初始化光传感器管理器
            lightSensorManager = LightSensorManager(this) { lightLevel ->
                currentLightLevel = lightLevel
                sensorStatus = "正常监听"
                
                if (autoAdjustmentEnabled) {
                    // 使用协程处理亮度调节，避免阻塞传感器回调
                    brightnessAdjustmentJob?.cancel()
                    brightnessAdjustmentJob = serviceScope.launch {
                        try {
                            val newBrightness = brightnessController.calculateBrightnessFromLight(lightLevel)

                            // 检查是否应该使用户外快速响应模式
                            val shouldUseOutdoorFastResponse = OutdoorBrightnessEnhancer.shouldImmediatelyAdjust(
                                lightLevel, currentBrightness, newBrightness
                            )

                            if (shouldUseOutdoorFastResponse) {
                                // 户外快速响应模式：立即调节
                                brightnessController.setBrightness(newBrightness)
                                currentBrightness = newBrightness
                                adjustmentCount++
                                lastAdjustmentTime = System.currentTimeMillis()

                                // 更新通知
                                updateNotification()

                                Log.d(TAG, "户外快速调节亮度: ${lightLevel}lux -> ${(newBrightness * 100).toInt()}%")
                            } else {
                                // 标准防频闪检查 - 大幅提高响应性
                                val timeSinceLastAdjustment = System.currentTimeMillis() - lastAdjustmentTime
                                val brightnessChange = kotlin.math.abs(newBrightness - currentBrightness)
                                val minTimeInterval = when {
                                    lightLevel < 5.0f -> 1000L      // 极暗环境：1秒间隔（大幅缩短）
                                    lightLevel > 500.0f -> 800L     // 户外环境：0.8秒间隔（大幅缩短）
                                    lightLevel > 200.0f -> 1000L    // 明亮环境：1秒间隔（大幅缩短）
                                    else -> 800L                    // 标准环境：0.8秒间隔（大幅缩短）
                                }
                                val minBrightnessChange = when {
                                    currentBrightness < 0.01f -> 0.001f  // 极低亮度：0.1%变化（降低阈值）
                                    currentBrightness < 0.10f -> 0.005f  // 低亮度：0.5%变化（降低阈值）
                                    lightLevel > 500.0f -> 0.01f         // 户外环境：1%变化（降低阈值）
                                    lightLevel > 200.0f -> 0.008f        // 明亮环境：0.8%变化（降低阈值）
                                    else -> 0.008f                       // 标准环境：0.8%变化（降低阈值）
                                }

                                if (timeSinceLastAdjustment > minTimeInterval ||
                                    brightnessChange > minBrightnessChange) {

                                    brightnessController.setBrightness(newBrightness)
                                    currentBrightness = newBrightness
                                    adjustmentCount++
                                    lastAdjustmentTime = System.currentTimeMillis()

                                    // 更新通知
                                    updateNotification()

                                    Log.d(TAG, "后台自动调节亮度: ${lightLevel}lux -> ${(newBrightness * 100).toInt()}%")
                                }
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "后台亮度调节失败: ${e.message}")
                            serviceHealthStatus = "亮度调节失败"
                        }
                    }
                }
            }
            
            serviceStartTime = System.currentTimeMillis()
            isServiceRunning = true
            serviceHealthStatus = "服务已启动"
            
            // 启用系统亮度监听器（如果学习功能已启用）
            try {
                if (BrightnessLearningManager.isLearningEnabled()) {
                    brightnessController.enableSystemBrightnessMonitoring(lightSensorManager)
                    Log.d(TAG, "系统亮度监听器已启动")
                }
            } catch (e: Exception) {
                Log.w(TAG, "启动系统亮度监听器失败: ${e.message}")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "服务创建失败: ${e.message}")
            serviceHealthStatus = "服务创建失败"
        }
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "护眼后台服务启动")
        
        try {
            // 解析启动参数
            intent?.let {
                val newMode = it.getStringExtra("eye_care_mode")
                val newAutoAdjustment = it.getBooleanExtra("auto_adjustment", autoAdjustmentEnabled)
                
                if (newMode != null) {
                    currentEyeCareMode = BrightnessController.EyeCareMode.valueOf(newMode)
                    Log.d(TAG, "模式更新: $currentEyeCareMode")
                }
                
                if (newAutoAdjustment != autoAdjustmentEnabled) {
                    autoAdjustmentEnabled = newAutoAdjustment
                    Log.d(TAG, "自动调节状态更新: $autoAdjustmentEnabled")
                }
                
                Log.d(TAG, "启动参数: 模式=$currentEyeCareMode, 自动调节=$autoAdjustmentEnabled")
            }
            
            // 立即应用新配置
            brightnessController.setEyeCareMode(currentEyeCareMode)
            
            // 启动前台服务
            startForeground(NOTIFICATION_ID, createNotification())
            
            // 获取WakeLock
            if (!wakeLock.isHeld) {
                try {
                    @Suppress("DEPRECATION")
                    wakeLock.acquire() // 无限超时，在onDestroy中释放
                    Log.d(TAG, "WakeLock获取成功")
                } catch (e: Exception) {
                    Log.e(TAG, "WakeLock获取失败: ${e.message}")
                }
            }
            
            // 启动监控任务
            startMonitoringTask()
            
            // 启动传感器监听
            startSensorListening()
            
            // 启动服务恢复任务
            startRecoveryTask()
            
            // 如果模式发生变化，重新启动传感器监听
            if (autoAdjustmentEnabled) {
                restartSensorListening()
            }
            
            // 启动传感器健康检查任务
            startSensorHealthCheckTask()
            
            // 返回START_STICKY确保服务被杀死后自动重启
            return START_STICKY
            
        } catch (e: Exception) {
            Log.e(TAG, "服务启动失败: ${e.message}")
            serviceHealthStatus = "服务启动失败"
            return START_NOT_STICKY
        }
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "护眼后台服务销毁")
        
        try {
            // 停止监控任务
            monitoringJob?.cancel()
            brightnessAdjustmentJob?.cancel()
            recoveryJob?.cancel()
            sensorHealthCheckJob?.cancel()
            serviceScope.cancel()
            
            // 停止传感器监听
            lightSensorManager.stopListening()
            
            // 停止系统亮度监听器
            try {
                brightnessController.disableSystemBrightnessMonitoring()
                Log.d(TAG, "系统亮度监听器已停止")
            } catch (e: Exception) {
                Log.w(TAG, "停止系统亮度监听器失败: ${e.message}")
            }
            
            // 释放WakeLock
            if (wakeLock.isHeld) {
                wakeLock.release()
                Log.d(TAG, "WakeLock已释放")
            }
            
            isServiceRunning = false
            serviceHealthStatus = "服务已停止"
            
        } catch (e: Exception) {
            Log.e(TAG, "服务销毁异常: ${e.message}")
        }
    }
    
    /**
     * 启动传感器监听 - 支持户外快速响应模式
     */
    private fun startSensorListening() {
        try {
            if (autoAdjustmentEnabled && lightSensorManager.isLightSensorAvailable()) {
                // 检查是否需要启用快速模式（基于当前光照强度）
                val fastMode = currentLightLevel > 1000.0f

                if (lightSensorManager.startListening(fastMode)) {
                    val modeText = if (fastMode) " (户外快速模式)" else ""
                    Log.d(TAG, "光传感器监听启动成功$modeText")
                    sensorStatus = "正常监听"
                } else {
                    Log.w(TAG, "光传感器监听启动失败")
                    sensorStatus = "监听失败"
                }
            } else {
                Log.w(TAG, "传感器不可用或自动调节已禁用")
                sensorStatus = "不可用"
            }
        } catch (e: Exception) {
            Log.e(TAG, "启动传感器监听失败: ${e.message}")
            sensorStatus = "启动失败"
        }
    }
    
    /**
     * 重新启动传感器监听 - 支持户外快速响应模式
     */
    private fun restartSensorListening() {
        try {
            Log.d(TAG, "重新启动传感器监听")

            // 检查是否需要启用快速模式
            val fastMode = currentLightLevel > 1000.0f || OutdoorBrightnessEnhancer.isInOutdoorMode()

            // 使用强制重启功能
            val success = lightSensorManager.forceRestartListening(fastMode)

            if (success) {
                val modeText = if (fastMode) " (户外快速模式)" else ""
                Log.d(TAG, "传感器监听重新启动成功$modeText")
                sensorStatus = "正常监听"
            } else {
                Log.w(TAG, "传感器监听重新启动失败")
                sensorStatus = "重启失败"

                // 如果强制重启失败，尝试延迟重启
                serviceScope.launch {
                    delay(2000) // 等待2秒
                    val retrySuccess = lightSensorManager.forceRestartListening(fastMode)
                    if (retrySuccess) {
                        val retryModeText = if (fastMode) " (户外快速模式)" else ""
                        Log.d(TAG, "传感器监听延迟重启成功$retryModeText")
                        sensorStatus = "正常监听"
                    } else {
                        Log.e(TAG, "传感器监听延迟重启也失败")
                        sensorStatus = "重启失败"
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "重新启动传感器监听失败: ${e.message}")
            sensorStatus = "重启异常"
        }
    }
    
    /**
     * 启动服务恢复任务
     */
    private fun startRecoveryTask() {
        recoveryJob = serviceScope.launch {
            while (isActive) {
                try {
                    delay(15000) // 缩短到15秒检查一次，提高响应性
                    
                    // 检查传感器状态
                    if (autoAdjustmentEnabled) {
                        val sensorAvailable = lightSensorManager.isLightSensorAvailable()
                        val sensorListening = lightSensorManager.isListening()
                        
                        if (!sensorAvailable) {
                            Log.w(TAG, "传感器不可用，尝试重新初始化")
                            startSensorListening()
                        } else if (!sensorListening) {
                            Log.w(TAG, "传感器未在监听，尝试重新启动")
                            restartSensorListening()
                        } else {
                            // 使用新的智能检测功能
                            if (lightSensorManager.needsRecovery()) {
                                val sensorHealth = lightSensorManager.getSensorHealth()
                                Log.w(TAG, "传感器需要恢复，健康状态: $sensorHealth")
                                restartSensorListening()
                            }
                            
                            // 传统的超时检测作为备用
                            val lastUpdateTime = lightSensorManager.getLastUpdateTime()
                            val currentTime = System.currentTimeMillis()
                            val timeSinceLastUpdate = currentTime - lastUpdateTime
                            
                            // 如果超过30秒没有数据更新，认为传感器失效
                            if (timeSinceLastUpdate > 30000) {
                                Log.w(TAG, "传感器数据更新超时，尝试重新启动")
                                restartSensorListening()
                            }
                        }
                    }
                    
                    // 检查权限状态
                    if (!brightnessController.hasWriteSettingsPermission()) {
                        Log.w(TAG, "缺少系统设置权限")
                        serviceHealthStatus = "缺少权限"
                    } else {
                        serviceHealthStatus = "正常"
                    }
                    
                    // 检查服务是否还在运行
                    if (!isServiceRunning) {
                        Log.w(TAG, "检测到服务状态异常，尝试恢复")
                        isServiceRunning = true
                    }
                    
                    // 检查WakeLock状态
                    if (!wakeLock.isHeld) {
                        Log.w(TAG, "WakeLock丢失，尝试重新获取")
                        try {
                            @Suppress("DEPRECATION")
                            wakeLock.acquire()
                            Log.d(TAG, "WakeLock重新获取成功")
                        } catch (e: Exception) {
                            Log.e(TAG, "WakeLock重新获取失败: ${e.message}")
                        }
                    }
                    
                } catch (e: Exception) {
                    Log.e(TAG, "恢复任务异常: ${e.message}")
                }
            }
        }
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "💚 护眼保护服务（重要）",
                NotificationManager.IMPORTANCE_DEFAULT  // 提高优先级
            ).apply {
                description = "护眼光感调节后台服务 - 为干眼症患者提供持续保护，请勿清理此通知"
                setShowBadge(true)  // 显示角标
                enableLights(false)
                enableVibration(false)
                setSound(null, null)
                // 设置为重要通知，不允许用户完全关闭
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    setAllowBubbles(false)
                }
                lockscreenVisibility = Notification.VISIBILITY_PUBLIC
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    /**
     * 创建防清理服务通知
     */
    private fun createNotification(): Notification {
        // 打开应用的Intent
        val openAppIntent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        val openAppPendingIntent = PendingIntent.getActivity(
            this, 0, openAppIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        // 停止服务的Intent
        val stopServiceIntent = Intent(this, NotificationReceiver::class.java).apply {
            action = ACTION_STOP_SERVICE
        }
        val stopServicePendingIntent = PendingIntent.getBroadcast(
            this, 1, stopServiceIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("🛡️ 护眼保护运行中（请勿清理）")
            .setContentText(getNotificationText())
            .setSmallIcon(android.R.drawable.star_on)  // 使用星形图标更显眼
            .setColor(0xFF2E7D32.toInt()) // 深绿色更显眼
            .setOngoing(true)  // 标记为持续通知
            .setAutoCancel(false)  // 禁止滑动删除
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)  // 提高优先级
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)  // 锁屏可见
            .setShowWhen(true)  // 显示时间
            .setUsesChronometer(false)
            .setContentIntent(openAppPendingIntent)
            .addAction(
                android.R.drawable.ic_dialog_info,
                "打开设置",
                openAppPendingIntent
            )
            .addAction(
                android.R.drawable.ic_media_pause,
                "停止保护",
                stopServicePendingIntent
            )
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText(getDetailedNotificationText())
                .setBigContentTitle("🛡️ 护眼保护运行中（请勿清理）")
                .setSummaryText("⚠️ 清理此应用将停止护眼保护"))
            .build()
    }
    
    /**
     * 更新通知内容
     */
    private fun updateNotification() {
        try {
            val notification = createNotification()
            notificationManager.notify(NOTIFICATION_ID, notification)
        } catch (e: Exception) {
            Log.w(TAG, "通知更新失败: ${e.message}")
        }
    }
    
    /**
     * 获取通知文本
     */
    private fun getNotificationText(): String {
        val mode = when (currentEyeCareMode) {
            BrightnessController.EyeCareMode.STANDARD -> "标准"
            BrightnessController.EyeCareMode.NIGHT -> "夜间"
            BrightnessController.EyeCareMode.ULTRA_SENSITIVE -> "超敏感"
        }
        
        return if (autoAdjustmentEnabled) {
            "💚 $mode 模式 · 亮度 ${(currentBrightness * 100).toInt()}% · 环境光 ${currentLightLevel.toInt()}lux"
        } else {
            "💚 $mode 模式 · 手动亮度 ${(currentBrightness * 100).toInt()}% · 护眼中"
        }
    }
    
    /**
     * 获取详细通知文本
     */
    private fun getDetailedNotificationText(): String {
        val runningTime = (System.currentTimeMillis() - serviceStartTime) / 1000 / 60 // 分钟
        val mode = when (currentEyeCareMode) {
            BrightnessController.EyeCareMode.STANDARD -> "标准护眼"
            BrightnessController.EyeCareMode.NIGHT -> "夜间护眼"
            BrightnessController.EyeCareMode.ULTRA_SENSITIVE -> "超敏感护眼"
        }
        
        val statusText = if (autoAdjustmentEnabled) {
            "自动调节模式：根据环境光线智能调节亮度"
        } else {
            "手动调节模式：使用固定护眼亮度"
        }
        
        val protectionStatus = when {
            currentBrightness <= 0.01f -> "🌙 深夜完美护眼"
            currentBrightness <= 0.03f -> "🌒 极佳护眼保护"
            currentBrightness <= 0.06f -> "🌓 优秀护眼保护"
            currentBrightness <= 0.12f -> "🌔 良好护眼保护"
            else -> "🌕 基础护眼保护"
        }
        
        val warningText = "⚠️ 重要提醒：\n• 请勿在后台清理中删除此应用\n• 清理后护眼保护将停止工作\n• 可能导致眼部不适和疲劳加重"
        
        // 获取户外快速响应状态
        val outdoorStatus = if (OutdoorBrightnessEnhancer.isInOutdoorMode()) {
            "🌞 户外快速响应已启用"
        } else {
            "🏠 室内标准模式"
        }

        return """
            $mode 正在为您的眼部健康保驾护航

            📊 当前状态：
            • 护眼等级：$protectionStatus
            • 屏幕亮度：${(currentBrightness * 100).toInt()}%
            • 环境光照：${currentLightLevel.toInt()} lux
            • 运行模式：$statusText
            • 响应模式：$outdoorStatus
            • 持续保护：${runningTime}分钟

            $warningText
        """.trimIndent()
    }
    
    /**
     * 启动监控任务
     */
    private fun startMonitoringTask() {
        monitoringJob = serviceScope.launch {
            while (isActive) {
                try {
                    // 检查当前时间，判断是否需要调整更新间隔
                    val updateInterval = getUpdateInterval()
                    
                    // 定期更新通知
                    updateNotification()
                    
                    // 检查服务健康状态
                    checkServiceHealth()
                    
                    // 定期检查亮度状态
                    checkBrightnessStatus()
                    
                    delay(updateInterval)
                } catch (e: Exception) {
                    Log.e(TAG, "监控任务异常: ${e.message}")
                    delay(SERVICE_UPDATE_INTERVAL)
                }
            }
        }
    }
    
    /**
     * 获取更新间隔
     */
    private fun getUpdateInterval(): Long {
        val currentHour = java.util.Calendar.getInstance().get(java.util.Calendar.HOUR_OF_DAY)
        
        return when {
            // 夜间时段使用更长间隔
            currentHour < 6 || currentHour >= 23 -> NIGHT_UPDATE_INTERVAL
            // 标准时段
            else -> SERVICE_UPDATE_INTERVAL
        }
    }
    
    /**
     * 检查服务健康状态
     */
    private fun checkServiceHealth() {
        try {
            // 检查传感器状态
            if (autoAdjustmentEnabled) {
                val sensorAvailable = lightSensorManager.isLightSensorAvailable()
                val sensorListening = lightSensorManager.isListening()
                
                if (!sensorAvailable) {
                    Log.w(TAG, "光传感器不可用，切换到手动模式")
                    autoAdjustmentEnabled = false
                    sensorStatus = "不可用"
                } else if (!sensorListening) {
                    Log.w(TAG, "光传感器未在监听，尝试重新启动")
                    restartSensorListening()
                } else {
                    // 检查传感器数据是否正常更新
                    val lastUpdateTime = lightSensorManager.getLastUpdateTime()
                    val currentTime = System.currentTimeMillis()
                    val timeSinceLastUpdate = currentTime - lastUpdateTime
                    
                    // 如果超过30秒没有数据更新，认为传感器失效
                    if (timeSinceLastUpdate > 30000) {
                        Log.w(TAG, "传感器数据更新超时，尝试重新启动")
                        restartSensorListening()
                    }
                }
            }
            
            // 检查权限状态
            if (!brightnessController.hasWriteSettingsPermission()) {
                Log.w(TAG, "缺少系统设置权限，某些功能可能受限")
                serviceHealthStatus = "缺少权限"
            } else {
                serviceHealthStatus = "正常"
            }
            
            // 更新当前亮度
            currentBrightness = brightnessController.getCurrentBrightness()
            
        } catch (e: Exception) {
            Log.w(TAG, "服务健康检查异常: ${e.message}")
            serviceHealthStatus = "检查异常"
        }
    }
    
    /**
     * 检查亮度状态
     */
    private fun checkBrightnessStatus() {
        try {
            // 检查系统亮度模式
            if (brightnessController.isSystemAutoBrightnessEnabled()) {
                Log.w(TAG, "检测到系统自动亮度开启，可能影响护眼调节效果")
            }
            
            // 检查亮度冲突
            if (brightnessController.hasBrightnessConflict()) {
                Log.w(TAG, "检测到亮度冲突，建议用户关闭系统自动亮度")
            }
            
        } catch (e: Exception) {
            Log.w(TAG, "亮度状态检查异常: ${e.message}")
        }
    }
    
    /**
     * 停止服务
     */
    fun stopService() {
        Log.d(TAG, "收到停止服务请求")
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            stopForeground(STOP_FOREGROUND_REMOVE)
        } else {
            @Suppress("DEPRECATION")
            stopForeground(true)
        }
        stopSelf()
    }
    
    /**
     * 更新服务配置
     */
    fun updateServiceConfig(
        eyeCareMode: BrightnessController.EyeCareMode,
        autoAdjustment: Boolean
    ) {
        val modeChanged = currentEyeCareMode != eyeCareMode
        val autoAdjustmentChanged = autoAdjustmentEnabled != autoAdjustment
        
        currentEyeCareMode = eyeCareMode
        autoAdjustmentEnabled = autoAdjustment
        
        brightnessController.setEyeCareMode(eyeCareMode)
        
        // 如果模式发生变化，重新启动传感器监听
        if (modeChanged && autoAdjustment) {
            Log.d(TAG, "模式发生变化，重新启动传感器监听")
            restartSensorListening()
        } else if (autoAdjustment && !lightSensorManager.isLightSensorAvailable()) {
            Log.w(TAG, "尝试启用自动调节，但传感器不可用")
        } else if (autoAdjustment) {
            lightSensorManager.startListening()
        } else {
            lightSensorManager.stopListening()
        }
        
        updateNotification()
        Log.d(TAG, "服务配置已更新: 模式=$eyeCareMode, 自动调节=$autoAdjustment, 模式变化=$modeChanged")
    }

    /**
     * 启动传感器健康检查任务
     */
    private fun startSensorHealthCheckTask() {
        sensorHealthCheckJob = serviceScope.launch {
            while (isActive) {
                try {
                    delay(10000) // 每10秒检查一次传感器健康状态
                    
                    if (autoAdjustmentEnabled) {
                        // 检查传感器是否正常工作
                        val sensorAvailable = lightSensorManager.isLightSensorAvailable()
                        val sensorListening = lightSensorManager.isListening()
                        val lastUpdateTime = lightSensorManager.getLastUpdateTime()
                        val currentTime = System.currentTimeMillis()
                        val timeSinceLastUpdate = currentTime - lastUpdateTime
                        
                        // 传感器健康状态评估
                        val sensorHealth = when {
                            !sensorAvailable -> "不可用"
                            !sensorListening -> "未监听"
                            timeSinceLastUpdate > 60000 -> "数据超时" // 1分钟无数据
                            timeSinceLastUpdate > 30000 -> "数据延迟" // 30秒无数据
                            else -> "正常"
                        }
                        
                        // 如果传感器不健康，尝试恢复
                        if (sensorHealth != "正常") {
                            Log.w(TAG, "传感器健康检查发现问题: $sensorHealth")
                            
                            when (sensorHealth) {
                                "不可用" -> {
                                    Log.w(TAG, "传感器不可用，尝试重新初始化")
                                    startSensorListening()
                                }
                                "未监听", "数据超时" -> {
                                    Log.w(TAG, "传感器监听异常，强制重启")
                                    lightSensorManager.forceRestartListening()
                                }
                                "数据延迟" -> {
                                    Log.w(TAG, "传感器数据延迟，尝试重启")
                                    restartSensorListening()
                                }
                            }
                            
                            // 更新传感器状态
                            sensorStatus = "恢复中"
                        } else {
                            sensorStatus = "正常监听"
                        }
                        
                        Log.v(TAG, "传感器健康检查: $sensorHealth, 最后更新: ${timeSinceLastUpdate/1000}秒前")
                    }
                    
                } catch (e: Exception) {
                    Log.e(TAG, "传感器健康检查异常: ${e.message}")
                }
            }
        }
    }
} 