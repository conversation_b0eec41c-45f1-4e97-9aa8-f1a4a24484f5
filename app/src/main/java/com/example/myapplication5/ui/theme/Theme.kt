package com.example.myapplication5.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

/**
 * 绿色护眼色彩方案 - 最舒缓眼部的颜色
 * 特点：绿色系深色背景 + 柔和绿色调 + 极低对比度
 */
private val GreenEyeCareColorScheme = darkColorScheme(
    // 主色调 - 柔和的绿色
    primary = EyeCareGreen400,
    onPrimary = EyeCareGreenDark900,
    primaryContainer = EyeCareGreen600,
    onPrimaryContainer = EyeCareGreenTextPrimary,
    
    // 次要色调 - 青绿色
    secondary = EyeCareTeal300,
    onSecondary = EyeCareGreenDark900,
    secondaryContainer = EyeCareTeal600,
    onSecondaryContainer = EyeCareGreenTextPrimary,
    
    // 第三色调 - 温和绿色
    tertiary = EyeCareGreen200,
    onTertiary = EyeCareGreenDark900,
    tertiaryContainer = EyeCareGreen700,
    onTertiaryContainer = EyeCareGreenTextPrimary,
    
    // 背景色 - 深绿色护眼
    background = EyeCareGreenDark900,
    onBackground = EyeCareGreenTextPrimary,
    surface = EyeCareGreenDark800,
    onSurface = EyeCareGreenTextPrimary,
    surfaceVariant = EyeCareGreenDark700,
    onSurfaceVariant = EyeCareGreenTextSecondary,
    
    // 边框和分割线
    outline = EyeCareGreenDark600,
    outlineVariant = EyeCareGreenDark500,
    
    // 错误状态 - 柔和红色
    error = EyeCareError,
    onError = EyeCareGreenTextPrimary,
    errorContainer = Color(0xFF4D2C2C),
    onErrorContainer = EyeCareError,
    
    // 表面色调变化
    surfaceTint = EyeCareGreen400,
    inverseSurface = EyeCareGreenTextPrimary,
    inverseOnSurface = EyeCareGreenDark900,
    inversePrimary = EyeCareGreenDark900,
    
    // 尺寸和容器
    surfaceDim = EyeCareGreenDark800,
    surfaceBright = EyeCareGreenDark700,
    surfaceContainerLowest = EyeCareGreenDark900,
    surfaceContainerLow = EyeCareGreenDark800,
    surfaceContainer = EyeCareGreenDark700,
    surfaceContainerHigh = EyeCareGreenDark600,
    surfaceContainerHighest = EyeCareGreenDark500
)

/**
 * 护眼色彩方案 - 常规护眼模式（暖色调）
 * 特点：深色背景 + 暖色调 + 低对比度
 */
private val WarmEyeCareColorScheme = darkColorScheme(
    // 主色调 - 温和的琥珀色
    primary = EyeCareAmber500,
    onPrimary = EyeCareDark900,
    primaryContainer = EyeCareAmber700,
    onPrimaryContainer = EyeCareTextPrimary,
    
    // 次要色调 - 柔和橙色
    secondary = EyeCareOrange300,
    onSecondary = EyeCareDark900,
    secondaryContainer = EyeCareOrange700,
    onSecondaryContainer = EyeCareTextPrimary,
    
    // 第三色调 - 温和强调色
    tertiary = EyeCareAmber200,
    onTertiary = EyeCareDark900,
    tertiaryContainer = EyeCareAmber900,
    onTertiaryContainer = EyeCareTextPrimary,
    
    // 背景色 - 深色护眼
    background = EyeCareDark900,
    onBackground = EyeCareTextPrimary,
    surface = EyeCareDark800,
    onSurface = EyeCareTextPrimary,
    surfaceVariant = EyeCareDark700,
    onSurfaceVariant = EyeCareTextSecondary,
    
    // 边框和分割线
    outline = EyeCareDark600,
    outlineVariant = EyeCareDark500,
    
    // 错误状态 - 柔和红色
    error = EyeCareError,
    onError = EyeCareTextPrimary,
    errorContainer = Color(0xFF4D2C2C),
    onErrorContainer = EyeCareError,
    
    // 表面色调变化
    surfaceTint = EyeCareAmber500,
    inverseSurface = EyeCareTextPrimary,
    inverseOnSurface = EyeCareDark900,
    inversePrimary = EyeCareDark900,
    
    // 尺寸和容器
    surfaceDim = EyeCareDark800,
    surfaceBright = EyeCareDark700,
    surfaceContainerLowest = EyeCareDark900,
    surfaceContainerLow = EyeCareDark800,
    surfaceContainer = EyeCareDark700,
    surfaceContainerHigh = EyeCareDark600,
    surfaceContainerHighest = EyeCareDark500
)

/**
 * 夜间红光护眼模式
 * 特点：红光滤镜 + 极低亮度 + 几乎无蓝光
 */
private val NightModeColorScheme = darkColorScheme(
    // 主色调 - 深红色系
    primary = NightModeRed300,
    onPrimary = Color(0xFF1A0000),
    primaryContainer = NightModeRed700,
    onPrimaryContainer = NightModeRed100,
    
    // 次要色调 - 暖红色
    secondary = NightModeRed100,
    onSecondary = Color(0xFF1A0000),
    secondaryContainer = NightModeRed500,
    onSecondaryContainer = NightModeRed50,
    
    // 第三色调
    tertiary = NightModeRed100,
    onTertiary = Color(0xFF1A0000),
    tertiaryContainer = NightModeRed900,
    onTertiaryContainer = NightModeRed50,
    
    // 背景色 - 极深红黑
    background = Color(0xFF0A0000),
    onBackground = Color(0xFF4D1A1A),
    surface = Color(0xFF1A0000),
    onSurface = Color(0xFF4D1A1A),
    surfaceVariant = Color(0xFF2D0A0A),
    onSurfaceVariant = Color(0xFF664444),
    
    // 边框和分割线
    outline = Color(0xFF4D1A1A),
    outlineVariant = Color(0xFF332222),
    
    // 错误状态
    error = NightModeRed300,
    onError = Color(0xFF1A0000),
    errorContainer = Color(0xFF4D0000),
    onErrorContainer = NightModeRed100,
    
    // 其他色调
    surfaceTint = NightModeRed700,
    inverseSurface = Color(0xFF4D1A1A),
    inverseOnSurface = Color(0xFF0A0000),
    inversePrimary = Color(0xFF1A0000)
)

/**
 * 夜间绿光护眼模式
 * 特点：绿光滤镜 + 极低亮度 + 减少刺激
 */
private val NightGreenModeColorScheme = darkColorScheme(
    // 主色调 - 深夜绿色系
    primary = NightModeGreen300,
    onPrimary = Color(0xFF0A1A0A),
    primaryContainer = NightModeGreen700,
    onPrimaryContainer = NightModeGreen100,
    
    // 次要色调 - 暖夜绿色
    secondary = NightModeGreen100,
    onSecondary = Color(0xFF0A1A0A),
    secondaryContainer = NightModeGreen500,
    onSecondaryContainer = NightModeGreen50,
    
    // 第三色调
    tertiary = NightModeGreen100,
    onTertiary = Color(0xFF0A1A0A),
    tertiaryContainer = NightModeGreen900,
    onTertiaryContainer = NightModeGreen50,
    
    // 背景色 - 极深绿黑
    background = Color(0xFF050A05),
    onBackground = Color(0xFF1A4D1A),
    surface = Color(0xFF0A1A0A),
    onSurface = Color(0xFF1A4D1A),
    surfaceVariant = Color(0xFF0F2D0F),
    onSurfaceVariant = Color(0xFF446644),
    
    // 边框和分割线
    outline = Color(0xFF1A4D1A),
    outlineVariant = Color(0xFF223322),
    
    // 错误状态
    error = NightModeGreen300,
    onError = Color(0xFF0A1A0A),
    errorContainer = Color(0xFF004D00),
    onErrorContainer = NightModeGreen100,
    
    // 其他色调
    surfaceTint = NightModeGreen700,
    inverseSurface = Color(0xFF1A4D1A),
    inverseOnSurface = Color(0xFF050A05),
    inversePrimary = Color(0xFF0A1A0A)
)

/**
 * 高对比度护眼模式（适合视力更弱的用户）
 */
private val HighContrastEyeCareScheme = darkColorScheme(
    primary = EyeCareGreen200,
    onPrimary = Color(0xFF000000),
    primaryContainer = EyeCareGreen700,
    onPrimaryContainer = Color(0xFFFFFFFF),
    
    secondary = EyeCareTeal100,
    onSecondary = Color(0xFF000000),
    secondaryContainer = EyeCareTeal700,
    onSecondaryContainer = Color(0xFFFFFFFF),
    
    background = Color(0xFF000000),
    onBackground = Color(0xFFE0FFE0),
    surface = Color(0xFF111111),
    onSurface = Color(0xFFE0FFE0),
    
    outline = EyeCareGreen500,
    error = Color(0xFFFF6B6B),
    onError = Color(0xFF000000)
)

/**
 * 护眼主题枚举
 */
enum class EyeCareThemeMode {
    GREEN_STANDARD,     // 绿色标准护眼模式（默认）
    WARM_STANDARD,      // 暖色调标准护眼模式
    NIGHT_RED,          // 夜间红光模式
    NIGHT_GREEN,        // 夜间绿光模式
    HIGH_CONTRAST       // 高对比度模式
}

/**
 * 护眼主题组件
 * 
 * @param eyeCareMode 护眼模式类型
 * @param content 内容组件
 */
@Composable
fun EyeCareTheme(
    eyeCareMode: EyeCareThemeMode = EyeCareThemeMode.GREEN_STANDARD,
    content: @Composable () -> Unit
) {
    val colorScheme = when (eyeCareMode) {
        EyeCareThemeMode.GREEN_STANDARD -> GreenEyeCareColorScheme
        EyeCareThemeMode.WARM_STANDARD -> WarmEyeCareColorScheme
        EyeCareThemeMode.NIGHT_RED -> NightModeColorScheme
        EyeCareThemeMode.NIGHT_GREEN -> NightGreenModeColorScheme
        EyeCareThemeMode.HIGH_CONTRAST -> HighContrastEyeCareScheme
    }
    
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            @Suppress("DEPRECATION")
            window.statusBarColor = colorScheme.background.toArgb()
            @Suppress("DEPRECATION")
            window.navigationBarColor = colorScheme.background.toArgb()
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = false
            WindowCompat.getInsetsController(window, view).isAppearanceLightNavigationBars = false
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = EyeCareTypography,
        content = content
    )
}

/**
 * 传统主题（保持兼容性）
 */
@Composable
fun MyApplication5Theme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    dynamicColor: Boolean = false, // 禁用动态颜色以确保护眼效果
    content: @Composable () -> Unit
) {
    // 默认使用绿色护眼主题
    EyeCareTheme(
        eyeCareMode = EyeCareThemeMode.GREEN_STANDARD,
        content = content
    )
}