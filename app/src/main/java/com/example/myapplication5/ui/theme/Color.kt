package com.example.myapplication5.ui.theme

import androidx.compose.ui.graphics.Color

/**
 * 护眼色彩方案 - 专为干眼症患者设计
 * 特点：
 * 1. 温和的暖色调，减少蓝光刺激
 * 2. 低对比度设计，减少视觉疲劳
 * 3. 深色背景为主，降低整体亮度
 * 4. 柔和的强调色，避免突兀感
 */

// === 护眼绿色系 (最舒缓眼部的颜色) ===
val EyeCareGreen50 = Color(0xFFE8F5E8)        // 极浅绿 - 最亮背景
val EyeCareGreen100 = Color(0xFFC8E6C9)       // 浅绿 - 卡片背景
val EyeCareGreen200 = Color(0xFFA5D6A7)       // 中浅绿
val EyeCareGreen300 = Color(0xFF81C784)       // 中绿
val EyeCareGreen400 = Color(0xFF66BB6A)       // 标准绿 - 主色调
val EyeCareGreen500 = Color(0xFF4CAF50)       // 深绿 - 按钮色
val EyeCareGreen600 = Color(0xFF43A047)       // 更深绿
val EyeCareGreen700 = Color(0xFF388E3C)       // 极深绿 - 强调色
val EyeCareGreen800 = Color(0xFF2E7D32)       // 深墨绿
val EyeCareGreen900 = Color(0xFF1B5E20)       // 极深墨绿

// === 护眼青绿色系 (更柔和的绿色调) ===
val EyeCareTeal50 = Color(0xFFE0F2F1)         // 极浅青绿
val EyeCareTeal100 = Color(0xFFB2DFDB)        // 浅青绿
val EyeCareTeal200 = Color(0xFF80CBC4)        // 中浅青绿
val EyeCareTeal300 = Color(0xFF4DB6AC)        // 中青绿
val EyeCareTeal400 = Color(0xFF26A69A)        // 标准青绿
val EyeCareTeal500 = Color(0xFF009688)        // 深青绿
val EyeCareTeal600 = Color(0xFF00897B)        // 更深青绿
val EyeCareTeal700 = Color(0xFF00796B)        // 极深青绿

// === 护眼主色调 (温和的琥珀色系 - 保留选项) ===
val EyeCareAmber50 = Color(0xFFFFF8E1)      // 极浅琥珀 - 最亮背景
val EyeCareAmber100 = Color(0xFFFFECB3)     // 浅琥珀 - 卡片背景
val EyeCareAmber200 = Color(0xFFFFE082)     // 中浅琥珀
val EyeCareAmber500 = Color(0xFFFFC107)     // 标准琥珀 - 主色调
val EyeCareAmber700 = Color(0xFFFF8F00)     // 深琥珀 - 按钮色
val EyeCareAmber900 = Color(0xFFE65100)     // 极深琥珀 - 强调色

// === 护眼橙色系 (温暖舒适 - 保留选项) ===
val EyeCareOrange50 = Color(0xFFFFF3E0)     // 极浅橙
val EyeCareOrange100 = Color(0xFFFFE0B2)    // 浅橙
val EyeCareOrange300 = Color(0xFFFFB74D)    // 中橙
val EyeCareOrange500 = Color(0xFFFF9800)    // 标准橙
val EyeCareOrange700 = Color(0xFFE65100)    // 深橙

// === 护眼深色系 (背景色) ===
val EyeCareDark900 = Color(0xFF0D0D0D)      // 极深黑 - 主背景
val EyeCareDark800 = Color(0xFF1A1A1A)      // 深黑 - 次要背景
val EyeCareDark700 = Color(0xFF2D2D2D)      // 中深灰 - 卡片背景
val EyeCareDark600 = Color(0xFF404040)      // 中灰 - 分割线
val EyeCareDark500 = Color(0xFF666666)      // 浅灰 - 次要文字
val EyeCareDark400 = Color(0xFF999999)      // 更浅灰

// === 护眼深绿色背景系 (为绿色主题专门设计) ===
val EyeCareGreenDark900 = Color(0xFF0A0E0A)    // 极深绿黑 - 主背景
val EyeCareGreenDark800 = Color(0xFF121612)    // 深绿黑 - 次要背景
val EyeCareGreenDark700 = Color(0xFF1C251C)    // 中深绿灰 - 卡片背景
val EyeCareGreenDark600 = Color(0xFF2C3A2C)    // 中绿灰 - 分割线
val EyeCareGreenDark500 = Color(0xFF4A5D4A)    // 浅绿灰 - 次要文字
val EyeCareGreenDark400 = Color(0xFF6B7E6B)    // 更浅绿灰

// === 护眼文字色 (低对比度) ===
val EyeCareTextPrimary = Color(0xFFE6D7C3)     // 主要文字 - 暖白色
val EyeCareTextSecondary = Color(0xFFBFB3A3)   // 次要文字 - 暖灰色
val EyeCareTextTertiary = Color(0xFF8C8376)    // 三级文字 - 深暖灰

// === 绿色主题文字色 (更舒缓的文字颜色) ===
val EyeCareGreenTextPrimary = Color(0xFFE8F0E8)      // 主要文字 - 淡绿白色
val EyeCareGreenTextSecondary = Color(0xFFC8D6C8)    // 次要文字 - 淡绿灰色
val EyeCareGreenTextTertiary = Color(0xFF9BAE9B)     // 三级文字 - 深淡绿灰

// === 夜间护眼模式 (极低蓝光) ===
val NightModeRed50 = Color(0xFFFFEBEE)       // 极浅红
val NightModeRed100 = Color(0xFFFFCDD2)      // 浅红
val NightModeRed300 = Color(0xFFE57373)      // 中红
val NightModeRed500 = Color(0xFFF44336)      // 标准红
val NightModeRed700 = Color(0xFFD32F2F)      // 深红
val NightModeRed900 = Color(0xFFB71C1C)      // 极深红

// === 夜间绿色模式 (深夜绿光护眼) ===
val NightModeGreen50 = Color(0xFFE8F0E8)     // 极浅夜绿
val NightModeGreen100 = Color(0xFFB8D0B8)    // 浅夜绿
val NightModeGreen300 = Color(0xFF7FA87F)    // 中夜绿
val NightModeGreen500 = Color(0xFF4F7F4F)    // 标准夜绿
val NightModeGreen700 = Color(0xFF2F5F2F)    // 深夜绿
val NightModeGreen900 = Color(0xFF1A4A1A)    // 极深夜绿

// === 功能状态色 (护眼版本) ===
val EyeCareSuccess = Color(0xFF66BB6A)       // 成功 - 柔和绿
val EyeCareWarning = Color(0xFFFFB74D)       // 警告 - 温和橙
val EyeCareError = Color(0xFFEF5350)         // 错误 - 柔和红
val EyeCareInfo = Color(0xFF42A5F5)          // 信息 - 柔和蓝

// === 传统色彩 (保持兼容) ===
val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// === 亮度调节相关颜色 ===
val BrightnessSliderTrack = Color(0xFF404040)      // 滑块轨道
val BrightnessSliderThumb = EyeCareGreen400         // 滑块按钮 - 改为绿色
val BrightnessSliderActive = EyeCareGreen600        // 激活状态

// === 传感器状态指示色 ===
val SensorActive = EyeCareSuccess                   // 传感器激活
val SensorInactive = EyeCareTextSecondary           // 传感器未激活
val SensorError = EyeCareError                      // 传感器错误