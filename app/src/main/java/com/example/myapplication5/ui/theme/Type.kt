package com.example.myapplication5.ui.theme

import androidx.compose.material3.Typography
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp

/**
 * 护眼字体排版 - 专为干眼症患者设计
 * 特点：
 * 1. 字体尺寸比标准大20-30%
 * 2. 行间距增加，减少阅读疲劳
 * 3. 优先使用清晰的Sans Serif字体
 * 4. 适度的字重，避免过细或过粗
 */

/**
 * 护眼字体排版系统
 */
val EyeCareTypography = Typography(
    // === 显示级别文字 (超大标题) ===
    displayLarge = TextStyle(
        fontFamily = FontFamily.SansSerif,
        fontWeight = FontWeight.Normal,
        fontSize = 64.sp,              // 比标准大30%
        lineHeight = 76.sp,            // 增加行间距
        letterSpacing = (-0.25).sp
    ),
    displayMedium = TextStyle(
        fontFamily = FontFamily.SansSerif,
        fontWeight = FontWeight.Normal,
        fontSize = 52.sp,              // 比标准大30%
        lineHeight = 64.sp,
        letterSpacing = 0.sp
    ),
    displaySmall = TextStyle(
        fontFamily = FontFamily.SansSerif,
        fontWeight = FontWeight.Normal,
        fontSize = 42.sp,              // 比标准大30%
        lineHeight = 52.sp,
        letterSpacing = 0.sp
    ),

    // === 标题级别文字 ===
    headlineLarge = TextStyle(
        fontFamily = FontFamily.SansSerif,
        fontWeight = FontWeight.Medium,
        fontSize = 36.sp,              // 比标准大20%
        lineHeight = 44.sp,
        letterSpacing = 0.sp
    ),
    headlineMedium = TextStyle(
        fontFamily = FontFamily.SansSerif,
        fontWeight = FontWeight.Medium,
        fontSize = 32.sp,              // 比标准大25%
        lineHeight = 40.sp,
        letterSpacing = 0.sp
    ),
    headlineSmall = TextStyle(
        fontFamily = FontFamily.SansSerif,
        fontWeight = FontWeight.Medium,
        fontSize = 28.sp,              // 比标准大25%
        lineHeight = 36.sp,
        letterSpacing = 0.sp
    ),

    // === 标题文字 ===
    titleLarge = TextStyle(
        fontFamily = FontFamily.SansSerif,
        fontWeight = FontWeight.Medium,
        fontSize = 26.sp,              // 比标准大18%
        lineHeight = 32.sp,
        letterSpacing = 0.sp
    ),
    titleMedium = TextStyle(
        fontFamily = FontFamily.SansSerif,
        fontWeight = FontWeight.Medium,
        fontSize = 20.sp,              // 比标准大20%
        lineHeight = 28.sp,
        letterSpacing = 0.15.sp
    ),
    titleSmall = TextStyle(
        fontFamily = FontFamily.SansSerif,
        fontWeight = FontWeight.Medium,
        fontSize = 17.sp,              // 比标准大20%
        lineHeight = 24.sp,
        letterSpacing = 0.1.sp
    ),

    // === 正文文字 (最重要的可读性) ===
    bodyLarge = TextStyle(
        fontFamily = FontFamily.SansSerif,
        fontWeight = FontWeight.Normal,
        fontSize = 20.sp,              // 比标准大25%，重要！
        lineHeight = 30.sp,            // 大幅增加行间距
        letterSpacing = 0.5.sp         // 增加字间距
    ),
    bodyMedium = TextStyle(
        fontFamily = FontFamily.SansSerif,
        fontWeight = FontWeight.Normal,
        fontSize = 18.sp,              // 比标准大25%
        lineHeight = 26.sp,            // 增加行间距
        letterSpacing = 0.25.sp
    ),
    bodySmall = TextStyle(
        fontFamily = FontFamily.SansSerif,
        fontWeight = FontWeight.Normal,
        fontSize = 15.sp,              // 比标准大25%
        lineHeight = 22.sp,            // 增加行间距
        letterSpacing = 0.4.sp
    ),

    // === 标签文字 ===
    labelLarge = TextStyle(
        fontFamily = FontFamily.SansSerif,
        fontWeight = FontWeight.Medium,
        fontSize = 17.sp,              // 比标准大30%
        lineHeight = 24.sp,
        letterSpacing = 0.1.sp
    ),
    labelMedium = TextStyle(
        fontFamily = FontFamily.SansSerif,
        fontWeight = FontWeight.Medium,
        fontSize = 15.sp,              // 比标准大25%
        lineHeight = 20.sp,
        letterSpacing = 0.5.sp
    ),
    labelSmall = TextStyle(
        fontFamily = FontFamily.SansSerif,
        fontWeight = FontWeight.Medium,
        fontSize = 13.sp,              // 比标准大25%
        lineHeight = 18.sp,
        letterSpacing = 0.5.sp
    )
)

/**
 * 特殊护眼场景文字样式
 */
object EyeCareTextStyles {
    /**
     * 大号亮度值显示 - 用于显示亮度百分比
     */
    val BrightnessDisplay = TextStyle(
        fontFamily = FontFamily.Monospace,  // 使用等宽字体，数字更清晰
        fontWeight = FontWeight.Bold,
        fontSize = 48.sp,
        lineHeight = 56.sp,
        letterSpacing = 2.sp
    )
    
    /**
     * 传感器数值显示 - 用于显示光照强度
     */
    val SensorValue = TextStyle(
        fontFamily = FontFamily.Monospace,
        fontWeight = FontWeight.Medium,
        fontSize = 32.sp,
        lineHeight = 40.sp,
        letterSpacing = 1.sp
    )
    
    /**
     * 状态提示文字 - 用于显示开启/关闭状态
     */
    val StatusText = TextStyle(
        fontFamily = FontFamily.SansSerif,
        fontWeight = FontWeight.Medium,
        fontSize = 22.sp,              // 比普通文字大
        lineHeight = 28.sp,
        letterSpacing = 0.5.sp
    )
    
    /**
     * 护眼提示文字 - 用于重要提醒
     */
    val EyeCareNotice = TextStyle(
        fontFamily = FontFamily.SansSerif,
        fontWeight = FontWeight.Medium,
        fontSize = 18.sp,
        lineHeight = 26.sp,
        letterSpacing = 0.8.sp         // 更大的字间距，便于阅读
    )
    
    /**
     * 夜间模式专用文字 - 减少字重以适应红光环境
     */
    val NightModeText = TextStyle(
        fontFamily = FontFamily.SansSerif,
        fontWeight = FontWeight.Light,  // 更轻的字重
        fontSize = 20.sp,
        lineHeight = 32.sp,             // 更大的行间距
        letterSpacing = 1.sp            // 更大的字间距
    )
}

/**
 * 传统字体排版（保持兼容性）
 */
val Typography = Typography(
    bodyLarge = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 16.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.5.sp
    )
)