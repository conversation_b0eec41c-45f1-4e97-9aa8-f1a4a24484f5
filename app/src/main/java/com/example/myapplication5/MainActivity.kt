package com.example.myapplication5

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import android.os.Build
import android.os.PowerManager
import android.content.Context
import android.util.Log
import com.example.myapplication5.ui.theme.*
import java.util.*
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 护眼主活动类 - 专为干眼症患者设计
 * 
 * 特色功能：
 * 1. 三种护眼模式：标准、夜间、超敏感
 * 2. 绿色/暖色调主题选择
 * 3. 后台运行和开机自启
 * 4. 系统亮度冲突检测和解决
 * 5. 大字体大按钮界面设计
 * 6. 实时护眼评级和建议
 * 7. 一键快速调节
 * 
 * <AUTHOR> v2.3
 * @since 2.3 - 添加系统冲突检测和用户引导
 */
class MainActivity : ComponentActivity() {
    
    private lateinit var brightnessController: BrightnessController
    private lateinit var lightSensorManager: LightSensorManager
    
    /** 自动亮度是否开启 */
    private var isAutoBrightnessEnabled = mutableStateOf(false)
    /** 当前光照强度 */
    private var currentLightLevel = mutableStateOf(0f)
    /** 当前亮度值 */
    private var currentBrightness = mutableStateOf(0.25f)
    /** 传感器信息 */
    private var sensorInfo = mutableStateOf("")
    /** 当前护眼模式 */
    private var currentEyeCareMode = mutableStateOf(BrightnessController.EyeCareMode.STANDARD)
    /** 护眼主题模式 - 默认绿色主题 */
    private var currentThemeMode = mutableStateOf(EyeCareThemeMode.GREEN_STANDARD)
    /** 用户亮度偏好 */
    private var userBrightnessOffset = mutableStateOf(0.0f)
    /** 是否使用绿色主题（true=绿色，false=暖色调） */
    private var useGreenTheme = mutableStateOf(true)
    /** 是否启用个性化亮度设置 */
    private var isPersonalizedBrightnessEnabled = mutableStateOf(false)
    /** 是否启用智能学习 */
    private var isLearningEnabled = mutableStateOf(true)
    /** 是否启用自动应用学习结果 */
    private var isAutoApplyLearningEnabled = mutableStateOf(false)
    /** 是否启用系统亮度监听 */
    private var isSystemBrightnessMonitoringEnabled = mutableStateOf(false)
    /** 是否启用原生亮度对比模式 */
    private var isNativeBrightnessComparisonEnabled = mutableStateOf(true)
    
    /** 后台运行状态 */
    private var isBackgroundServiceEnabled = mutableStateOf(false)
    /** 开机自启状态 */
    private var isAutoStartEnabled = mutableStateOf(false)
    /** 服务运行状态 */
    private var serviceRunningStatus = mutableStateOf("未运行")
    
    /** 后台模式配置状态 */
    private var isBackgroundModeConfigured = mutableStateOf(false)
    /** 后台模式配置进度 */
    private var backgroundModeProgress = mutableStateOf("")
    
    /** 系统自动亮度状态 */
    private var systemAutoBrightnessEnabled = mutableStateOf(false)
    /** 是否存在亮度冲突 */
    private var hasBrightnessConflict = mutableStateOf(false)
    
    /** 快速操作面板状态 */
    private var showQuickActions = mutableStateOf(false)
    /** 用户满意度评分 */
    private var userSatisfactionRating = mutableStateOf(0)
    /** 今日护眼时长 */
    private var todayEyeCareDuration = mutableStateOf(0L)
    /** 护眼健康评分 */
    private var eyeHealthScore = mutableStateOf(85)
    
    /**
     * 快速操作面板组件
     */
    @Composable
    private fun QuickActionsPanel() {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.secondaryContainer
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "快速操作",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSecondaryContainer
                    )
                    
                    IconButton(
                        onClick = { showQuickActions.value = !showQuickActions.value }
                    ) {
                        Icon(
                            imageVector = if (showQuickActions.value) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                            contentDescription = "展开/收起",
                            tint = MaterialTheme.colorScheme.onSecondaryContainer
                        )
                    }
                }
                
                if (showQuickActions.value) {
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // 快速亮度调节
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        QuickActionButton(
                            icon = Icons.Default.Info,
                            label = "夜间模式",
                            onClick = { 
                                currentEyeCareMode.value = BrightnessController.EyeCareMode.NIGHT
                                applyEyeCareMode()
                            }
                        )
                        
                        QuickActionButton(
                            icon = Icons.Default.Info,
                            label = "标准模式",
                            onClick = { 
                                currentEyeCareMode.value = BrightnessController.EyeCareMode.STANDARD
                                applyEyeCareMode()
                            }
                        )
                        
                        QuickActionButton(
                            icon = Icons.Default.Info,
                            label = "超敏感模式",
                            onClick = { 
                                currentEyeCareMode.value = BrightnessController.EyeCareMode.ULTRA_SENSITIVE
                                applyEyeCareMode()
                            }
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // 一键操作
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        QuickActionButton(
                            icon = Icons.Default.PlayArrow,
                            label = "启动服务",
                            onClick = { startBackgroundService() }
                        )
                        
                        QuickActionButton(
                            icon = Icons.Default.Info,
                            label = "停止服务",
                            onClick = { stopBackgroundService() }
                        )
                        
                        QuickActionButton(
                            icon = Icons.Default.Refresh,
                            label = "重新检测",
                            onClick = { performSystemConflictDetection() }
                        )
                    }
                }
            }
        }
    }
    
    /**
     * 快速操作按钮组件
     */
    @Composable
    private fun QuickActionButton(
        icon: ImageVector,
        label: String,
        onClick: () -> Unit
    ) {
        Button(
            onClick = onClick,
            modifier = Modifier.width(100.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            ),
            contentPadding = PaddingValues(8.dp)
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = label,
                    tint = MaterialTheme.colorScheme.onPrimaryContainer,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = label,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
    
    /**
     * 用户满意度反馈组件
     */
    @Composable
    private fun UserSatisfactionCard() {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.tertiaryContainer
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "护眼体验如何？",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onTertiaryContainer
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 满意度评分
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    for (i in 1..5) {
                        IconButton(
                            onClick = { 
                                userSatisfactionRating.value = i
                                saveUserSatisfaction(i)
                            }
                        ) {
                            Icon(
                                imageVector = if (i <= userSatisfactionRating.value) Icons.Default.Star else Icons.Default.Info,
                                contentDescription = "评分 $i",
                                tint = if (i <= userSatisfactionRating.value) 
                                    MaterialTheme.colorScheme.primary 
                                else 
                                    MaterialTheme.colorScheme.onTertiaryContainer,
                                modifier = Modifier.size(32.dp)
                            )
                        }
                    }
                }
                
                if (userSatisfactionRating.value > 0) {
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = when (userSatisfactionRating.value) {
                            1 -> "非常不满意"
                            2 -> "不满意"
                            3 -> "一般"
                            4 -> "满意"
                            5 -> "非常满意"
                            else -> ""
                        },
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onTertiaryContainer
                    )
                }
            }
        }
    }
    
    /**
     * 护眼健康评分卡片
     */
    @Composable
    private fun EyeHealthScoreCard() {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = when {
                    eyeHealthScore.value >= 90 -> MaterialTheme.colorScheme.primaryContainer
                    eyeHealthScore.value >= 80 -> MaterialTheme.colorScheme.secondaryContainer
                    eyeHealthScore.value >= 70 -> MaterialTheme.colorScheme.tertiaryContainer
                    else -> MaterialTheme.colorScheme.errorContainer
                }
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "护眼健康评分",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "${eyeHealthScore.value}",
                    style = MaterialTheme.typography.displayMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = when {
                        eyeHealthScore.value >= 90 -> "优秀！继续保持"
                        eyeHealthScore.value >= 80 -> "良好，有提升空间"
                        eyeHealthScore.value >= 70 -> "一般，需要改善"
                        else -> "需要关注护眼健康"
                    },
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 今日护眼时长
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "今日护眼时长",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    
                    Text(
                        text = formatDuration(todayEyeCareDuration.value),
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
            }
        }
    }
    
    /**
     * 格式化时长显示
     */
    private fun formatDuration(durationMs: Long): String {
        val hours = durationMs / (1000 * 60 * 60)
        val minutes = (durationMs % (1000 * 60 * 60)) / (1000 * 60)
        return "${hours}小时${minutes}分钟"
    }
    
    /**
     * 启动后台服务
     */
    private fun startBackgroundService() {
        try {
            val intent = Intent(this, EyeCareBackgroundService::class.java).apply {
                putExtra("eye_care_mode", currentEyeCareMode.value.name)
                putExtra("auto_adjustment", isAutoBrightnessEnabled.value)
                putExtra("theme_mode", currentThemeMode.value.name)
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(intent)
            } else {
                startService(intent)
            }
            
            isBackgroundServiceEnabled.value = true
            updateServiceStatus()
            
            Toast.makeText(this, "后台服务已启动", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Log.e("MainActivity", "启动后台服务失败: ${e.message}")
            Toast.makeText(this, "启动服务失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 停止后台服务
     */
    private fun stopBackgroundService() {
        try {
            val intent = Intent(this, EyeCareBackgroundService::class.java)
            stopService(intent)
            
            isBackgroundServiceEnabled.value = false
            updateServiceStatus()
            
            Toast.makeText(this, "后台服务已停止", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Log.e("MainActivity", "停止后台服务失败: ${e.message}")
            Toast.makeText(this, "停止服务失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 应用护眼模式
     */
    private fun applyEyeCareMode() {
        try {
            brightnessController.setEyeCareMode(currentEyeCareMode.value)
            
            // 如果自动亮度开启，立即调节亮度
            if (isAutoBrightnessEnabled.value) {
                val newBrightness = brightnessController.calculateBrightnessFromLight(currentLightLevel.value)
                brightnessController.setBrightness(newBrightness)
                currentBrightness.value = newBrightness
            }
            
            Toast.makeText(this, "已切换到${getEyeCareModeName(currentEyeCareMode.value)}", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Log.e("MainActivity", "应用护眼模式失败: ${e.message}")
            Toast.makeText(this, "切换模式失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 获取护眼模式名称
     */
    private fun getEyeCareModeName(mode: BrightnessController.EyeCareMode): String {
        return when (mode) {
            BrightnessController.EyeCareMode.STANDARD -> "标准模式"
            BrightnessController.EyeCareMode.NIGHT -> "夜间模式"
            BrightnessController.EyeCareMode.ULTRA_SENSITIVE -> "超敏感模式"
        }
    }
    
    /**
     * 执行系统冲突检测
     */
    private fun performSystemConflictDetection() {
        lifecycleScope.launch {
            try {
                // 显示检测中状态
                Toast.makeText(this@MainActivity, "正在检测系统状态...", Toast.LENGTH_SHORT).show()
                
                delay(1000) // 模拟检测过程
                
                // 检查系统亮度状态
                checkSystemBrightnessStatus()
                
                // 检查权限状态
                checkPermissionStatus()
                
                // 更新服务状态
                updateServiceStatus()
                
                Toast.makeText(this@MainActivity, "系统检测完成", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                Log.e("MainActivity", "系统冲突检测失败: ${e.message}")
                Toast.makeText(this@MainActivity, "检测失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    /**
     * 初始化护眼健康分析
     */
    private fun initializeEyeHealthAnalytics() {
        try {
            // 计算当前健康评分
            eyeHealthScore.value = EyeHealthAnalyticsManager.calculateHealthScore(this)
            
            // 获取今日使用时长
            todayEyeCareDuration.value = EyeHealthAnalyticsManager.getUsageDuration(this)
            
            Log.d("MainActivity", "护眼健康分析初始化完成，评分: ${eyeHealthScore.value}")
        } catch (e: Exception) {
            Log.e("MainActivity", "初始化护眼健康分析失败: ${e.message}")
        }
    }
    
    /**
     * 更新护眼健康数据
     */
    private fun updateEyeHealthData() {
        try {
            // 更新健康评分
            eyeHealthScore.value = EyeHealthAnalyticsManager.calculateHealthScore(this)
            
            // 更新使用时长
            todayEyeCareDuration.value = EyeHealthAnalyticsManager.getUsageDuration(this)
            
            Log.d("MainActivity", "护眼健康数据已更新，评分: ${eyeHealthScore.value}")
        } catch (e: Exception) {
            Log.e("MainActivity", "更新护眼健康数据失败: ${e.message}")
        }
    }
    
    /**
     * 保存用户满意度
     */
    private fun saveUserSatisfaction(rating: Int) {
        try {
            val prefs = getSharedPreferences("user_feedback", Context.MODE_PRIVATE)
            prefs.edit()
                .putInt("satisfaction_rating", rating)
                .putLong("rating_timestamp", System.currentTimeMillis())
                .apply()
            
            Log.d("MainActivity", "用户满意度已保存: $rating")
            
            // 根据评分提供反馈
            val feedback = when (rating) {
                1, 2 -> "感谢您的反馈，我们会继续改进护眼功能"
                3 -> "我们会努力提供更好的护眼体验"
                4, 5 -> "很高兴您满意我们的护眼功能！"
                else -> ""
            }
            
            if (feedback.isNotEmpty()) {
                Toast.makeText(this, feedback, Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "保存用户满意度失败: ${e.message}")
        }
    }
    
    /** 权限申请启动器 */
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (brightnessController.hasWriteSettingsPermission()) {
            Toast.makeText(this, "权限授予成功！现在可以自动调节亮度了", Toast.LENGTH_SHORT).show()
            checkSystemBrightnessStatus()
        } else {
            Toast.makeText(this, "需要系统设置权限才能自动调节亮度，请手动调节", Toast.LENGTH_LONG).show()
        }
    }
    
    /** 电池优化权限申请启动器 */
    private val batteryOptimizationLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        if (powerManager.isIgnoringBatteryOptimizations(packageName)) {
            Toast.makeText(this, "电池优化已关闭，后台运行更稳定", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(this, "建议关闭电池优化以确保后台服务稳定运行", Toast.LENGTH_LONG).show()
        }
    }
    
    /** 系统亮度设置启动器 */
    private val systemBrightnessSettingsLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        checkSystemBrightnessStatus()
        if (!brightnessController.isSystemAutoBrightnessEnabled()) {
            Toast.makeText(this, "系统自动亮度已关闭，护眼调节将更加精确", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // 初始化护眼亮度控制器
        brightnessController = BrightnessController(this, this)
        
        // 根据时间自动设置初始护眼模式
        val currentHour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY)
        if (brightnessController.shouldUseNightMode(currentHour)) {
            currentEyeCareMode.value = BrightnessController.EyeCareMode.NIGHT
            // 夜间默认使用绿色夜间模式（对眼睛更舒缓）
            currentThemeMode.value = EyeCareThemeMode.NIGHT_GREEN
            brightnessController.setEyeCareMode(BrightnessController.EyeCareMode.NIGHT)
        }
        
        // 初始化光传感器管理器
        lightSensorManager = LightSensorManager(this) { lightLevel ->
            currentLightLevel.value = lightLevel
            
            // 如果自动亮度开启，则调节亮度
            if (isAutoBrightnessEnabled.value) {
                val newBrightness = brightnessController.calculateBrightnessFromLight(lightLevel)
                brightnessController.setBrightness(newBrightness)
                currentBrightness.value = newBrightness
            }
        }
        
        // 获取传感器信息
        sensorInfo.value = lightSensorManager.getSensorInfo()
        
        // 初始化当前亮度
        currentBrightness.value = brightnessController.getCurrentBrightness()
        
        // 恢复用户设置
        restoreUserSettings()
        
        // 更新学习系统状态
        updateLearningStatus()
        
        // 检查系统亮度状态
        checkSystemBrightnessStatus()
        
        // 更新服务状态
        updateServiceStatus()
        
        // 检查是否因清理导致重启
        checkForCleanupRestart()
        
        // 启动服务监控
        ServiceMonitor.startMonitoring(this)
        
        // 初始化护眼健康分析
        initializeEyeHealthAnalytics()
        
        setContent {
            EyeCareTheme(eyeCareMode = currentThemeMode.value) {
                Scaffold(
                    modifier = Modifier.fillMaxSize(),
                    containerColor = MaterialTheme.colorScheme.background
                ) { innerPadding ->
                    EyeCareMainScreen(
                        modifier = Modifier.padding(innerPadding)
                    )
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        
        // 检查权限状态（用户可能从设置页面返回）
        checkPermissionStatus()
        
        // 如果前台自动亮度开启且后台服务未运行，则开始监听传感器
        if (isAutoBrightnessEnabled.value && !EyeCareBackgroundService.isServiceRunning) {
            lightSensorManager.startListening()
        }
        
        // 启动服务监控
        if (isBackgroundServiceEnabled.value) {
            ServiceMonitor.startMonitoring(this)
        }
        
        // 更新服务状态和系统状态
        updateServiceStatus()
        checkSystemBrightnessStatus()
        
        // 更新护眼健康数据
        updateEyeHealthData()
    }

    override fun onPause() {
        super.onPause()
        
        // 记录应用关闭时间，用于清理检测
        try {
            val prefs = getSharedPreferences("app_status", Context.MODE_PRIVATE)
            prefs.edit().putLong("last_close_time", System.currentTimeMillis()).apply()
        } catch (e: Exception) {
            Log.w("CleanupDetector", "记录关闭时间失败: ${e.message}")
        }
        
        // 如果后台服务运行，则不需要在前台监听传感器
        if (!EyeCareBackgroundService.isServiceRunning) {
            lightSensorManager.stopListening()
        }
        
        // 保存用户设置
        saveUserSettings()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 停止服务监控
        ServiceMonitor.stopMonitoring(this)
    }

    /**
     * 检查系统亮度状态
     */
    private fun checkSystemBrightnessStatus() {
        systemAutoBrightnessEnabled.value = brightnessController.isSystemAutoBrightnessEnabled()
        hasBrightnessConflict.value = brightnessController.hasBrightnessConflict()
    }

    /**
     * 护眼主界面 - 简化设计，突出核心功能
     */
    @Composable
    fun EyeCareMainScreen(modifier: Modifier = Modifier) {
        val scrollState = rememberScrollState()
        
        Column(
            modifier = modifier
                .fillMaxSize()
                .padding(16.dp)
                .verticalScroll(scrollState),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // === 核心亮度调节区域 ===
            CoreBrightnessControl()
            
            // === 快速模式切换 ===
            QuickModeSelector()
            
            // === 状态信息（简化版） ===
            SimpleStatusInfo()
            
            // === 基础功能（已实现） ===
            BasicFeaturesSection()
            
            // === 高级功能（可折叠，只显示已实现的） ===
            AdvancedFeaturesSection()
        }
    }
    
    /**
     * 核心亮度调节控制 - 类似手机自带的简洁设计
     */
    @Composable
    private fun CoreBrightnessControl() {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                // 标题 - 简洁明了
                Text(
                    text = "屏幕亮度",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 当前亮度显示 - 大字体，突出显示
                Text(
                    text = "${(currentBrightness.value * 100).toInt()}%",
                    style = MaterialTheme.typography.displayMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 护眼评级 - 简洁显示
                Text(
                    text = brightnessController.getBrightnessEyeCareRating(),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                )
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // 亮度滑块 - 大尺寸，易于操作
                Slider(
                    value = currentBrightness.value,
                    onValueChange = { brightness ->
                        if (!isAutoBrightnessEnabled.value) {
                            val originalBrightness = currentBrightness.value
                            brightnessController.setBrightness(brightness)
                            currentBrightness.value = brightness
                            
                            // 记录手动调节行为用于智能学习
                            brightnessController.recordManualAdjustment(
                                originalBrightness,
                                brightness,
                                currentLightLevel.value
                            )
                        }
                    },
                    enabled = !isAutoBrightnessEnabled.value,
                    valueRange = brightnessController.getMinBrightness()..brightnessController.getMaxBrightness(),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp),  // 增大滑块高度
                    colors = SliderDefaults.colors(
                        thumbColor = MaterialTheme.colorScheme.primary,
                        activeTrackColor = MaterialTheme.colorScheme.primary,
                        inactiveTrackColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.3f)
                    )
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 自动/手动切换 - 大按钮，易于点击
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "自动调节",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    
                    Switch(
                        checked = isAutoBrightnessEnabled.value,
                        onCheckedChange = { enabled ->
                            toggleAutoBrightness(enabled)
                        },
                        modifier = Modifier.size(64.dp, 36.dp),  // 更大的开关
                        colors = SwitchDefaults.colors(
                            checkedThumbColor = MaterialTheme.colorScheme.primary,
                            checkedTrackColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.5f),
                            uncheckedThumbColor = MaterialTheme.colorScheme.outline,
                            uncheckedTrackColor = MaterialTheme.colorScheme.outline.copy(alpha = 0.5f)
                        )
                    )
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 快速预设按钮 - 类似手机自带的快速调节
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    QuickPresetButton("暗", 0.05f)  // 5%
                    QuickPresetButton("标准", 0.25f)  // 25%
                    QuickPresetButton("亮", 0.60f)  // 60%
                    QuickPresetButton("最亮", 0.95f)  // 95%
                }
            }
        }
    }
    
    /**
     * 快速预设按钮
     */
    @Composable
    private fun QuickPresetButton(label: String, brightness: Float) {
        Button(
            onClick = {
                if (!isAutoBrightnessEnabled.value) {
                    brightnessController.setBrightness(brightness)
                    currentBrightness.value = brightness
                }
            },
            enabled = !isAutoBrightnessEnabled.value,
            modifier = Modifier.width(70.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.secondaryContainer,
                contentColor = MaterialTheme.colorScheme.onSecondaryContainer
            ),
            contentPadding = PaddingValues(8.dp)
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.Medium
            )
        }
    }
    
    /**
     * 快速模式选择器
     */
    @Composable
    private fun QuickModeSelector() {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainer
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "护眼模式",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    ModeButton("标准", BrightnessController.EyeCareMode.STANDARD)
                    ModeButton("夜间", BrightnessController.EyeCareMode.NIGHT)
                    ModeButton("超敏感", BrightnessController.EyeCareMode.ULTRA_SENSITIVE)
                }
            }
        }
    }
    
    /**
     * 模式按钮
     */
    @Composable
    private fun ModeButton(label: String, mode: BrightnessController.EyeCareMode) {
        val isSelected = currentEyeCareMode.value == mode
        
        Button(
            onClick = {
                currentEyeCareMode.value = mode
                brightnessController.setEyeCareMode(mode)
                saveUserSettings()
            },
            modifier = Modifier.fillMaxWidth(),
            colors = ButtonDefaults.buttonColors(
                containerColor = if (isSelected) 
                    MaterialTheme.colorScheme.primary 
                else 
                    MaterialTheme.colorScheme.surfaceVariant,
                contentColor = if (isSelected) 
                    MaterialTheme.colorScheme.onPrimary 
                else 
                    MaterialTheme.colorScheme.onSurfaceVariant
            ),
            contentPadding = PaddingValues(12.dp)
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
            )
        }
    }
    
    /**
     * 简化状态信息
     */
    @Composable
    private fun SimpleStatusInfo() {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainer
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "环境光照",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "${currentLightLevel.value.toInt()} lux",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
                
                Column {
                    Text(
                        text = "服务状态",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = if (EyeCareBackgroundService.isServiceRunning) "运行中" else "已停止",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = if (EyeCareBackgroundService.isServiceRunning) 
                            MaterialTheme.colorScheme.primary 
                        else 
                            MaterialTheme.colorScheme.error
                    )
                }
            }
        }
    }
    
    /**
     * 基础功能区域 - 只包含已完全实现的功能
     */
    @Composable
    private fun BasicFeaturesSection() {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainer
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "基础功能",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 后台服务控制
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "后台护眼保护",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = if (EyeCareBackgroundService.isServiceRunning) "✅ 正在运行" else "⏸️ 已停止",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    Button(
                        onClick = { 
                            if (EyeCareBackgroundService.isServiceRunning) {
                                stopBackgroundService()
                            } else {
                                startBackgroundService()
                            }
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = if (EyeCareBackgroundService.isServiceRunning) 
                                MaterialTheme.colorScheme.error 
                            else 
                                MaterialTheme.colorScheme.primary
                        )
                    ) {
                        Text(
                            if (EyeCareBackgroundService.isServiceRunning) "停止" else "启动",
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 权限设置
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "系统设置权限",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = if (brightnessController.isPermissionAvailable()) "✅ 已授权" else "❌ 未授权",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    OutlinedButton(
                        onClick = { requestWriteSettingsPermission() }
                    ) {
                        Text("设置", style = MaterialTheme.typography.bodySmall)
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 系统设置
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "系统自动亮度",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = if (hasBrightnessConflict.value) "⚠️ 需要关闭" else "✅ 已关闭",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    OutlinedButton(
                        onClick = { openSystemBrightnessSettings() }
                    ) {
                        Text("设置", style = MaterialTheme.typography.bodySmall)
                    }
                }
            }
        }
    }
    
    /**
     * 高级功能区域 - 只显示已完全实现的功能
     */
    @Composable
    private fun AdvancedFeaturesSection() {
        var expanded by remember { mutableStateOf(false) }
        
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainer
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "高级功能",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    
                    IconButton(onClick = { expanded = !expanded }) {
                        Icon(
                            imageVector = if (expanded) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                            contentDescription = if (expanded) "收起" else "展开",
                            tint = MaterialTheme.colorScheme.onSurface
                        )
                    }
                }
                
                if (expanded) {
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // 个性化亮度设置（已实现）
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = "个性化亮度设置",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurface
                            )
                            Text(
                                text = "为不同场景自定义亮度",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        
                        Switch(
                            checked = isPersonalizedBrightnessEnabled.value,
                            onCheckedChange = { enabled ->
                                isPersonalizedBrightnessEnabled.value = enabled
                                brightnessController.setPersonalizedBrightnessEnabled(enabled)
                                saveUserSettings()
                                
                                val message = if (enabled) "个性化亮度设置已启用" else "个性化亮度设置已禁用"
                                Toast.makeText(this@MainActivity, message, Toast.LENGTH_SHORT).show()
                            }
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // 个性化设置配置按钮
                    if (isPersonalizedBrightnessEnabled.value) {
                        Button(
                            onClick = {
                                try {
                                    val intent = Intent(this@MainActivity, PersonalizedBrightnessActivity::class.java)
                                    startActivity(intent)
                                } catch (e: Exception) {
                                    Toast.makeText(this@MainActivity, "无法打开个性化设置: ${e.message}", Toast.LENGTH_SHORT).show()
                                }
                            },
                            modifier = Modifier.fillMaxWidth().height(48.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.secondaryContainer,
                                contentColor = MaterialTheme.colorScheme.onSecondaryContainer
                            )
                        ) {
                            Text("配置个性化亮度", style = MaterialTheme.typography.bodyMedium)
                        }
                        
                        Spacer(modifier = Modifier.height(12.dp))
                    }
                    
                    // 诊断工具（简化版）
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = "诊断工具",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurface
                            )
                            Text(
                                text = "故障排除和服务诊断",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        
                        OutlinedButton(
                            onClick = {
                                try {
                                    performSimpleDiagnostic()
                                } catch (e: Exception) {
                                    Toast.makeText(this@MainActivity, "诊断失败: ${e.message}", Toast.LENGTH_SHORT).show()
                                }
                            }
                        ) {
                            Text("快速诊断", style = MaterialTheme.typography.bodySmall)
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // 重置功能
                    Button(
                        onClick = {
                            brightnessController.resetBrightness()
                            currentBrightness.value = BrightnessController.DEFAULT_BRIGHTNESS
                            Toast.makeText(this@MainActivity, "亮度已重置为默认值", Toast.LENGTH_SHORT).show()
                        },
                        modifier = Modifier.fillMaxWidth().height(48.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.tertiaryContainer,
                            contentColor = MaterialTheme.colorScheme.onTertiaryContainer
                        )
                    ) {
                        Text("重置为默认设置", style = MaterialTheme.typography.bodyMedium)
                    }
                }
            }
        }
    }
    
    /**
     * 系统冲突警告
     */
    @Composable
    private fun SystemConflictWarning() {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.errorContainer
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(20.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Filled.Info,
                    contentDescription = "冲突警告",
                    modifier = Modifier.size(32.dp),
                    tint = MaterialTheme.colorScheme.onErrorContainer
                )
                
                Spacer(modifier = Modifier.width(16.dp))
                
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "⚠️ 系统亮度冲突",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onErrorContainer,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Text(
                        text = "检测到系统自动亮度已开启，可能影响护眼调节效果",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Button(
                    onClick = {
                        openSystemBrightnessSettings()
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("去设置", color = MaterialTheme.colorScheme.onError)
                }
            }
        }
    }
    
    /**
     * 系统设置指引
     */
    @Composable
    private fun SystemSettingsGuidance() {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainer
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
            ) {
                Text(
                    text = "📱 系统设置指引",
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                // 权限状态显示
                Surface(
                    color = if (!brightnessController.hasWriteSettingsPermission()) 
                        MaterialTheme.colorScheme.errorContainer 
                    else 
                        MaterialTheme.colorScheme.tertiaryContainer,
                    shape = RoundedCornerShape(12.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = if (!brightnessController.hasWriteSettingsPermission()) Icons.Filled.Close else Icons.Filled.Check,
                                contentDescription = "权限状态",
                                tint = if (!brightnessController.hasWriteSettingsPermission()) 
                                    MaterialTheme.colorScheme.onErrorContainer 
                                else 
                                    MaterialTheme.colorScheme.onTertiaryContainer,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = brightnessController.getPermissionStatusDescription(),
                                style = MaterialTheme.typography.bodyMedium,
                                color = if (!brightnessController.hasWriteSettingsPermission()) 
                                    MaterialTheme.colorScheme.onErrorContainer 
                                else 
                                    MaterialTheme.colorScheme.onTertiaryContainer
                            )
                        }
                        
                        if (!brightnessController.hasWriteSettingsPermission()) {
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "💡 需要手动授权系统设置权限才能自动调节亮度",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onErrorContainer
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 系统状态显示
                Surface(
                    color = if (hasBrightnessConflict.value) 
                        MaterialTheme.colorScheme.errorContainer 
                    else 
                        MaterialTheme.colorScheme.tertiaryContainer,
                    shape = RoundedCornerShape(12.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = if (hasBrightnessConflict.value) Icons.Filled.Close else Icons.Filled.Check,
                                contentDescription = "状态",
                                tint = if (hasBrightnessConflict.value) 
                                    MaterialTheme.colorScheme.onErrorContainer 
                                else 
                                    MaterialTheme.colorScheme.onTertiaryContainer,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = brightnessController.getSystemBrightnessModeDescription(),
                                style = MaterialTheme.typography.bodyMedium,
                                color = if (hasBrightnessConflict.value) 
                                    MaterialTheme.colorScheme.onErrorContainer 
                                else 
                                    MaterialTheme.colorScheme.onTertiaryContainer
                            )
                        }
                        
                        if (hasBrightnessConflict.value) {
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "💡 建议关闭系统自动亮度以获得最佳护眼效果",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onErrorContainer
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 操作按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedButton(
                        onClick = {
                            openSystemBrightnessSettings()
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("系统亮度设置")
                    }
                    
                    OutlinedButton(
                        onClick = {
                            checkSystemBrightnessStatus()
                            Toast.makeText(this@MainActivity, "状态已刷新", Toast.LENGTH_SHORT).show()
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("刷新状态")
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 权限申请按钮
                if (!brightnessController.hasWriteSettingsPermission()) {
                    Button(
                        onClick = {
                            requestWriteSettingsPermission()
                        },
                        modifier = Modifier.fillMaxWidth().height(56.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.error
                        )
                    ) {
                        Text("申请系统设置权限", style = MaterialTheme.typography.labelLarge)
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 详细说明
                Text(
                    text = """
                        📋 设置步骤：
                        1. 点击"申请系统设置权限"按钮
                        2. 在系统设置中开启"允许修改系统设置"
                        3. 点击"系统亮度设置"关闭"自适应亮度"
                        4. 返回应用，护眼调节将正常工作
                        
                        ℹ️ 说明：系统设置权限是必需的，用于调节屏幕亮度。关闭系统自动亮度后，我们的护眼算法将完全接管亮度调节，为干眼症患者提供更精准的保护。
                        
                        ⚠️ 注意：如果权限申请失败，请手动进入系统设置-应用管理-护眼光感调节-权限，开启"修改系统设置"权限。
                    """.trimIndent(),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
    
    /**
     * 应用标题和状态
     */
    @Composable
    private fun EyeCareHeader() {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainer
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Filled.Star,
                    contentDescription = "护眼图标",
                    modifier = Modifier.size(48.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                Text(
                    text = "护眼光感调节",
                    style = MaterialTheme.typography.headlineMedium,
                    color = MaterialTheme.colorScheme.onSurface,
                    textAlign = TextAlign.Center
                )
                
                Text(
                    text = "专为干眼症患者设计",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 当前模式指示器
                Surface(
                    color = MaterialTheme.colorScheme.primaryContainer,
                    shape = RoundedCornerShape(20.dp)
                ) {
                    Text(
                        text = "${getModeDisplayName(currentEyeCareMode.value)} · ${getThemeDisplayName(useGreenTheme.value)}",
                        style = MaterialTheme.typography.labelLarge,
                        color = MaterialTheme.colorScheme.onPrimaryContainer,
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                    )
                }
                
                // 服务状态指示器
                if (EyeCareBackgroundService.isServiceRunning) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Surface(
                        color = MaterialTheme.colorScheme.tertiaryContainer,
                        shape = RoundedCornerShape(20.dp)
                    ) {
                        Row(
                            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Filled.Check,
                                contentDescription = "后台运行",
                                modifier = Modifier.size(16.dp),
                                tint = MaterialTheme.colorScheme.onTertiaryContainer
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = "后台保护中",
                                style = MaterialTheme.typography.labelMedium,
                                color = MaterialTheme.colorScheme.onTertiaryContainer
                            )
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 主题色彩选择器
     */
    @Composable
    private fun ThemeColorSelector() {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainer
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
            ) {
                Text(
                    text = "护眼色彩主题",
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                Text(
                    text = "选择最舒适的色彩主题",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                // 主题选择按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    ThemeColorButton(
                        isGreen = true,
                        label = "舒缓绿色",
                        description = "最护眼",
                        isSelected = useGreenTheme.value,
                        modifier = Modifier.weight(1f)
                    )
                    
                    ThemeColorButton(
                        isGreen = false,
                        label = "温暖橙色",
                        description = "减蓝光",
                        isSelected = !useGreenTheme.value,
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
    
    /**
     * 主题色彩选择按钮
     */
    @Composable
    private fun ThemeColorButton(
        isGreen: Boolean,
        label: String,
        description: String,
        isSelected: Boolean,
        modifier: Modifier = Modifier
    ) {
        Card(
            modifier = modifier,
            colors = CardDefaults.cardColors(
                containerColor = if (isSelected) 
                    MaterialTheme.colorScheme.primaryContainer 
                else 
                    MaterialTheme.colorScheme.surface
            ),
            onClick = {
                useGreenTheme.value = isGreen
                updateThemeMode()
                saveUserSettings()
                Toast.makeText(this@MainActivity, "已切换到$label 主题", Toast.LENGTH_SHORT).show()
            }
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 色彩预览圆点
                Surface(
                    modifier = Modifier.size(32.dp),
                    shape = RoundedCornerShape(16.dp),
                    color = if (isGreen) EyeCareGreen400 else EyeCareAmber500
                ) {}
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = label,
                    style = MaterialTheme.typography.labelLarge,
                    color = if (isSelected) 
                        MaterialTheme.colorScheme.onPrimaryContainer 
                    else 
                        MaterialTheme.colorScheme.onSurface,
                    textAlign = TextAlign.Center
                )
                
                Text(
                    text = description,
                    style = MaterialTheme.typography.labelSmall,
                    color = if (isSelected) 
                        MaterialTheme.colorScheme.onPrimaryContainer 
                    else 
                        MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
    
    /**
     * 快速状态显示
     */
    @Composable
    private fun QuickStatusCard() {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainer
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // 当前亮度
                StatusItem(
                    icon = Icons.Filled.Star,
                    label = "当前亮度",
                    value = "${(currentBrightness.value * 100).toInt()}%",
                    rating = brightnessController.getBrightnessEyeCareRating()
                )
                
                // 光照强度
                StatusItem(
                    icon = Icons.Filled.Home,
                    label = "环境光照",
                    value = "${currentLightLevel.value.toInt()} lux",
                    rating = getEnvironmentRating(currentLightLevel.value)
                )
                
                // 运行状态
                StatusItem(
                    icon = if (EyeCareBackgroundService.isServiceRunning) Icons.Filled.Check else 
                          if (hasBrightnessConflict.value) Icons.Filled.Info else Icons.Filled.Close,
                    label = "系统状态",
                    value = if (hasBrightnessConflict.value) "需要设置" else serviceRunningStatus.value,
                    rating = if (hasBrightnessConflict.value) "系统冲突" else if (EyeCareBackgroundService.isServiceRunning) "后台保护" else "前台运行"
                )
            }
        }
    }
    
    /**
     * 状态项组件
     */
    @Composable
    private fun StatusItem(
        icon: ImageVector,
        label: String,
        value: String,
        rating: String
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.width(100.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = label,
                modifier = Modifier.size(32.dp),
                tint = if (label == "系统状态" && hasBrightnessConflict.value) 
                    MaterialTheme.colorScheme.error 
                else 
                    MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = label,
                style = MaterialTheme.typography.labelMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )
            
            Text(
                text = value,
                style = EyeCareTextStyles.SensorValue.copy(fontSize = 18.sp),
                color = MaterialTheme.colorScheme.onSurface,
                textAlign = TextAlign.Center
            )
            
            Text(
                text = rating,
                style = MaterialTheme.typography.labelSmall,
                color = if (label == "系统状态" && hasBrightnessConflict.value) 
                    MaterialTheme.colorScheme.error 
                else 
                    MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )
        }
    }
    
    /**
     * 护眼模式选择器
     */
    @Composable
    private fun EyeCareModeSelector() {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainer
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
            ) {
                Text(
                    text = "护眼模式",
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                // 模式选择按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    EyeCareModeButton(
                        mode = BrightnessController.EyeCareMode.STANDARD,
                        icon = Icons.Filled.Home,
                        label = "标准护眼",
                        description = "日常使用",
                        modifier = Modifier.weight(1f)
                    )
                    
                    EyeCareModeButton(
                        mode = BrightnessController.EyeCareMode.NIGHT,
                        icon = Icons.Filled.Star,
                        label = "夜间模式",
                        description = "深夜使用",
                        modifier = Modifier.weight(1f)
                    )
                    
                    EyeCareModeButton(
                        mode = BrightnessController.EyeCareMode.ULTRA_SENSITIVE,
                        icon = Icons.Filled.Favorite,
                        label = "超敏感",
                        description = "极暗环境",
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
    
    /**
     * 护眼模式按钮
     */
    @Composable
    private fun EyeCareModeButton(
        mode: BrightnessController.EyeCareMode,
        icon: ImageVector,
        label: String,
        description: String,
        modifier: Modifier = Modifier
    ) {
        val isSelected = currentEyeCareMode.value == mode
        
        Card(
            modifier = modifier,
            colors = CardDefaults.cardColors(
                containerColor = if (isSelected) 
                    MaterialTheme.colorScheme.primaryContainer 
                else 
                    MaterialTheme.colorScheme.surface
            ),
            onClick = {
                currentEyeCareMode.value = mode
                brightnessController.setEyeCareMode(mode)
                updateThemeMode()
                saveUserSettings()
                
                // 重新启动传感器监听（如果自动调节已启用）
                if (isAutoBrightnessEnabled.value) {
                    restartSensorListening()
                }
                
                // 如果后台服务运行，更新服务配置
                if (EyeCareBackgroundService.isServiceRunning) {
                    updateBackgroundService()
                }
                
                Toast.makeText(this@MainActivity, "已切换到$label", Toast.LENGTH_SHORT).show()
            }
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = label,
                    modifier = Modifier.size(32.dp),
                    tint = if (isSelected) 
                        MaterialTheme.colorScheme.onPrimaryContainer 
                    else 
                        MaterialTheme.colorScheme.onSurface
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = label,
                    style = MaterialTheme.typography.labelLarge,
                    color = if (isSelected) 
                        MaterialTheme.colorScheme.onPrimaryContainer 
                    else 
                        MaterialTheme.colorScheme.onSurface,
                    textAlign = TextAlign.Center
                )
                
                Text(
                    text = description,
                    style = MaterialTheme.typography.labelSmall,
                    color = if (isSelected) 
                        MaterialTheme.colorScheme.onPrimaryContainer 
                    else 
                        MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
    
    /**
     * 主要控制面板
     */
    @Composable
    private fun MainControlPanel() {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainer
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
            ) {
                // 自动亮度开关
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "自动护眼调节",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = if (hasBrightnessConflict.value) 
                                "根据环境光照自动调节亮度（需关闭系统自动亮度）" 
                            else 
                                "根据环境光照自动调节亮度",
                            style = MaterialTheme.typography.bodyMedium,
                            color = if (hasBrightnessConflict.value) 
                                MaterialTheme.colorScheme.error 
                            else 
                                MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    Switch(
                        checked = isAutoBrightnessEnabled.value,
                        onCheckedChange = { enabled ->
                            toggleAutoBrightness(enabled)
                        },
                        modifier = Modifier.size(60.dp, 32.dp)  // 更大的开关
                    )
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 手动亮度调节
                Text(
                    text = "手动亮度调节",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = "当前: ${(currentBrightness.value * 100).toInt()}% - ${brightnessController.getBrightnessEyeCareRating()}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Slider(
                    value = currentBrightness.value,
                    onValueChange = { brightness ->
                        if (!isAutoBrightnessEnabled.value) {
                            val originalBrightness = currentBrightness.value
                            brightnessController.setBrightness(brightness)
                            currentBrightness.value = brightness
                            
                            // 记录手动调节行为用于智能学习
                            brightnessController.recordManualAdjustment(
                                originalBrightness,
                                brightness,
                                currentLightLevel.value
                            )
                        }
                    },
                    enabled = !isAutoBrightnessEnabled.value,
                    valueRange = brightnessController.getMinBrightness()..brightnessController.getMaxBrightness(),
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 快速调节按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Button(
                        onClick = {
                            brightnessController.resetBrightness()
                            currentBrightness.value = BrightnessController.DEFAULT_BRIGHTNESS
                        },
                        modifier = Modifier.weight(1f).height(56.dp)  // 更高的按钮
                    ) {
                        Text("重置护眼亮度", style = MaterialTheme.typography.labelLarge)
                    }
                    
                    Button(
                        onClick = {
                            requestWriteSettingsPermission()
                        },
                        modifier = Modifier.weight(1f).height(56.dp)
                    ) {
                        Text("申请权限", style = MaterialTheme.typography.labelLarge)
                    }
                }
            }
        }
    }
    
    /**
     * 后台运行设置
     */
    @Composable
    private fun BackgroundServiceSettings() {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainer
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
            ) {
                Text(
                    text = "后台运行设置",
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                // 后台运行开关
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "后台护眼保护",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = "应用关闭后继续自动调节亮度",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    Switch(
                        checked = isBackgroundServiceEnabled.value,
                        onCheckedChange = { enabled ->
                            toggleBackgroundService(enabled)
                        },
                        modifier = Modifier.size(60.dp, 32.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 开机自启开关
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "开机自动启动",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = "开机后自动启动护眼保护",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    Switch(
                        checked = isAutoStartEnabled.value,
                        onCheckedChange = { enabled ->
                            toggleAutoStart(enabled)
                        },
                        modifier = Modifier.size(60.dp, 32.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 一键启用后台模式按钮
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Button(
                        onClick = {
                            configureBackgroundMode()
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(60.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = if (isBackgroundModeConfigured.value) 
                                MaterialTheme.colorScheme.tertiary 
                            else 
                                MaterialTheme.colorScheme.primary,
                            contentColor = if (isBackgroundModeConfigured.value)
                                MaterialTheme.colorScheme.onTertiary
                            else
                                MaterialTheme.colorScheme.onPrimary
                        ),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Center
                        ) {
                            Icon(
                                imageVector = if (isBackgroundModeConfigured.value) Icons.Filled.CheckCircle else Icons.Filled.Settings,
                                contentDescription = "后台模式配置",
                                modifier = Modifier.size(24.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = if (isBackgroundModeConfigured.value) "✅ 后台模式已配置" else "🚀 一键启用后台模式",
                                style = MaterialTheme.typography.titleMedium,
                                fontSize = 16.sp
                            )
                        }
                    }
                    
                    // 配置进度显示
                    if (backgroundModeProgress.value.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Surface(
                            color = MaterialTheme.colorScheme.secondaryContainer,
                            shape = RoundedCornerShape(8.dp),
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text(
                                text = backgroundModeProgress.value,
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSecondaryContainer,
                                modifier = Modifier.padding(12.dp)
                            )
                        }
                    }
                    
                    // 功能说明
                    if (!isBackgroundModeConfigured.value) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "一键配置后台模式将自动：\n• 检查并申请必要权限\n• 关闭系统亮度冲突\n• 优化电池设置\n• 启动后台护眼服务\n• 验证配置成功",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.padding(start = 4.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 电池优化按钮
                Button(
                    onClick = {
                        requestBatteryOptimizationExemption()
                    },
                    modifier = Modifier.fillMaxWidth().height(56.dp)
                ) {
                    Text("电池优化豁免", style = MaterialTheme.typography.labelLarge)
                }
                
                // 服务状态显示
                if (EyeCareBackgroundService.isServiceRunning) {
                    Spacer(modifier = Modifier.height(12.dp))
                    Surface(
                        color = MaterialTheme.colorScheme.tertiaryContainer,
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Row(
                            modifier = Modifier.padding(12.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Filled.Check,
                                contentDescription = "运行状态",
                                tint = MaterialTheme.colorScheme.onTertiaryContainer,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "后台护眼服务正在运行，持续保护您的眼部健康",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onTertiaryContainer
                            )
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 高级设置
     */
    @Composable
    private fun AdvancedSettings() {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainer
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
            ) {
                Text(
                    text = "个性化设置",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                // 个性化亮度设置开关
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "情境化亮度设置",
                            style = MaterialTheme.typography.titleSmall,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = "为不同使用场景自定义舒适亮度",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    Switch(
                        checked = isPersonalizedBrightnessEnabled.value,
                        onCheckedChange = { enabled ->
                            isPersonalizedBrightnessEnabled.value = enabled
                            brightnessController.setPersonalizedBrightnessEnabled(enabled)
                            saveUserSettings()
                            
                            val message = if (enabled) "个性化亮度设置已启用" else "个性化亮度设置已禁用"
                            Toast.makeText(this@MainActivity, message, Toast.LENGTH_SHORT).show()
                        },
                        modifier = Modifier.size(50.dp, 28.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 个性化亮度设置按钮
                Button(
                    onClick = {
                        PersonalizedBrightnessActivity.start(this@MainActivity)
                    },
                    modifier = Modifier.fillMaxWidth().height(48.dp),
                    enabled = isPersonalizedBrightnessEnabled.value
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Settings,
                            contentDescription = "个性化设置",
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("配置情境亮度", style = MaterialTheme.typography.labelLarge)
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 个性化状态显示
                if (isPersonalizedBrightnessEnabled.value) {
                    Surface(
                        color = MaterialTheme.colorScheme.primaryContainer,
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(12.dp)
                        ) {
                            val currentScenario = brightnessController.getCurrentScenario()
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = currentScenario.icon,
                                    fontSize = 16.sp
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "当前匹配情境: ${currentScenario.displayName}",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer
                                )
                            }
                            
                            Text(
                                text = "点击上方按钮可调整各情境的亮度设置",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 亮度偏好调节
                Text(
                    text = "全局亮度偏好",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = "在个性化设置基础上的全局调节 (${if (userBrightnessOffset.value > 0) "+" else ""}${(userBrightnessOffset.value * 100).toInt()}%)",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Slider(
                    value = userBrightnessOffset.value,
                    onValueChange = { offset ->
                        userBrightnessOffset.value = offset
                        brightnessController.setUserBrightnessOffset(offset)
                        saveUserSettings()
                        
                        // 如果后台服务运行，更新服务配置
                        if (EyeCareBackgroundService.isServiceRunning) {
                            updateBackgroundService()
                        }
                    },
                    valueRange = -0.2f..0.2f,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
    
    /**
     * 诊断工具按钮 - 简洁版本
     */
    @Composable
    private fun DiagnosticToolButton() {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainer
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 24.dp, vertical = 16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "🔧 诊断工具",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Text(
                        text = "故障排除和服务诊断",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                OutlinedButton(
                    onClick = {
                        try {
                            performSimpleDiagnostic()
                        } catch (e: Exception) {
                            Log.e("DiagnosticTool", "诊断失败: ${e.message}")
                            Toast.makeText(this@MainActivity, "诊断失败: ${e.message}", Toast.LENGTH_SHORT).show()
                        }
                    },
                    modifier = Modifier.height(40.dp)
                ) {
                    Text("快速诊断", style = MaterialTheme.typography.labelSmall)
                }
            }
        }
    }
    
    // 注意：诊断对话框已经被新的DiagnosticActivity页面替代
    
    // 注意：基础诊断已经被新的DiagnosticActivity页面替代
    
    // 注意：详细诊断对话框已经被新的DiagnosticActivity页面替代
    
    // 注意：诊断结果对话框已经被新的DiagnosticActivity页面替代
    
    /**
     * 执行简化诊断 - 更安全的版本（包含传感器健康检查）
     */
    private fun performSimpleDiagnostic() {
        try {
            val currentTime = System.currentTimeMillis()
            val serviceRunning = EyeCareBackgroundService.isServiceRunning
            val hasPermission = try {
                brightnessController.hasWriteSettingsPermission()
            } catch (e: Exception) {
                false
            }
            
            val systemAutoEnabled = try {
                brightnessController.isSystemAutoBrightnessEnabled()
            } catch (e: Exception) {
                false
            }
            
            val currentBrightness = try {
                (brightnessController.getCurrentBrightness() * 100).toInt()
            } catch (e: Exception) {
                0
            }
            
            // 新增：传感器健康检查
            val sensorHealthInfo = try {
                if (this::lightSensorManager.isInitialized) {
                    val health = lightSensorManager.getSensorHealth()
                    val needsRecovery = lightSensorManager.needsRecovery()
                    val lastUpdate = lightSensorManager.getLastUpdateTime()
                    val timeSinceUpdate = (currentTime - lastUpdate) / 1000
                    
                    """
                        🔍 传感器状态：$health
                        ⏱️ 距上次更新：${timeSinceUpdate}秒
                        ${if (needsRecovery) "⚠️ 需要恢复传感器" else "✅ 传感器正常"}
                    """.trimIndent()
                } else {
                    "🔍 传感器：未初始化"
                }
            } catch (e: Exception) {
                "🔍 传感器：检查失败"
            }
            
            val simpleInfo = """
                🔍 快速诊断结果
                
                📅 检查时间：${java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(currentTime)}
                
                🟢 后台服务：${if (serviceRunning) "运行中" else "已停止"}
                🔑 系统权限：${if (hasPermission) "已授权" else "未授权"}
                🌞 系统自动亮度：${if (systemAutoEnabled) "已开启（建议关闭）" else "已关闭"}
                📱 当前屏幕亮度：${currentBrightness}%
                
                $sensorHealthInfo
                
                ${if (!serviceRunning) "⚠️ 建议：点击下方重启服务按钮" else "✅ 服务状态正常"}
                ${if (!hasPermission) "⚠️ 建议：前往设置授予权限" else ""}
                ${if (systemAutoEnabled) "⚠️ 建议：关闭系统自动亮度" else ""}
            """.trimIndent()
            
            val dialog = AlertDialog.Builder(this)
                .setTitle("🔍 快速诊断")
                .setMessage(simpleInfo)
                .setPositiveButton("确定", null)
                .setNegativeButton("重启服务") { _, _ ->
                    try {
                        if (serviceRunning) {
                            stopBackgroundService()
                        }
                        startBackgroundService()
                        Toast.makeText(this, "服务重启中...", Toast.LENGTH_SHORT).show()
                        updateServiceStatus()
                    } catch (e: Exception) {
                        Toast.makeText(this, "重启服务失败", Toast.LENGTH_SHORT).show()
                    }
                }
                .setNeutralButton("权限设置") { _, _ ->
                    try {
                        val intent = Intent(Settings.ACTION_MANAGE_WRITE_SETTINGS)
                        intent.data = Uri.parse("package:$packageName")
                        startActivity(intent)
                    } catch (e: Exception) {
                        Toast.makeText(this, "无法打开权限设置", Toast.LENGTH_SHORT).show()
                    }
                }
                .create()
            
            dialog.show()
            
        } catch (e: Exception) {
            Log.e("DiagnosticTool", "简化诊断失败: ${e.message}")
            // 最后的兜底方案
            Toast.makeText(this, """
                基础诊断信息：
                
                服务状态：${if (EyeCareBackgroundService.isServiceRunning) "运行中" else "已停止"}
                
                如有问题请重启应用或重启手机
            """.trimIndent(), Toast.LENGTH_LONG).show()
        }
    }
    
    // 注意：原来的DiagnosticSection已经替换为DiagnosticToolButton和对话框形式
    
    /**
     * 护眼建议卡片
     */
    @Composable
    private fun EyeCareAdviceCard() {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.secondaryContainer
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Filled.Info,
                    contentDescription = "建议",
                    modifier = Modifier.size(32.dp),
                    tint = MaterialTheme.colorScheme.onSecondaryContainer
                )
                
                Spacer(modifier = Modifier.width(16.dp))
                
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "护眼建议",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSecondaryContainer
                    )
                    
                    Text(
                        text = brightnessController.getEyeCareAdvice(currentLightLevel.value),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSecondaryContainer
                    )
                }
            }
        }
    }
    
    /**
     * 退出应用区域
     */
    @Composable
    private fun ExitApplicationSection() {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainer
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "退出应用",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                Text(
                    text = "如需退出应用，请选择退出方式",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(bottom = 20.dp)
                )
                
                OutlinedButton(
                    onClick = {
                        showExitConfirmationDialog()
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    ),
                    border = BorderStroke(1.dp, MaterialTheme.colorScheme.error),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Close,
                            contentDescription = "退出应用",
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "退出护眼应用",
                            style = MaterialTheme.typography.titleMedium
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "💡 提示：退出应用不会影响已启动的后台护眼保护",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
    
    // === 辅助函数 ===
    
    /**
     * 打开系统亮度设置
     */
    private fun openSystemBrightnessSettings() {
        try {
            val intent = Intent(Settings.ACTION_DISPLAY_SETTINGS)
            systemBrightnessSettingsLauncher.launch(intent)
        } catch (e: Exception) {
            Toast.makeText(this, "无法打开系统设置，请手动进入设置-显示-亮度", Toast.LENGTH_LONG).show()
        }
    }
    
    /**
     * 更新主题模式
     */
    private fun updateThemeMode() {
        currentThemeMode.value = when {
            currentEyeCareMode.value == BrightnessController.EyeCareMode.NIGHT && useGreenTheme.value -> EyeCareThemeMode.NIGHT_GREEN
            currentEyeCareMode.value == BrightnessController.EyeCareMode.NIGHT && !useGreenTheme.value -> EyeCareThemeMode.NIGHT_RED
            useGreenTheme.value -> EyeCareThemeMode.GREEN_STANDARD
            else -> EyeCareThemeMode.WARM_STANDARD
        }
    }
    
    /**
     * 切换后台服务
     */
    private fun toggleBackgroundService(enabled: Boolean) {
        if (enabled) {
            // 检查权限
            if (!brightnessController.hasWriteSettingsPermission()) {
                Toast.makeText(this, "请先授予系统设置权限", Toast.LENGTH_SHORT).show()
                requestWriteSettingsPermission()
                return
            }
            
            // 检查系统冲突
            if (hasBrightnessConflict.value) {
                Toast.makeText(this, "建议先关闭系统自动亮度以获得最佳效果", Toast.LENGTH_LONG).show()
            }
            
            // 启动后台服务
            startBackgroundService()
            isBackgroundServiceEnabled.value = true
            Toast.makeText(this, "后台护眼保护已启动", Toast.LENGTH_SHORT).show()
        } else {
            // 停止后台服务
            stopBackgroundService()
            isBackgroundServiceEnabled.value = false
            Toast.makeText(this, "后台护眼保护已停止", Toast.LENGTH_SHORT).show()
        }
        
        saveUserSettings()
        updateServiceStatus()
    }
    
    /**
     * 切换开机自启
     */
    private fun toggleAutoStart(enabled: Boolean) {
        isAutoStartEnabled.value = enabled
        saveUserSettings()
        
        val message = if (enabled) "开机自启已启用" else "开机自启已禁用"
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
        
        // 如果启用开机自启，显示设置指导
        if (enabled) {
            showAutoStartGuide()
        }
    }
    
    /**
     * 一键配置后台模式
     * 综合检查权限、设置和服务状态，提供完整的后台运行解决方案
     */
    private fun configureBackgroundMode() {
        backgroundModeProgress.value = "正在检查配置..."
        
        // 异步执行配置检查
        lifecycleScope.launch {
            try {
                // 步骤1：检查系统设置权限
                backgroundModeProgress.value = "🔐 检查系统设置权限..."
                delay(500)
                
                if (!brightnessController.hasWriteSettingsPermission()) {
                    backgroundModeProgress.value = "❌ 需要授予系统设置权限"
                    showBackgroundModeDialog(
                        "权限检查",
                        "需要授予【修改系统设置】权限才能在后台调节亮度。\n\n点击【去设置】按钮，找到【修改系统设置】权限并开启。",
                        "去设置"
                    ) {
                        requestWriteSettingsPermission()
                    }
                    return@launch
                }
                
                // 步骤2：检查系统自动亮度冲突
                backgroundModeProgress.value = "🔍 检查系统设置冲突..."
                delay(500)
                checkSystemBrightnessStatus()
                
                if (hasBrightnessConflict.value) {
                    backgroundModeProgress.value = "⚠️ 检测到亮度设置冲突"
                    showBackgroundModeDialog(
                        "系统冲突",
                        "检测到系统自动亮度已开启，这会与护眼应用产生冲突。\n\n建议关闭系统自动亮度以获得最佳护眼效果。",
                        "去关闭"
                    ) {
                        openSystemBrightnessSettings()
                    }
                    // 继续执行，不阻止配置
                }
                
                // 步骤3：检查电池优化设置
                backgroundModeProgress.value = "🔋 检查电池优化设置..."
                delay(500)
                
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
                    if (!powerManager.isIgnoringBatteryOptimizations(packageName)) {
                        backgroundModeProgress.value = "⚠️ 建议关闭电池优化"
                        showBackgroundModeDialog(
                            "电池优化",
                            "为了确保后台服务稳定运行，建议将护眼应用添加到电池优化白名单。\n\n这不会显著增加耗电量。",
                            "去设置"
                        ) {
                            requestBatteryOptimizationExemption()
                        }
                        // 继续执行，不阻止配置
                    }
                }
                
                // 步骤4：启动后台服务
                backgroundModeProgress.value = "🚀 启动后台护眼服务..."
                delay(500)
                
                if (!isBackgroundServiceEnabled.value) {
                    toggleBackgroundService(true)
                }
                
                // 步骤5：验证服务状态
                backgroundModeProgress.value = "✅ 验证服务状态..."
                delay(1000)
                
                updateServiceStatus()
                
                if (EyeCareBackgroundService.isServiceRunning) {
                    backgroundModeProgress.value = "🎉 后台模式配置成功！"
                    isBackgroundModeConfigured.value = true
                    
                    // 保存配置状态
                    EyeCareSettingsManager.setBackgroundModeConfigured(this@MainActivity, true)
                    saveUserSettings()
                    
                    showBackgroundModeDialog(
                        "配置成功 - 重要提醒",
                        """🎉 后台护眼模式已成功启用！
                        
                        ✅ 系统权限：已授予
                        ✅ 后台服务：正在运行
                        ✅ 护眼保护：已激活
                        
                        📱 使用说明：
                        • 现在可以安全退出应用，护眼保护持续工作
                        • 通知栏显示【护眼保护运行中】状态
                        
                        ⚠️ 重要注意事项：
                        • 请勿在清理后台时删除护眼应用
                        • 清理后护眼保护将停止工作
                        • 可能导致眼部疲劳和不适
                        
                        💡 建议设置：
                        • 将护眼应用加入后台管理白名单
                        • 避免使用一键清理功能清理本应用
                        • 如发现服务停止，请重新打开应用""".trimIndent(),
                        "我知道了"
                    ) {
                        // 显示白名单设置引导
                        showWhitelistGuide()
                    }
                } else {
                    backgroundModeProgress.value = "❌ 服务启动失败"
                    showBackgroundModeDialog(
                        "配置失败",
                        "❌ 后台服务启动失败。\n\n可能的原因：\n• 系统限制过于严格\n• 内存不足\n• 设备不支持\n\n请尝试：\n1. 重启应用\n2. 重启设备\n3. 手动进入应用设置检查权限",
                        "重试"
                    ) {
                        configureBackgroundMode() // 重试配置
                    }
                }
                
            } catch (e: Exception) {
                backgroundModeProgress.value = "❌ 配置过程出错"
                Log.e("BackgroundMode", "配置后台模式失败", e)
                showBackgroundModeDialog(
                    "配置错误",
                    "配置过程中出现错误：${e.message}\n\n请重试或联系技术支持。",
                    "重试"
                ) {
                    configureBackgroundMode()
                }
            }
        }
    }
    
    /**
     * 显示后台模式配置对话框
     */
    private fun showBackgroundModeDialog(
        title: String,
        message: String,
        positiveText: String,
        onPositiveClick: () -> Unit
    ) {
        val dialog = AlertDialog.Builder(this)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton(positiveText) { _, _ ->
                onPositiveClick()
            }
            .setNegativeButton("取消") { _, _ -> }
            .setCancelable(true)
            .create()
        
        dialog.show()
    }
    
    /**
     * 检查开机自启状态
     */
    private fun checkAutoStartStatus(): String {
        try {
            val prefs = getSharedPreferences("boot_log", Context.MODE_PRIVATE)
            val lastBootTime = prefs.getLong("last_boot_time", 0)
            val lastBootAction = prefs.getString("last_boot_action", "unknown")
            val lastBootError = prefs.getString("last_boot_error", "")
            
            if (lastBootTime == 0L) {
                return "未检测到开机启动记录"
            }
            
            val timeAgo = (System.currentTimeMillis() - lastBootTime) / 1000 / 60 // 分钟
            val status = when (lastBootAction) {
                "success" -> "✅ 开机启动成功"
                "failed" -> "❌ 开机启动失败: $lastBootError"
                else -> "❓ 开机启动状态未知"
            }
            
            return "$status (${timeAgo}分钟前)"
            
        } catch (e: Exception) {
            return "检查开机状态失败: ${e.message}"
        }
    }
    
    /**
     * 打开应用设置页面
     */
    private fun openAppSettings() {
        try {
            val intent = Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = android.net.Uri.fromParts("package", packageName, null)
            }
            startActivity(intent)
        } catch (e: Exception) {
            Log.e("MainActivity", "打开应用设置失败: ${e.message}")
            Toast.makeText(this, "无法打开应用设置", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 更新后台服务配置
     */
    private fun updateBackgroundService() {
        if (EyeCareBackgroundService.isServiceRunning) {
            // 重新启动服务以应用新配置
            stopBackgroundService()
            startBackgroundService()
        }
    }
    
    /**
     * 重新启动传感器监听
     */
    private fun restartSensorListening() {
        try {
            // 先停止当前监听
            lightSensorManager.stopListening()
            
            // 延迟一点时间后重新启动
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                if (isAutoBrightnessEnabled.value && lightSensorManager.isLightSensorAvailable()) {
                    if (lightSensorManager.startListening()) {
                        Log.d("MainActivity", "传感器监听重新启动成功")
                        Toast.makeText(this, "传感器已重新启动", Toast.LENGTH_SHORT).show()
                    } else {
                        Log.w("MainActivity", "传感器监听重新启动失败")
                        Toast.makeText(this, "传感器启动失败，请检查权限", Toast.LENGTH_SHORT).show()
                    }
                }
            }, 300)
        } catch (e: Exception) {
            Log.e("MainActivity", "重新启动传感器监听失败: ${e.message}")
        }
    }
    
    /**
     * 申请电池优化豁免
     */
    private fun requestBatteryOptimizationExemption() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                    data = Uri.parse("package:$packageName")
                }
                batteryOptimizationLauncher.launch(intent)
            }
        } catch (e: Exception) {
            Toast.makeText(this, "无法打开电池优化设置，请手动设置", Toast.LENGTH_LONG).show()
        }
    }
    
    /**
     * 保存用户设置
     */
    private fun saveUserSettings() {
        EyeCareSettingsManager.saveSettings(
            context = this,
            autoStartEnabled = isAutoStartEnabled.value,
            eyeCareMode = currentEyeCareMode.value.name,
            autoAdjustment = isAutoBrightnessEnabled.value,
            brightnessOffset = userBrightnessOffset.value,
            useGreenTheme = useGreenTheme.value,
            backgroundServiceEnabled = isBackgroundServiceEnabled.value,
            backgroundModeConfigured = isBackgroundModeConfigured.value,
            personalizedBrightnessEnabled = isPersonalizedBrightnessEnabled.value,
            learningEnabled = isLearningEnabled.value,
            autoApplyLearningEnabled = isAutoApplyLearningEnabled.value,
            systemBrightnessMonitoringEnabled = isSystemBrightnessMonitoringEnabled.value,
            nativeBrightnessComparisonEnabled = isNativeBrightnessComparisonEnabled.value
        )
    }
    
    /**
     * 恢复用户设置
     */
    private fun restoreUserSettings() {
        isAutoStartEnabled.value = EyeCareSettingsManager.isAutoStartEnabled(this)
        isAutoBrightnessEnabled.value = EyeCareSettingsManager.isAutoAdjustmentEnabled(this)
        userBrightnessOffset.value = EyeCareSettingsManager.getBrightnessOffset(this)
        useGreenTheme.value = EyeCareSettingsManager.isGreenThemeEnabled(this)
        isBackgroundServiceEnabled.value = EyeCareSettingsManager.isBackgroundServiceEnabled(this)
        isBackgroundModeConfigured.value = EyeCareSettingsManager.isBackgroundModeConfigured(this)
        isPersonalizedBrightnessEnabled.value = EyeCareSettingsManager.isPersonalizedBrightnessEnabled(this)
        isLearningEnabled.value = EyeCareSettingsManager.isLearningEnabled(this)
        isAutoApplyLearningEnabled.value = EyeCareSettingsManager.isAutoApplyLearningEnabled(this)
        isSystemBrightnessMonitoringEnabled.value = EyeCareSettingsManager.isSystemBrightnessMonitoringEnabled(this)
        isNativeBrightnessComparisonEnabled.value = EyeCareSettingsManager.isNativeBrightnessComparisonEnabled(this)
        
        val eyeCareModeString = EyeCareSettingsManager.getEyeCareMode(this)
        currentEyeCareMode.value = BrightnessController.EyeCareMode.valueOf(eyeCareModeString)
        
        brightnessController.setEyeCareMode(currentEyeCareMode.value)
        brightnessController.setUserBrightnessOffset(userBrightnessOffset.value)
        brightnessController.setPersonalizedBrightnessEnabled(isPersonalizedBrightnessEnabled.value)
        brightnessController.setLearningEnabled(isLearningEnabled.value)
        brightnessController.setAutoApplyLearningEnabled(isAutoApplyLearningEnabled.value)
        updateThemeMode()
        
        // 如果用户之前启用了后台服务，自动启动
        if (isBackgroundServiceEnabled.value && !EyeCareBackgroundService.isServiceRunning) {
            startBackgroundService()
        }
    }
    
    /**
     * 更新服务状态
     */
    private fun updateServiceStatus() {
        serviceRunningStatus.value = when {
            EyeCareBackgroundService.isServiceRunning -> "后台运行"
            isAutoBrightnessEnabled.value -> "前台运行"
            else -> "已停止"
        }
        
        isBackgroundServiceEnabled.value = EyeCareBackgroundService.isServiceRunning
    }
    
    /**
     * 检查权限状态
     */
    private fun checkPermissionStatus() {
        val hasPermission = brightnessController.hasWriteSettingsPermission()
        val isDebugMode = isDebugModeEnabled()
        
        // 如果权限状态发生变化，显示提示
        if (hasPermission && !isAutoBrightnessEnabled.value) {
            Toast.makeText(this, "权限已获得，现在可以开启自动护眼调节了", Toast.LENGTH_LONG).show()
        } else if (!hasPermission && isAutoBrightnessEnabled.value) {
            Toast.makeText(this, "权限已丢失，自动护眼调节已停止", Toast.LENGTH_LONG).show()
            isAutoBrightnessEnabled.value = false
            saveUserSettings()
        }
        
        // 检查调试模式状态
        if (!hasPermission && !isDebugMode) {
            showDebugModeGuide()
        }
    }
    
    /**
     * 检查是否为调试模式
     */
    private fun isDebugModeEnabled(): Boolean {
        return try {
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            val applicationInfo = packageInfo.applicationInfo
            (applicationInfo?.flags ?: 0 and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE) != 0
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 显示调试模式引导
     */
    private fun showDebugModeGuide() {
        val dialog = AlertDialog.Builder(this)
            .setTitle("需要调试模式或系统权限")
            .setMessage("""
                检测到您的应用无法获得系统设置权限，这是因为：
                
                🔒 权限限制：WRITE_SETTINGS权限需要系统签名
                🐛 调试模式：开发测试时可临时获得权限
                👤 用户授权：正式使用时需要手动授权
                
                解决方案：
                1. 开发测试：在开发者选项中开启"调试应用"
                2. 正式使用：手动授权"修改系统设置"权限
                3. 系统应用：使用系统签名安装到/system/app
                
                当前状态：${if (isDebugModeEnabled()) "调试模式已开启" else "调试模式未开启"}
            """.trimIndent())
            .setPositiveButton("去开发者选项") { _, _ ->
                openDeveloperOptions()
            }
            .setNegativeButton("手动授权") { _, _ ->
                requestWriteSettingsPermission()
            }
            .setNeutralButton("知道了") { _, _ -> }
            .setCancelable(false)
            .create()
        
        dialog.show()
    }
    
    /**
     * 打开开发者选项
     */
    private fun openDeveloperOptions() {
        try {
            val intent = Intent(android.provider.Settings.ACTION_APPLICATION_DEVELOPMENT_SETTINGS)
            startActivity(intent)
        } catch (e: Exception) {
            Toast.makeText(this, "无法打开开发者选项，请手动进入设置-开发者选项", Toast.LENGTH_LONG).show()
        }
    }
    
    private fun getModeDisplayName(mode: BrightnessController.EyeCareMode): String {
        return when (mode) {
            BrightnessController.EyeCareMode.STANDARD -> "标准护眼模式"
            BrightnessController.EyeCareMode.NIGHT -> "夜间护眼模式"
            BrightnessController.EyeCareMode.ULTRA_SENSITIVE -> "超敏感模式"
        }
    }
    
    private fun getThemeDisplayName(isGreen: Boolean): String {
        return if (isGreen) "舒缓绿色" else "温暖橙色"
    }
    
    private fun getEnvironmentRating(lightLevel: Float): String {
        return when {
            lightLevel < 1 -> "完全黑暗"
            lightLevel < 5 -> "极暗环境"
            lightLevel < 20 -> "昏暗环境"
            lightLevel < 100 -> "室内环境"
            lightLevel < 500 -> "明亮环境"
            else -> "户外环境"
        }
    }
    
    /**
     * 切换自动亮度功能
     */
    private fun toggleAutoBrightness(enabled: Boolean) {
        if (enabled) {
            // 检查权限
            if (!brightnessController.hasWriteSettingsPermission()) {
                Toast.makeText(this, "需要系统设置权限才能自动调节亮度", Toast.LENGTH_LONG).show()
                showPermissionExplanationDialog()
                return
            }
            
            // 检查传感器
            if (!lightSensorManager.isLightSensorAvailable()) {
                Toast.makeText(this, "设备不支持光传感器，无法自动调节", Toast.LENGTH_SHORT).show()
                return
            }
            
            // 检查系统冲突
            checkSystemBrightnessStatus()
            if (hasBrightnessConflict.value) {
                Toast.makeText(this, "检测到系统自动亮度冲突，建议关闭系统自动亮度", Toast.LENGTH_LONG).show()
            }
            
            // 如果后台服务未运行，则在前台启动传感器监听
            if (!EyeCareBackgroundService.isServiceRunning) {
                if (lightSensorManager.startListening()) {
                    isAutoBrightnessEnabled.value = true
                    Toast.makeText(this, "护眼自动调节已开启", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(this, "启动光传感器失败", Toast.LENGTH_SHORT).show()
                }
            } else {
                // 后台服务运行时，更新服务配置
                isAutoBrightnessEnabled.value = true
                updateBackgroundService()
                Toast.makeText(this, "护眼自动调节已开启", Toast.LENGTH_SHORT).show()
            }
        } else {
            // 停止自动调节
            if (!EyeCareBackgroundService.isServiceRunning) {
                lightSensorManager.stopListening()
            }
            
            isAutoBrightnessEnabled.value = false
            
            if (EyeCareBackgroundService.isServiceRunning) {
                updateBackgroundService()
            }
            
            Toast.makeText(this, "护眼自动调节已关闭", Toast.LENGTH_SHORT).show()
        }
        
        saveUserSettings()
        updateServiceStatus()
    }
    
    /**
     * 申请系统设置写入权限
     */
    private fun requestWriteSettingsPermission() {
        if (!brightnessController.hasWriteSettingsPermission()) {
            // 显示详细的权限说明对话框
            showPermissionExplanationDialog()
        } else {
            Toast.makeText(this, "已有系统设置权限", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 显示权限说明对话框
     */
    private fun showPermissionExplanationDialog() {
        val dialog = AlertDialog.Builder(this)
            .setTitle("需要系统设置权限")
            .setMessage("""
                护眼功能需要修改系统亮度设置，请按以下步骤操作：
                
                1. 点击"去设置"按钮
                2. 在系统设置中找到"护眼光感调节"
                3. 开启"允许修改系统设置"开关
                4. 返回应用即可正常使用
                
                注意：此权限仅用于调节屏幕亮度，不会影响其他系统设置。
            """.trimIndent())
            .setPositiveButton("去设置") { _, _ ->
                openWriteSettingsPermission()
            }
            .setNegativeButton("取消") { _, _ ->
                Toast.makeText(this, "未获得权限，护眼功能将受限", Toast.LENGTH_LONG).show()
            }
            .setCancelable(false)
            .create()
        
        dialog.show()
    }
    
    /**
     * 打开系统设置权限页面
     */
    private fun openWriteSettingsPermission() {
        try {
            val intent = Intent(Settings.ACTION_MANAGE_WRITE_SETTINGS).apply {
                data = Uri.parse("package:$packageName")
            }
            permissionLauncher.launch(intent)
        } catch (e: Exception) {
            // 如果无法直接打开权限页面，引导用户手动操作
            showManualPermissionGuide()
        }
    }
    
    /**
     * 显示手动权限设置指南
     */
    private fun showManualPermissionGuide() {
        val dialog = AlertDialog.Builder(this)
            .setTitle("手动设置权限")
            .setMessage("""
                请按以下步骤手动设置权限：
                
                1. 打开系统"设置"
                2. 找到"应用管理"或"应用"
                3. 搜索并点击"护眼光感调节"
                4. 找到"权限"选项
                5. 开启"修改系统设置"权限
                6. 返回应用重新尝试
            """.trimIndent())
            .setPositiveButton("知道了") { _, _ -> }
            .setCancelable(false)
            .create()
        
        dialog.show()
    }
    
    /**
     * 显示开机自启设置指导
     */
    private fun showAutoStartGuide() {
        val dialog = AlertDialog.Builder(this)
            .setTitle("开机自启设置指导")
            .setMessage("""
                为了确保开机自启功能正常工作，请按以下步骤设置：
                
                1️⃣ 系统设置 → 应用管理 → 护眼光感调节
                2️⃣ 权限管理 → 自启动 → 允许
                3️⃣ 电池优化 → 不优化
                4️⃣ 后台运行 → 允许
                
                不同品牌手机设置路径可能不同：
                • 华为：设置 → 应用 → 护眼光感调节 → 自启动
                • 小米：设置 → 应用管理 → 护眼光感调节 → 权限管理
                • OPPO：设置 → 应用管理 → 护眼光感调节 → 自启动
                • vivo：设置 → 应用管理 → 护眼光感调节 → 自启动
                
                设置完成后，重启手机测试开机自启功能。
            """.trimIndent())
            .setPositiveButton("去设置") { _, _ ->
                openAppSettings()
            }
            .setNegativeButton("知道了") { _, _ -> }
            .setCancelable(false)
            .create()
        
        dialog.show()
    }
    
    /**
     * 显示白名单设置引导
     */
    private fun showWhitelistGuide() {
        val dialog = AlertDialog.Builder(this)
            .setTitle("🛡️ 设置后台管理白名单")
            .setMessage("""
                为了确保护眼保护不被意外清理，建议将应用加入白名单：
                
                📱 不同品牌设置方法：
                
                🔹 小米/红米手机：
                • 设置 → 应用设置 → 应用管理
                • 找到"护眼光感调节" → 权限管理
                • 开启"自启动"和"后台活动"
                • 安全中心 → 垃圾清理 → 设置 → 自动清理白名单
                
                🔹 华为/荣耀手机：
                • 设置 → 应用和服务 → 应用管理
                • 找到"护眼光感调节" → 权限 → 自启动
                • 手机管家 → 清理加速 → 忽略清单
                
                🔹 OPPO/一加手机：
                • 设置 → 应用管理 → 应用列表
                • 找到"护眼光感调节" → 权限管理 → 自启动
                • 手机管家 → 清理存储 → 清理白名单
                
                🔹 vivo/iQOO手机：
                • 设置 → 应用与权限 → 应用管理
                • 找到"护眼光感调节" → 权限 → 自启动
                • i管家 → 空间清理 → 白名单管理
                
                ⚠️ 温馨提示：
                • 设置完成后建议重启手机测试
                • 如果找不到设置项，可搜索"自启动"或"白名单"
                • 不同系统版本设置位置可能略有差异
            """.trimIndent())
            .setPositiveButton("去设置") { _, _ ->
                openAppSettings()
            }
            .setNeutralButton("稍后设置") { _, _ ->
                Toast.makeText(this, "建议尽快设置白名单，避免服务被意外清理", Toast.LENGTH_LONG).show()
            }
            .setNegativeButton("我知道了") { _, _ -> }
            .setCancelable(true)
            .create()
        
        dialog.show()
    }
    
    /**
     * 检查是否因清理导致重启
     */
    private fun checkForCleanupRestart() {
        try {
            // 检查用户是否启用了后台服务
            if (!isBackgroundServiceEnabled.value) {
                return
            }
            
            // 检查服务是否正在运行
            if (EyeCareBackgroundService.isServiceRunning) {
                return
            }
            
            val prefs = getSharedPreferences("app_status", Context.MODE_PRIVATE)
            
            // 检查是否是用户主动完全退出
            val wasCompletelyExited = prefs.getBoolean("was_completely_exited", false)
            if (wasCompletelyExited) {
                // 清除完全退出标记，显示欢迎回来信息
                prefs.edit().putBoolean("was_completely_exited", false).apply()
                showWelcomeBackDialog()
                return
            }
            
            // 如果用户启用了后台服务但服务未运行，且不是主动退出，可能是被清理了
            val lastCloseTime = prefs.getLong("last_close_time", 0)
            val currentTime = System.currentTimeMillis()
            val timeSinceLastClose = (currentTime - lastCloseTime) / 1000 / 60 // 分钟
            
            // 如果距离上次关闭超过5分钟且服务未运行，可能是被清理了
            if (timeSinceLastClose > 5) {
                showCleanupDetectedDialog()
            }
            
            // 记录本次启动时间
            prefs.edit().putLong("last_open_time", currentTime).apply()
            
        } catch (e: Exception) {
            Log.w("CleanupDetector", "清理检测失败: ${e.message}")
        }
    }
    
    /**
     * 显示清理检测对话框
     */
    private fun showCleanupDetectedDialog() {
        val dialog = AlertDialog.Builder(this)
            .setTitle("🔍 检测到可能的后台清理")
            .setMessage("""
                检测到护眼应用可能被意外清理：
                
                📱 常见原因：
                • 使用系统或第三方清理工具
                • 在后台应用管理中手动清理
                • 系统内存不足自动清理
                • 电池优化导致的限制
                
                🛡️ 预防措施：
                • 将护眼应用加入后台白名单
                • 关闭应用的电池优化
                • 避免手动清理护眼应用
                • 设置应用为"自启动"
                
                💡 现在是否重新配置后台模式？
            """.trimIndent())
            .setPositiveButton("立即配置") { _, _ ->
                configureBackgroundMode()
            }
            .setNeutralButton("设置白名单") { _, _ ->
                showWhitelistGuide()
            }
            .setNegativeButton("稍后") { _, _ -> }
            .setCancelable(true)
            .create()
        
        dialog.show()
    }
    
    /**
     * 显示退出确认对话框
     */
    private fun showExitConfirmationDialog() {
        val dialog = AlertDialog.Builder(this)
            .setTitle("🚪 选择退出方式")
            .setMessage("""
                请选择您想要的退出方式：
                
                🛡️ 退出应用（保持后台保护）：
                • 关闭应用界面，返回桌面
                • 后台护眼服务继续运行
                • 护眼保护持续工作
                • 通知栏显示运行状态
                
                ⛔ 完全退出（停止所有服务）：
                • 关闭应用界面
                • 停止后台护眼服务
                • 护眼保护彻底停止
                • 需要重新启动应用才能恢复保护
                
                💡 建议：如无特殊需要，推荐选择"保持后台保护"
            """.trimIndent())
            .setPositiveButton("保持后台保护") { _, _ ->
                exitAppKeepingService()
            }
            .setNegativeButton("完全退出") { _, _ ->
                showCompleteExitConfirmation()
            }
            .setNeutralButton("取消") { _, _ -> }
            .setCancelable(true)
            .create()
        
        dialog.show()
    }
    
    /**
     * 显示完全退出的二次确认
     */
    private fun showCompleteExitConfirmation() {
        val dialog = AlertDialog.Builder(this)
            .setTitle("⚠️ 确认完全退出")
            .setMessage("""
                您确定要完全退出护眼应用吗？
                
                ❌ 完全退出的后果：
                • 后台护眼保护将停止工作
                • 屏幕亮度将不再自动调节
                • 可能导致眼部疲劳和不适
                • 需要重新打开应用才能恢复保护
                
                🛡️ 如果您只是想关闭界面，建议选择"保持后台保护"
                
                您仍然要完全退出吗？
            """.trimIndent())
            .setPositiveButton("仍然完全退出") { _, _ ->
                completelyExitApp()
            }
            .setNegativeButton("保持后台保护") { _, _ ->
                exitAppKeepingService()
            }
            .setNeutralButton("取消") { _, _ -> }
            .setCancelable(true)
            .create()
        
        dialog.show()
    }
    
    /**
     * 退出应用但保持后台服务
     */
    private fun exitAppKeepingService() {
        try {
            // 记录退出时间用于清理检测
            val prefs = getSharedPreferences("app_status", Context.MODE_PRIVATE)
            prefs.edit().putLong("last_close_time", System.currentTimeMillis()).apply()
            
            // 显示退出提示
            Toast.makeText(
                this, 
                "🛡️ 应用已退出，后台护眼保护继续运行", 
                Toast.LENGTH_LONG
            ).show()
            
            // 检查后台服务状态
            if (isBackgroundServiceEnabled.value && EyeCareBackgroundService.isServiceRunning) {
                Log.d("Exit", "应用退出，后台服务继续运行")
            } else if (isBackgroundServiceEnabled.value) {
                // 如果用户启用了后台服务但服务未运行，尝试启动
                startBackgroundService()
                Toast.makeText(this, "🚀 已启动后台护眼保护", Toast.LENGTH_SHORT).show()
            }
            
            // 移动到后台（不是真正的退出）
            moveTaskToBack(true)
            
        } catch (e: Exception) {
            Log.e("Exit", "退出应用时出错: ${e.message}")
            Toast.makeText(this, "退出时出现问题，请稍后重试", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 完全退出应用（停止所有服务）
     */
    private fun completelyExitApp() {
        try {
            Log.d("Exit", "执行完全退出")
            
            // 停止后台服务
            if (EyeCareBackgroundService.isServiceRunning) {
                stopBackgroundService()
                Log.d("Exit", "后台服务已停止")
            }
            
            // 停止前台传感器监听
            if (::lightSensorManager.isInitialized) {
                lightSensorManager.stopListening()
                Log.d("Exit", "前台传感器监听已停止")
            }
            
            // 停止服务监控
            ServiceMonitor.stopMonitoring(this)
            Log.d("Exit", "服务监控已停止")
            
            // 记录完全退出状态
            val prefs = getSharedPreferences("app_status", Context.MODE_PRIVATE)
            prefs.edit().apply {
                putLong("last_complete_exit_time", System.currentTimeMillis())
                putBoolean("was_completely_exited", true)
                apply()
            }
            
            // 显示退出提示
            Toast.makeText(
                this, 
                "⛔ 护眼应用已完全退出，所有保护已停止", 
                Toast.LENGTH_LONG
            ).show()
            
            // 完全退出应用
            finishAffinity() // 关闭应用的所有Activity
            System.exit(0) // 完全退出进程
            
        } catch (e: Exception) {
            Log.e("Exit", "完全退出时出错: ${e.message}")
            Toast.makeText(this, "退出时出现问题: ${e.message}", Toast.LENGTH_LONG).show()
            
            // 如果出错，至少尝试正常退出
            finish()
        }
    }
    
    /**
     * 显示欢迎回来对话框
     */
    private fun showWelcomeBackDialog() {
        val dialog = AlertDialog.Builder(this)
            .setTitle("🎉 欢迎回来")
            .setMessage("""
                欢迎重新使用护眼应用！
                
                上次您选择了完全退出，所有护眼服务已停止。
                
                🛡️ 是否重新启动护眼保护？
                • 一键配置后台模式
                • 恢复智能亮度调节
                • 继续保护您的眼部健康
                
                💡 提示：您也可以稍后手动配置
            """.trimIndent())
            .setPositiveButton("立即配置") { _, _ ->
                configureBackgroundMode()
            }
            .setNeutralButton("手动配置") { _, _ ->
                Toast.makeText(this, "您可以随时点击'一键启用后台模式'来配置护眼保护", Toast.LENGTH_LONG).show()
            }
            .setNegativeButton("稍后") { _, _ -> }
            .setCancelable(true)
            .create()
        
        dialog.show()
    }
    
    /**
     * 智能学习系统界面
     */
    @Composable
    private fun IntelligentLearningSection() {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainer
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
            ) {
                Text(
                    text = "🧠 智能学习系统",
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                Text(
                    text = "系统会记录您的手动调节习惯，自动学习您的偏好并推荐个性化亮度设置",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                // 学习开关
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "启用智能学习",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = "记录您的手动调节习惯",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    Switch(
                        checked = isLearningEnabled.value,
                        onCheckedChange = { enabled ->
                            toggleLearning(enabled)
                        },
                        modifier = Modifier.size(60.dp, 32.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 自动应用开关
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "自动应用学习结果",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = "自动使用学习到的个性化亮度",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    Switch(
                        checked = isAutoApplyLearningEnabled.value,
                        onCheckedChange = { enabled ->
                            toggleAutoApplyLearning(enabled)
                        },
                        enabled = isLearningEnabled.value,
                        modifier = Modifier.size(60.dp, 32.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 原生亮度对比开关
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "原生亮度对比模式",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = "护眼亮度比原生系统亮度低30%",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    Switch(
                        checked = isNativeBrightnessComparisonEnabled.value,
                        onCheckedChange = { enabled ->
                            toggleNativeBrightnessComparison(enabled)
                        },
                        modifier = Modifier.size(60.dp, 32.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                if (isLearningEnabled.value) {
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 学习统计信息
                    Surface(
                        color = MaterialTheme.colorScheme.primaryContainer,
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = brightnessController.getLearningStats(),
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                            

                        }
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 学习功能按钮
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        OutlinedButton(
                            onClick = {
                                showLearningAdviceDialog()
                            },
                            modifier = Modifier.weight(1f).height(48.dp)
                        ) {
                            Text("学习建议", style = MaterialTheme.typography.labelMedium)
                        }
                        
                        Button(
                            onClick = {
                                applyLearningRecommendations()
                            },
                            modifier = Modifier.weight(1f).height(48.dp),
                            enabled = brightnessController.getHighConfidenceScenariosCount() > 0
                        ) {
                            Text("应用学习", style = MaterialTheme.typography.labelMedium)
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        OutlinedButton(
                            onClick = {
                                showDetailedLearningReport()
                            },
                            modifier = Modifier.weight(1f).height(48.dp)
                        ) {
                            Text("详细报告", style = MaterialTheme.typography.labelMedium)
                        }
                        
                        OutlinedButton(
                            onClick = {
                                showClearLearningDataDialog()
                            },
                            modifier = Modifier.weight(1f).height(48.dp),
                            colors = ButtonDefaults.outlinedButtonColors(
                                contentColor = MaterialTheme.colorScheme.error
                            )
                        ) {
                            Text("清除数据", style = MaterialTheme.typography.labelMedium)
                        }
                    }
                    

                }
            }
        }
    }
    
    /**
     * 更新学习系统状态
     */
    private fun updateLearningStatus() {
        isLearningEnabled.value = brightnessController.isLearningEnabled()
        isAutoApplyLearningEnabled.value = brightnessController.isAutoApplyLearningEnabled()
        isSystemBrightnessMonitoringEnabled.value = brightnessController.isSystemBrightnessMonitoringActive()
        isNativeBrightnessComparisonEnabled.value = EyeCareSettingsManager.isNativeBrightnessComparisonEnabled(this)
    }
    
    /**
     * 切换智能学习状态
     */
    private fun toggleLearning(enabled: Boolean) {
        brightnessController.setLearningEnabled(enabled)
        isLearningEnabled.value = enabled
        saveUserSettings()
        
        val message = if (enabled) {
            "智能学习已启用，系统将记录您的手动调节习惯"
        } else {
            "智能学习已禁用"
        }
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 切换自动应用学习结果状态
     */
    private fun toggleAutoApplyLearning(enabled: Boolean) {
        brightnessController.setAutoApplyLearningEnabled(enabled)
        isAutoApplyLearningEnabled.value = enabled
        saveUserSettings()
        
        val message = if (enabled) {
            "自动应用学习结果已启用，系统将使用学习到的个性化亮度"
        } else {
            "自动应用学习结果已禁用，您可以手动应用学习建议"
        }
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 切换系统亮度监听状态
     */
    private fun toggleSystemBrightnessMonitoring(enabled: Boolean) {
        if (enabled) {
            brightnessController.enableSystemBrightnessMonitoring(lightSensorManager)
        } else {
            brightnessController.disableSystemBrightnessMonitoring()
        }
        
        isSystemBrightnessMonitoringEnabled.value = enabled
        saveUserSettings()
        
        val message = if (enabled) {
            "系统亮度监听已启用，现在可以通过系统界面调节亮度来进行学习"
        } else {
            "系统亮度监听已禁用"
        }
        
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 🆕 切换原生亮度对比模式状态
     */
    private fun toggleNativeBrightnessComparison(enabled: Boolean) {
        isNativeBrightnessComparisonEnabled.value = enabled
        saveUserSettings()
        
        val message = if (enabled) {
            "原生亮度对比模式已启用，护眼亮度将比原生系统亮度低30%"
        } else {
            "原生亮度对比模式已禁用，使用标准护眼亮度算法"
        }
        
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 显示系统亮度监听使用提示
     */
    private fun showSystemBrightnessMonitoringTips() {
        val dialog = AlertDialog.Builder(this)
            .setTitle("📱 系统亮度调节使用提示")
            .setMessage(brightnessController.getSystemBrightnessMonitoringTips())
            .setPositiveButton("我知道了", null)
            .setNeutralButton("查看统计") { _, _ ->
                showSystemBrightnessMonitoringStats()
            }
            .create()
        
        dialog.show()
    }
    
    /**
     * 显示系统亮度监听详细统计
     */
    private fun showSystemBrightnessMonitoringStats() {
        val dialog = AlertDialog.Builder(this)
            .setTitle("📊 系统亮度监听统计")
            .setMessage(brightnessController.getSystemBrightnessMonitoringStats())
            .setPositiveButton("确定", null)
            .setNegativeButton("重置统计") { _, _ ->
                brightnessController.resetSystemBrightnessMonitoringStats()
                Toast.makeText(this, "系统亮度监听统计已重置", Toast.LENGTH_SHORT).show()
            }
            .create()
        
        dialog.show()
    }
    
    /**
     * 显示学习建议对话框
     */
    private fun showLearningAdviceDialog() {
        val advice = brightnessController.getLearningUsageAdvice()
        
        AlertDialog.Builder(this)
            .setTitle("💡 智能学习建议")
            .setMessage(advice)
            .setPositiveButton("了解") { _, _ -> }
            .setNeutralButton("查看报告") { _, _ ->
                showDetailedLearningReport()
            }
            .show()
    }
    
    /**
     * 应用学习建议
     */
    private fun applyLearningRecommendations() {
        try {
            val success = brightnessController.applyLearningRecommendation()
            
            if (success) {
                // 更新个性化设置状态
                isPersonalizedBrightnessEnabled.value = true
                
                Toast.makeText(
                    this,
                    "学习建议已应用到个性化设置！请在'高级设置'中查看详情",
                    Toast.LENGTH_LONG
                ).show()
                
                Log.d("Learning", "学习建议应用成功")
            } else {
                Toast.makeText(
                    this,
                    "暂无可应用的学习建议，请继续使用手动调节功能",
                    Toast.LENGTH_SHORT
                ).show()
            }
        } catch (e: Exception) {
            Log.e("Learning", "应用学习建议失败: ${e.message}")
            Toast.makeText(this, "应用学习建议时出错，请稍后重试", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 显示详细学习报告
     */
    private fun showDetailedLearningReport() {
        val report = brightnessController.getDetailedLearningReport()
        
        AlertDialog.Builder(this)
            .setTitle("📋 详细学习报告")
            .setMessage(report)
            .setPositiveButton("确定") { _, _ -> }
            .setNeutralButton("应用建议") { _, _ ->
                applyLearningRecommendations()
            }
            .show()
    }
    
    /**
     * 显示清除学习数据确认对话框
     */
    private fun showClearLearningDataDialog() {
        AlertDialog.Builder(this)
            .setTitle("⚠️ 清除学习数据")
            .setMessage("""
                确定要清除所有学习数据吗？
                
                这将删除：
                • 所有调节记录
                • 学习到的偏好设置
                • 统计和分析数据
                
                此操作不可撤销！
            """.trimIndent())
            .setPositiveButton("确定清除") { _, _ ->
                clearLearningData()
            }
            .setNegativeButton("取消") { _, _ -> }
            .show()
    }
    
    /**
     * 清除学习数据
     */
    private fun clearLearningData() {
        try {
            brightnessController.clearLearningData()
            updateLearningStatus()
            
            Toast.makeText(this, "学习数据已清除，可以重新开始学习您的习惯", Toast.LENGTH_SHORT).show()
            Log.d("Learning", "学习数据已清除")
        } catch (e: Exception) {
            Log.e("Learning", "清除学习数据失败: ${e.message}")
            Toast.makeText(this, "清除学习数据时出错，请稍后重试", Toast.LENGTH_SHORT).show()
        }
    }
}

// 扩展函数，为 BrightnessController 添加公共访问方法
private fun BrightnessController.getMinBrightness(): Float {
    return when (getCurrentEyeCareMode()) {
        BrightnessController.EyeCareMode.NIGHT -> BrightnessController.ULTRA_LOW_BRIGHTNESS
        BrightnessController.EyeCareMode.ULTRA_SENSITIVE -> BrightnessController.ULTRA_LOW_BRIGHTNESS
        else -> BrightnessController.MIN_BRIGHTNESS
    }
}

private fun BrightnessController.getMaxBrightness(): Float {
    return when (getCurrentEyeCareMode()) {
        BrightnessController.EyeCareMode.NIGHT -> BrightnessController.NIGHT_MODE_MAX_BRIGHTNESS
        BrightnessController.EyeCareMode.ULTRA_SENSITIVE -> BrightnessController.INDOOR_MAX_BRIGHTNESS
        else -> BrightnessController.MAX_BRIGHTNESS
    }
}