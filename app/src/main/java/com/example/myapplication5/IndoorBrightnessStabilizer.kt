package com.example.myapplication5

import android.content.Context
import android.util.Log
import kotlinx.coroutines.*
import kotlin.math.abs

/**
 * 室内亮度稳定器
 * 
 * 专门解决室内环境下亮度异常下滑问题
 * 
 * 核心功能：
 * 1. 室内环境检测和保护
 * 2. 传感器数据异常过滤
 * 3. 亮度下滑防护机制
 * 4. 服务状态异常恢复
 * 5. 超强光模式误触发防护
 * 
 * 解决问题：
 * - 室内环境下亮度突然下滑到最低值
 * - 传感器数据跳变导致的亮度异常
 * - 服务重启后的状态丢失
 * - 线程安全机制导致的调节异常
 * 
 * <AUTHOR> v1.0
 * @since 2.9 - 解决室内亮度稳定性问题
 */
object IndoorBrightnessStabilizer {
    
    private const val TAG = "IndoorBrightnessStabilizer"
    
    /** 室内环境光照阈值 */
    private const val INDOOR_MAX_LUX = 500.0f              // 500 lux
    /** 室内稳定环境阈值 */
    private const val INDOOR_STABLE_LUX = 200.0f           // 200 lux
    /** 室内最低光照阈值 */
    private const val INDOOR_MIN_LUX = 10.0f               // 10 lux
    
    /** 室内最低安全亮度 */
    private const val INDOOR_MIN_SAFE_BRIGHTNESS = 0.05f   // 5%
    /** 室内标准亮度 */
    private const val INDOOR_STANDARD_BRIGHTNESS = 0.15f   // 15%
    /** 室内最大亮度 */
    private const val INDOOR_MAX_BRIGHTNESS = 0.40f        // 40%
    
    /** 传感器数据异常阈值 */
    private const val SENSOR_ANOMALY_THRESHOLD = 1.0f      // 1 lux
    /** 亮度下滑检测阈值 */
    private const val BRIGHTNESS_DROP_THRESHOLD = 0.10f    // 10%
    /** 连续异常检测次数 */
    private const val MAX_ANOMALY_COUNT = 3
    
    // 状态跟踪
    private var isIndoorMode = false
    private var lastValidLightLevel = 50.0f
    private var lastValidBrightness = 0.15f
    private var consecutiveAnomalies = 0
    private var lastStabilityCheck = 0L
    private var indoorModeStartTime = 0L
    
    // 数据历史
    private val lightLevelHistory = mutableListOf<Float>()
    private val brightnessHistory = mutableListOf<Float>()
    private val maxHistorySize = 10
    
    /**
     * 检测并启用室内稳定模式
     * 
     * @param lightLevel 当前光照强度
     * @return 是否处于室内模式
     */
    fun detectAndEnableIndoorMode(lightLevel: Float): Boolean {
        val currentTime = System.currentTimeMillis()
        val wasIndoorMode = isIndoorMode
        
        // 检测室内环境
        isIndoorMode = lightLevel <= INDOOR_MAX_LUX
        
        if (isIndoorMode && !wasIndoorMode) {
            indoorModeStartTime = currentTime
            Log.i(TAG, "启用室内稳定模式: ${lightLevel.toInt()} lux")
        } else if (!isIndoorMode && wasIndoorMode) {
            Log.i(TAG, "退出室内稳定模式")
            resetStabilizer()
        }
        
        if (isIndoorMode) {
            // 更新历史数据
            updateHistory(lightLevel)
            
            // 检查数据稳定性
            checkDataStability(lightLevel, currentTime)
        }
        
        return isIndoorMode
    }
    
    /**
     * 过滤传感器异常数据
     * 
     * @param rawLightLevel 原始光照数据
     * @return 过滤后的光照数据
     */
    fun filterSensorData(rawLightLevel: Float): Float {
        if (!isIndoorMode) return rawLightLevel
        
        // 检测异常数据
        val isAnomalous = detectSensorAnomaly(rawLightLevel)
        
        if (isAnomalous) {
            consecutiveAnomalies++
            Log.w(TAG, "检测到传感器异常数据: ${rawLightLevel} lux (第${consecutiveAnomalies}次)")
            
            if (consecutiveAnomalies >= MAX_ANOMALY_COUNT) {
                Log.e(TAG, "传感器连续异常，使用稳定值")
                return getStableLightLevel()
            }
            
            // 使用上次有效值
            return lastValidLightLevel
        } else {
            // 数据正常，重置异常计数
            consecutiveAnomalies = 0
            lastValidLightLevel = rawLightLevel
            return rawLightLevel
        }
    }
    
    /**
     * 检测传感器数据异常
     */
    private fun detectSensorAnomaly(lightLevel: Float): Boolean {
        // 检查是否为异常低值
        if (lightLevel < SENSOR_ANOMALY_THRESHOLD) {
            return true
        }
        
        // 检查是否与历史数据差异过大
        if (lightLevelHistory.isNotEmpty()) {
            val avgHistory = lightLevelHistory.average().toFloat()
            val deviation = abs(lightLevel - avgHistory)
            
            // 如果偏差超过历史平均值的80%，认为异常
            if (deviation > avgHistory * 0.8f && lightLevel < avgHistory * 0.2f) {
                return true
            }
        }
        
        return false
    }
    
    /**
     * 防护亮度下滑
     * 
     * @param targetBrightness 目标亮度
     * @param currentBrightness 当前亮度
     * @return 防护后的安全亮度
     */
    fun protectBrightnessDropping(targetBrightness: Float, currentBrightness: Float): Float {
        if (!isIndoorMode) return targetBrightness
        
        // 检测亮度下滑
        val brightnessChange = currentBrightness - targetBrightness
        val isSignificantDrop = brightnessChange > BRIGHTNESS_DROP_THRESHOLD
        
        if (isSignificantDrop && targetBrightness < INDOOR_MIN_SAFE_BRIGHTNESS) {
            Log.w(TAG, "检测到室内亮度异常下滑: ${(currentBrightness*100).toInt()}% -> ${(targetBrightness*100).toInt()}%")
            
            // 使用安全亮度
            val safeBrightness = getSafeIndoorBrightness(currentBrightness)
            Log.i(TAG, "应用室内安全亮度: ${(safeBrightness*100).toInt()}%")
            
            return safeBrightness
        }
        
        // 确保不低于室内最低安全亮度
        val protectedBrightness = targetBrightness.coerceAtLeast(INDOOR_MIN_SAFE_BRIGHTNESS)
        
        if (protectedBrightness != targetBrightness) {
            Log.d(TAG, "室内亮度保护: ${(targetBrightness*100).toInt()}% -> ${(protectedBrightness*100).toInt()}%")
        }
        
        // 更新亮度历史
        updateBrightnessHistory(protectedBrightness)
        
        return protectedBrightness
    }
    
    /**
     * 获取安全的室内亮度
     */
    private fun getSafeIndoorBrightness(currentBrightness: Float): Float {
        return when {
            // 如果当前亮度合理，保持不变
            currentBrightness >= INDOOR_MIN_SAFE_BRIGHTNESS -> currentBrightness
            
            // 如果有历史亮度记录，使用平均值
            brightnessHistory.isNotEmpty() -> {
                val avgBrightness = brightnessHistory.average().toFloat()
                avgBrightness.coerceIn(INDOOR_MIN_SAFE_BRIGHTNESS, INDOOR_MAX_BRIGHTNESS)
            }
            
            // 使用上次有效亮度
            lastValidBrightness >= INDOOR_MIN_SAFE_BRIGHTNESS -> lastValidBrightness
            
            // 默认使用标准室内亮度
            else -> INDOOR_STANDARD_BRIGHTNESS
        }
    }
    
    /**
     * 检查超强光模式误触发
     */
    fun checkOutdoorModeMistrigger(lightLevel: Float): Boolean {
        if (!isIndoorMode) return false
        
        // 如果在室内环境下检测到超强光模式被激活，认为是误触发
        val isOutdoorModeActive = OutdoorUltraBrightnessEnhancer.isInUltraBrightMode()
        
        if (isOutdoorModeActive && lightLevel <= INDOOR_MAX_LUX) {
            Log.w(TAG, "检测到超强光模式在室内环境下误触发: ${lightLevel.toInt()} lux")
            
            // 重置超强光模式
            OutdoorUltraBrightnessEnhancer.reset()
            Log.i(TAG, "已重置超强光模式状态")
            
            return true
        }
        
        return false
    }
    
    /**
     * 更新光照历史数据
     */
    private fun updateHistory(lightLevel: Float) {
        lightLevelHistory.add(lightLevel)
        if (lightLevelHistory.size > maxHistorySize) {
            lightLevelHistory.removeAt(0)
        }
    }
    
    /**
     * 更新亮度历史数据
     */
    private fun updateBrightnessHistory(brightness: Float) {
        lastValidBrightness = brightness
        brightnessHistory.add(brightness)
        if (brightnessHistory.size > maxHistorySize) {
            brightnessHistory.removeAt(0)
        }
    }
    
    /**
     * 检查数据稳定性
     */
    private fun checkDataStability(lightLevel: Float, currentTime: Long) {
        lastStabilityCheck = currentTime
        
        // 检查光照数据稳定性
        if (lightLevelHistory.size >= 5) {
            val variance = calculateVariance(lightLevelHistory)
            if (variance > 100.0f) { // 方差过大表示数据不稳定
                Log.w(TAG, "室内光照数据不稳定，方差: $variance")
            }
        }
    }
    
    /**
     * 计算方差
     */
    private fun calculateVariance(data: List<Float>): Float {
        if (data.isEmpty()) return 0f
        
        val mean = data.average().toFloat()
        val variance = data.map { (it - mean) * (it - mean) }.average().toFloat()
        return variance
    }
    
    /**
     * 获取稳定的光照值
     */
    private fun getStableLightLevel(): Float {
        return when {
            lightLevelHistory.isNotEmpty() -> {
                // 使用历史数据的中位数
                val sortedHistory = lightLevelHistory.sorted()
                sortedHistory[sortedHistory.size / 2]
            }
            lastValidLightLevel > INDOOR_MIN_LUX -> lastValidLightLevel
            else -> 50.0f // 默认室内光照值
        }
    }
    
    /**
     * 获取室内稳定器状态报告
     */
    fun getStabilizerStatus(): String {
        val currentTime = System.currentTimeMillis()
        val indoorDuration = if (isIndoorMode) {
            (currentTime - indoorModeStartTime) / 1000
        } else 0L
        
        return """
            室内亮度稳定器状态:
            ====================
            当前模式: ${if (isIndoorMode) "室内稳定模式" else "非室内环境"}
            连续异常: ${consecutiveAnomalies}次
            最后有效光照: ${lastValidLightLevel.toInt()} lux
            最后有效亮度: ${(lastValidBrightness*100).toInt()}%
            历史数据量: ${lightLevelHistory.size}/${maxHistorySize}
            室内持续: ${indoorDuration}秒
            
            保护功能: ${if (isIndoorMode) "已启用" else "待机中"}
            - 传感器异常过滤: ✅
            - 亮度下滑防护: ✅
            - 超强光误触发检测: ✅
            - 数据稳定性监控: ✅
        """.trimIndent()
    }
    
    /**
     * 重置稳定器状态
     */
    fun resetStabilizer() {
        consecutiveAnomalies = 0
        lightLevelHistory.clear()
        brightnessHistory.clear()
        lastStabilityCheck = 0L
        indoorModeStartTime = 0L
        Log.d(TAG, "室内亮度稳定器状态已重置")
    }
    
    /**
     * 是否处于室内模式
     */
    fun isInIndoorMode(): Boolean = isIndoorMode
    
    /**
     * 获取室内安全亮度范围
     */
    fun getIndoorSafeBrightnessRange(): Pair<Float, Float> {
        return Pair(INDOOR_MIN_SAFE_BRIGHTNESS, INDOOR_MAX_BRIGHTNESS)
    }
}
