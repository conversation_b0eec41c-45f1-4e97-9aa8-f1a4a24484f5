package com.example.myapplication5

import android.content.Context
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.util.*

/**
 * 护眼健康分析管理器 - 数据分析和健康评分系统
 * 
 * 核心功能：
 * 1. 计算护眼健康评分
 * 2. 统计护眼使用时长
 * 3. 分析护眼习惯
 * 4. 生成健康报告
 * 5. 提供改善建议
 * 
 * 评分算法：
 * - 基于使用时长、模式选择、调节频率等因素
 * - 考虑时间分布、环境适应性等
 * - 动态调整评分权重
 * 
 * <AUTHOR> v1.0
 * @since 1.0 - 支持护眼健康评分和分析
 */
object EyeHealthAnalyticsManager {
    
    private const val TAG = "EyeHealthAnalytics"
    private const val PREFS_NAME = "eye_health_analytics"
    private const val KEY_DAILY_STATS = "daily_statistics"
    private const val KEY_HEALTH_SCORE = "health_score"
    private const val KEY_USAGE_DURATION = "usage_duration"
    
    /**
     * 每日统计数据
     */
    data class DailyStatistics(
        val date: String,                           // 日期 (yyyy-MM-dd)
        val totalDuration: Long,                    // 总使用时长（毫秒）
        val nightModeDuration: Long,                // 夜间模式使用时长
        val ultraSensitiveDuration: Long,           // 超敏感模式使用时长
        val standardModeDuration: Long,             // 标准模式使用时长
        val adjustmentCount: Int,                   // 调节次数
        val averageBrightness: Float,               // 平均亮度
        val lightLevelChanges: Int,                 // 光照变化次数
        val healthScore: Int,                       // 当日健康评分
        val eyeStrainLevel: Int,                    // 眼疲劳程度 (1-10)
        val recommendations: List<String>           // 建议列表
    ) {
        /**
         * 计算护眼模式分布百分比
         */
        fun getModeDistribution(): Map<String, Float> {
            val total = totalDuration.toFloat()
            if (total == 0f) return mapOf(
                "标准模式" to 0f,
                "夜间模式" to 0f,
                "超敏感模式" to 0f
            )
            
            return mapOf(
                "标准模式" to (standardModeDuration / total * 100),
                "夜间模式" to (nightModeDuration / total * 100),
                "超敏感模式" to (ultraSensitiveDuration / total * 100)
            )
        }
        
        /**
         * 获取主要使用模式
         */
        fun getPrimaryMode(): String {
            val distribution = getModeDistribution()
            return distribution.maxByOrNull { it.value }?.key ?: "标准模式"
        }
        
        /**
         * 检查是否过度使用
         */
        fun isOveruse(): Boolean {
            val hoursUsed = totalDuration / (1000 * 60 * 60)
            return hoursUsed > 12 // 超过12小时认为过度使用
        }
        
        /**
         * 检查夜间使用情况
         */
        fun hasNightUsage(): Boolean {
            return nightModeDuration > 0
        }
    }
    
    /**
     * 护眼健康评分计算器
     */
    data class HealthScoreCalculator(
        val dailyStats: DailyStatistics,
        val previousScores: List<Int> = emptyList()
    ) {
        /**
         * 计算综合健康评分
         */
        fun calculateHealthScore(): Int {
            var score = 0
            
            // 基础分数 (0-40分)
            score += calculateBaseScore()
            
            // 使用时长分数 (0-20分)
            score += calculateDurationScore()
            
            // 模式选择分数 (0-20分)
            score += calculateModeScore()
            
            // 调节频率分数 (0-10分)
            score += calculateAdjustmentScore()
            
            // 时间分布分数 (0-10分)
            score += calculateTimeDistributionScore()
            
            // 确保分数在0-100范围内
            return score.coerceIn(0, 100)
        }
        
        /**
         * 计算基础分数
         */
        private fun calculateBaseScore(): Int {
            return when {
                dailyStats.eyeStrainLevel <= 3 -> 40
                dailyStats.eyeStrainLevel <= 5 -> 30
                dailyStats.eyeStrainLevel <= 7 -> 20
                else -> 10
            }
        }
        
        /**
         * 计算使用时长分数
         */
        private fun calculateDurationScore(): Int {
            val hoursUsed = dailyStats.totalDuration.toDouble() / (1000 * 60 * 60)
            return when {
                hoursUsed in 2.0..8.0 -> 20  // 理想使用时长
                hoursUsed in 1.0..10.0 -> 15 // 可接受范围
                hoursUsed > 12.0 -> 5        // 过度使用
                else -> 10                   // 使用不足
            }
        }
        
        /**
         * 计算模式选择分数
         */
        private fun calculateModeScore(): Int {
            var score = 0
            
            // 夜间模式使用加分
            if (dailyStats.hasNightUsage()) {
                score += 10
            }
            
            // 模式多样性加分
            val activeModes = listOf(
                dailyStats.standardModeDuration > 0,
                dailyStats.nightModeDuration > 0,
                dailyStats.ultraSensitiveDuration > 0
            ).count { it }
            
            score += when (activeModes) {
                3 -> 10  // 使用所有模式
                2 -> 7   // 使用两种模式
                else -> 3 // 只使用一种模式
            }
            
            return score
        }
        
        /**
         * 计算调节频率分数
         */
        private fun calculateAdjustmentScore(): Int {
            val hoursUsed = dailyStats.totalDuration.toDouble() / (1000 * 60 * 60)
            if (hoursUsed == 0.0) return 0
            
            val adjustmentsPerHour = dailyStats.adjustmentCount / hoursUsed
            return when {
                adjustmentsPerHour <= 5 -> 10   // 调节频率适中
                adjustmentsPerHour <= 10 -> 7   // 调节频率较高
                adjustmentsPerHour <= 20 -> 4   // 调节频率过高
                else -> 1                       // 调节频率过高
            }
        }
        
        /**
         * 计算时间分布分数
         */
        private fun calculateTimeDistributionScore(): Int {
            // 检查是否有夜间使用
            if (dailyStats.hasNightUsage()) {
                return 10
            }
            
            // 检查是否过度使用
            if (dailyStats.isOveruse()) {
                return 3
            }
            
            return 7
        }
    }
    
    /**
     * 获取今日统计数据
     */
    fun getTodayStatistics(context: Context): DailyStatistics {
        val today = getCurrentDate()
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val json = prefs.getString(KEY_DAILY_STATS, "{}")
        
        return try {
            val type = object : TypeToken<Map<String, DailyStatistics>>() {}.type
            val statsMap: Map<String, DailyStatistics> = Gson().fromJson(json, type) ?: emptyMap()
            statsMap[today] ?: createDefaultDailyStatistics(today)
        } catch (e: Exception) {
            Log.e(TAG, "获取今日统计数据失败: ${e.message}")
            createDefaultDailyStatistics(today)
        }
    }
    
    /**
     * 更新今日统计数据
     */
    fun updateTodayStatistics(context: Context, stats: DailyStatistics) {
        try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val json = prefs.getString(KEY_DAILY_STATS, "{}")
            
            val type = object : TypeToken<Map<String, DailyStatistics>>() {}.type
            val statsMap: MutableMap<String, DailyStatistics> = Gson().fromJson(json, type) ?: mutableMapOf()
            
            statsMap[stats.date] = stats
            
            val updatedJson = Gson().toJson(statsMap)
            prefs.edit().putString(KEY_DAILY_STATS, updatedJson).apply()
            
            Log.d(TAG, "今日统计数据已更新: ${stats.date}")
        } catch (e: Exception) {
            Log.e(TAG, "更新今日统计数据失败: ${e.message}")
        }
    }
    
    /**
     * 计算护眼健康评分
     */
    fun calculateHealthScore(context: Context): Int {
        val todayStats = getTodayStatistics(context)
        val previousScores = getPreviousScores(context)
        
        val calculator = HealthScoreCalculator(todayStats, previousScores)
        val score = calculator.calculateHealthScore()
        
        // 保存评分
        saveHealthScore(context, score)
        
        return score
    }
    
    /**
     * 获取护眼使用时长
     */
    fun getUsageDuration(context: Context): Long {
        val todayStats = getTodayStatistics(context)
        return todayStats.totalDuration
    }
    
    /**
     * 生成护眼建议
     */
    fun generateRecommendations(context: Context): List<String> {
        val todayStats = getTodayStatistics(context)
        val recommendations = mutableListOf<String>()
        
        // 检查使用时长
        val hoursUsed = todayStats.totalDuration / (1000 * 60 * 60)
        if (hoursUsed > 8) {
            recommendations.add("建议减少屏幕使用时间，每小时休息5-10分钟")
        } else if (hoursUsed < 2) {
            recommendations.add("护眼应用使用时间较短，建议适当增加使用时间")
        }
        
        // 检查夜间使用
        if (!todayStats.hasNightUsage()) {
            recommendations.add("建议在夜间使用夜间模式，减少蓝光对眼睛的伤害")
        }
        
        // 检查调节频率
        if (hoursUsed > 0) {
            val adjustmentsPerHour = todayStats.adjustmentCount / hoursUsed
            if (adjustmentsPerHour > 15) {
                recommendations.add("亮度调节过于频繁，建议使用自动调节模式")
            }
        }
        
        // 检查眼疲劳程度
        when {
            todayStats.eyeStrainLevel > 7 -> {
                recommendations.add("眼疲劳程度较高，建议立即休息并做眼保健操")
                recommendations.add("考虑使用超敏感模式，减少对眼睛的刺激")
            }
            todayStats.eyeStrainLevel > 5 -> {
                recommendations.add("眼疲劳程度中等，建议适当休息")
            }
        }
        
        // 如果没有建议，添加正面鼓励
        if (recommendations.isEmpty()) {
            recommendations.add("护眼习惯良好，继续保持！")
        }
        
        return recommendations
    }
    
    /**
     * 获取历史评分
     */
    fun getPreviousScores(context: Context): List<Int> {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val json = prefs.getString(KEY_DAILY_STATS, "{}")
        
        return try {
            val type = object : TypeToken<Map<String, DailyStatistics>>() {}.type
            val statsMap: Map<String, DailyStatistics> = Gson().fromJson(json, type) ?: emptyMap()
            
            // 获取最近7天的评分
            val recentDates = getRecentDates(7)
            recentDates.mapNotNull { date ->
                statsMap[date]?.healthScore
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取历史评分失败: ${e.message}")
            emptyList()
        }
    }
    
    /**
     * 创建默认每日统计数据
     */
    private fun createDefaultDailyStatistics(date: String): DailyStatistics {
        return DailyStatistics(
            date = date,
            totalDuration = 0L,
            nightModeDuration = 0L,
            ultraSensitiveDuration = 0L,
            standardModeDuration = 0L,
            adjustmentCount = 0,
            averageBrightness = 0.5f,
            lightLevelChanges = 0,
            healthScore = 85,
            eyeStrainLevel = 3,
            recommendations = listOf("开始使用护眼应用，保护眼睛健康")
        )
    }
    
    /**
     * 获取当前日期
     */
    private fun getCurrentDate(): String {
        val calendar = Calendar.getInstance()
        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH) + 1
        val day = calendar.get(Calendar.DAY_OF_MONTH)
        return String.format("%04d-%02d-%02d", year, month, day)
    }
    
    /**
     * 获取最近N天的日期
     */
    private fun getRecentDates(days: Int): List<String> {
        val dates = mutableListOf<String>()
        val calendar = Calendar.getInstance()
        
        for (i in 0 until days) {
            calendar.add(Calendar.DAY_OF_MONTH, -i)
            val year = calendar.get(Calendar.YEAR)
            val month = calendar.get(Calendar.MONTH) + 1
            val day = calendar.get(Calendar.DAY_OF_MONTH)
            dates.add(String.format("%04d-%02d-%02d", year, month, day))
            calendar.add(Calendar.DAY_OF_MONTH, i) // 重置日期
        }
        
        return dates
    }
    
    /**
     * 保存健康评分
     */
    private fun saveHealthScore(context: Context, score: Int) {
        try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit().putInt(KEY_HEALTH_SCORE, score).apply()
        } catch (e: Exception) {
            Log.e(TAG, "保存健康评分失败: ${e.message}")
        }
    }
} 