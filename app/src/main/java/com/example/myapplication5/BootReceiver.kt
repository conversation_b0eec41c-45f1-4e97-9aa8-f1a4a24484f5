package com.example.myapplication5

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Build
import android.util.Log

/**
 * 开机自启接收器 - 护眼服务自动启动
 * 
 * 功能：
 * 1. 监听系统开机完成广播
 * 2. 检查用户是否启用了开机自启
 * 3. 自动启动护眼后台服务
 * 4. 恢复用户的护眼设置
 * 
 * <AUTHOR>
 * @since 2.1
 */
class BootReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "BootReceiver"
        const val PREFS_NAME = "eye_care_settings"
        const val KEY_AUTO_START = "auto_start_enabled"
        const val KEY_EYE_CARE_MODE = "eye_care_mode"
        const val KEY_AUTO_ADJUSTMENT = "auto_adjustment_enabled"
        const val KEY_BRIGHTNESS_OFFSET = "brightness_offset"
        const val KEY_USE_GREEN_THEME = "use_green_theme"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.action
        Log.d(TAG, "收到广播: $action")
        
        when (action) {
            Intent.ACTION_BOOT_COMPLETED,
            "android.intent.action.QUICKBOOT_POWERON",
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED,
            "android.intent.action.LOCKED_BOOT_COMPLETED" -> {
                Log.d(TAG, "收到开机事件: $action")
                handleBootCompleted(context)
            }
            else -> {
                Log.d(TAG, "收到其他广播: $action")
            }
        }
    }
    
    /**
     * 处理开机完成事件
     */
    private fun handleBootCompleted(context: Context) {
        try {
            Log.d(TAG, "开始处理开机完成事件")
            
            // 使用新的设置管理器
            val autoStartEnabled = EyeCareSettingsManager.isAutoStartEnabled(context)
            
            if (!autoStartEnabled) {
                Log.d(TAG, "开机自启已禁用，跳过启动服务")
                return
            }
            
            // 恢复用户设置
            val eyeCareMode = EyeCareSettingsManager.getEyeCareMode(context)
            val autoAdjustment = EyeCareSettingsManager.isAutoAdjustmentEnabled(context)
            val backgroundServiceEnabled = EyeCareSettingsManager.isBackgroundServiceEnabled(context)
            
            Log.d(TAG, "恢复护眼设置: 模式=$eyeCareMode, 自动调节=$autoAdjustment, 后台服务=$backgroundServiceEnabled")
            
            // 延迟启动服务，确保系统完全启动
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                try {
                    // 只有启用了后台服务才启动
                    if (backgroundServiceEnabled) {
                        startEyeCareService(context, eyeCareMode, autoAdjustment)
                    }
                    
                    // 发送开机启动通知
                    sendBootNotification(context)
                    
                } catch (e: Exception) {
                    Log.e(TAG, "延迟启动服务失败: ${e.message}", e)
                }
            }, 10000) // 延迟10秒启动
            
        } catch (e: Exception) {
            Log.e(TAG, "开机启动处理失败: ${e.message}", e)
        }
    }
    
    /**
     * 启动护眼服务
     */
    private fun startEyeCareService(
        context: Context,
        eyeCareMode: String?,
        autoAdjustment: Boolean
    ) {
        try {
            Log.d(TAG, "准备启动护眼服务: 模式=$eyeCareMode, 自动调节=$autoAdjustment")
            
            val serviceIntent = Intent(context, EyeCareBackgroundService::class.java).apply {
                putExtra("eye_care_mode", eyeCareMode ?: "STANDARD")
                putExtra("auto_adjustment", autoAdjustment)
                putExtra("start_reason", "boot_completed")
                // 添加标志确保服务能正确启动
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            
            // Android 8.0+ 需要使用 startForegroundService
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                try {
                    context.startForegroundService(serviceIntent)
                    Log.d(TAG, "使用 startForegroundService 启动成功")
                } catch (e: Exception) {
                    Log.w(TAG, "startForegroundService 失败，尝试 startService: ${e.message}")
                    context.startService(serviceIntent)
                }
            } else {
                context.startService(serviceIntent)
                Log.d(TAG, "使用 startService 启动成功")
            }
            
            Log.d(TAG, "护眼后台服务启动成功")
            
        } catch (e: Exception) {
            Log.e(TAG, "启动护眼服务失败: ${e.message}", e)
        }
    }
    
    /**
     * 发送开机启动通知
     */
    private fun sendBootNotification(context: Context) {
        try {
            // 记录开机启动日志
            Log.d(TAG, "护眼保护已自动启动")
            
            // 保存开机启动记录
            val prefs = context.getSharedPreferences("boot_log", Context.MODE_PRIVATE)
            prefs.edit().apply {
                putLong("last_boot_time", System.currentTimeMillis())
                putString("last_boot_action", "success")
                apply()
            }
            
        } catch (e: Exception) {
            Log.w(TAG, "发送开机通知失败: ${e.message}")
            
            // 记录失败日志
            val prefs = context.getSharedPreferences("boot_log", Context.MODE_PRIVATE)
            prefs.edit().apply {
                putLong("last_boot_time", System.currentTimeMillis())
                putString("last_boot_action", "failed")
                putString("last_boot_error", e.message)
                apply()
            }
        }
    }
} 