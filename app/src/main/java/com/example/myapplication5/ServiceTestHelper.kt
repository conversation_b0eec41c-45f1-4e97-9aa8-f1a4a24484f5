package com.example.myapplication5

import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log

/**
 * 服务测试助手 - 用于诊断后台服务问题
 * 
 * 功能：
 * 1. 检查服务运行状态
 * 2. 测试服务启动和停止
 * 3. 诊断权限问题
 * 4. 提供调试信息
 * 
 * <AUTHOR>
 * @since 2.4
 */
object ServiceTestHelper {
    
    private const val TAG = "ServiceTestHelper"
    
    /**
     * 测试后台服务启动
     */
    fun testServiceStart(context: Context): Boolean {
        return try {
            Log.d(TAG, "开始测试后台服务启动")
            
            // 检查权限
            if (!hasRequiredPermissions(context)) {
                Log.e(TAG, "缺少必要权限")
                return false
            }
            
            // 启动服务
            val serviceIntent = Intent(context, EyeCareBackgroundService::class.java).apply {
                putExtra("eye_care_mode", "STANDARD")
                putExtra("auto_adjustment", true)
                putExtra("test_mode", true)
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
            } else {
                context.startService(serviceIntent)
            }
            
            Log.d(TAG, "后台服务启动命令已发送")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "测试服务启动失败: ${e.message}")
            false
        }
    }
    
    /**
     * 测试后台服务停止
     */
    fun testServiceStop(context: Context): Boolean {
        return try {
            Log.d(TAG, "开始测试后台服务停止")
            
            val serviceIntent = Intent(context, EyeCareBackgroundService::class.java)
            context.stopService(serviceIntent)
            
            Log.d(TAG, "后台服务停止命令已发送")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "测试服务停止失败: ${e.message}")
            false
        }
    }
    
    /**
     * 检查服务运行状态
     */
    fun checkServiceStatus(context: Context): String {
        return try {
            val isRunning = EyeCareBackgroundService.isServiceRunning
            val hasPermission = BrightnessController(context, null).hasWriteSettingsPermission()
            val autoBrightness = BrightnessController(context, null).isSystemAutoBrightnessEnabled()
            
            """
                服务运行状态: ${if (isRunning) "正在运行" else "未运行"}
                系统设置权限: ${if (hasPermission) "已授权" else "未授权"}
                系统自动亮度: ${if (autoBrightness) "已开启（冲突）" else "已关闭（正常）"}
                后台服务设置: ${if (EyeCareSettingsManager.isBackgroundServiceEnabled(context)) "已启用" else "已禁用"}
            """.trimIndent()
            
        } catch (e: Exception) {
            "检查服务状态失败: ${e.message}"
        }
    }
    
    /**
     * 检查必要权限
     */
    private fun hasRequiredPermissions(context: Context): Boolean {
        val brightnessController = BrightnessController(context, null)
        return brightnessController.hasWriteSettingsPermission()
    }
    
    /**
     * 获取详细诊断信息
     */
    fun getDiagnosticInfo(context: Context): String {
        return try {
            val brightnessController = BrightnessController(context, null)
            val lightSensorManager = LightSensorManager(context) { }
            
            """
                === 护眼服务诊断报告 ===
                
                1. 服务状态:
                   - 后台服务运行: ${EyeCareBackgroundService.isServiceRunning}
                   - 用户设置启用: ${EyeCareSettingsManager.isBackgroundServiceEnabled(context)}
                
                2. 权限状态:
                   - 系统设置权限: ${brightnessController.hasWriteSettingsPermission()}
                   - 通知权限: 已配置
                   - 前台服务权限: 已配置
                
                3. 系统状态:
                   - 系统自动亮度: ${if (brightnessController.isSystemAutoBrightnessEnabled()) "已开启（冲突）" else "已关闭（正常）"}
                   - 亮度冲突: ${if (brightnessController.hasBrightnessConflict()) "存在冲突" else "无冲突"}
                
                4. 传感器状态:
                   - 光传感器可用: ${lightSensorManager.isLightSensorAvailable()}
                   - 传感器信息: ${lightSensorManager.getSensorInfo()}
                
                5. 亮度控制:
                   - 当前亮度: ${(brightnessController.getCurrentBrightness() * 100).toInt()}%
                   - 护眼模式: ${brightnessController.getCurrentEyeCareMode()}
                   - 亮度评级: ${brightnessController.getBrightnessEyeCareRating()}
                
                6. 用户设置:
                   - 自动调节: ${EyeCareSettingsManager.isAutoAdjustmentEnabled(context)}
                   - 护眼模式: ${EyeCareSettingsManager.getEyeCareMode(context)}
                   - 开机自启: ${EyeCareSettingsManager.isAutoStartEnabled(context)}
                
                === 建议 ===
                ${getRecommendations(context, brightnessController)}
            """.trimIndent()
            
        } catch (e: Exception) {
            "获取诊断信息失败: ${e.message}"
        }
    }
    
    /**
     * 获取建议
     */
    private fun getRecommendations(context: Context, brightnessController: BrightnessController): String {
        val recommendations = mutableListOf<String>()
        
        if (!brightnessController.hasWriteSettingsPermission()) {
            recommendations.add("• 请授予系统设置权限以启用自动亮度调节")
        }
        
        if (brightnessController.hasBrightnessConflict()) {
            recommendations.add("• 建议关闭系统自动亮度以获得最佳护眼效果")
        }
        
        if (!EyeCareBackgroundService.isServiceRunning && EyeCareSettingsManager.isBackgroundServiceEnabled(context)) {
            recommendations.add("• 后台服务未运行，请检查应用设置或重启应用")
        }
        
        if (recommendations.isEmpty()) {
            recommendations.add("• 所有设置正常，护眼功能运行良好")
        }
        
        return recommendations.joinToString("\n")
    }
    
    /**
     * 强制重启服务
     */
    fun forceRestartService(context: Context): Boolean {
        return try {
            Log.d(TAG, "强制重启后台服务")
            
            // 先停止服务
            testServiceStop(context)
            
            // 等待一秒
            Thread.sleep(1000)
            
            // 重新启动服务
            testServiceStart(context)
            
            Log.d(TAG, "服务强制重启完成")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "强制重启服务失败: ${e.message}")
            false
        }
    }

    /**
     * 测试系统设置权限
     */
    fun testWriteSettingsPermission(context: Context): String {
        val brightnessController = BrightnessController(context)
        val hasPermission = brightnessController.hasWriteSettingsPermission()
        
        // 检查应用类型
        val appTypeInfo = getAppTypeInfo(context)
        
        // 检查系统版本
        val systemInfo = getSystemInfo()
        
        // 检查权限状态
        val permissionInfo = getPermissionInfo(context)
        
        return """
            权限测试结果：
            ✅ 系统设置权限：${if (hasPermission) "已授权" else "未授权"}
            📱 权限状态：${brightnessController.getPermissionStatusDescription()}
            🔧 权限可用：${brightnessController.isPermissionAvailable()}
            
            应用类型检查：
            $appTypeInfo
            
            系统信息：
            $systemInfo
            
            权限详细信息：
            $permissionInfo
            
            测试建议：
            ${if (hasPermission) "权限正常，可以调节亮度" else getPermissionAdvice()}
        """.trimIndent()
    }
    
    /**
     * 获取应用类型信息
     */
    private fun getAppTypeInfo(context: Context): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            val applicationInfo = packageInfo.applicationInfo
            val isSystemApp = (applicationInfo?.flags ?: 0 and android.content.pm.ApplicationInfo.FLAG_SYSTEM) != 0
            val isUpdatedSystemApp = (applicationInfo?.flags ?: 0 and android.content.pm.ApplicationInfo.FLAG_UPDATED_SYSTEM_APP) != 0
            val isDebuggable = (applicationInfo?.flags ?: 0 and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE) != 0
            
            """
                应用类型：${if (isSystemApp) "系统应用" else if (isUpdatedSystemApp) "更新系统应用" else "普通应用"}
                调试模式：${if (isDebuggable) "已开启" else "未开启"}
                包名：${context.packageName}
                版本：${packageInfo.versionName} (${if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) packageInfo.longVersionCode else @Suppress("DEPRECATION") packageInfo.versionCode})
            """.trimIndent()
        } catch (e: Exception) {
            "应用类型检查失败: ${e.message}"
        }
    }
    
    /**
     * 获取系统信息
     */
    private fun getSystemInfo(): String {
        return """
            Android版本：${android.os.Build.VERSION.RELEASE} (API ${android.os.Build.VERSION.SDK_INT})
            设备型号：${android.os.Build.MODEL}
            制造商：${android.os.Build.MANUFACTURER}
            系统架构：${android.os.Build.SUPPORTED_ABIS.firstOrNull() ?: "未知"}
        """.trimIndent()
    }
    
    /**
     * 获取权限信息
     */
    private fun getPermissionInfo(context: Context): String {
        return try {
            val hasWriteSettings = context.checkSelfPermission("android.permission.WRITE_SETTINGS") == android.content.pm.PackageManager.PERMISSION_GRANTED
            val canWriteSettings = android.provider.Settings.System.canWrite(context)
            
            """
                WRITE_SETTINGS权限声明：${if (hasWriteSettings) "已声明" else "未声明"}
                Settings.System.canWrite：${if (canWriteSettings) "true" else "false"}
                权限保护级别：signature (需要系统签名)
            """.trimIndent()
        } catch (e: Exception) {
            "权限信息检查失败: ${e.message}"
        }
    }
    
    /**
     * 获取权限建议
     */
    private fun getPermissionAdvice(): String {
        return """
            解决方案：
            1. 用户授权方案：引导用户手动开启"修改系统设置"权限（推荐）
            2. 调试模式方案：在开发者选项中开启"调试应用"模式（开发测试）
            3. 系统应用方案：将应用安装到/system/app目录并使用系统签名（高级用户）
            4. 替代方案：使用窗口亮度调节（仅前台有效）
            
            推荐操作：
            - 开发测试：使用调试模式
            - 正式发布：引导用户手动授权权限
            - 系统级功能：需要系统签名和特殊安装
        """.trimIndent()
    }

    /**
     * 测试后台服务稳定性
     */
    fun testBackendServiceStability(context: Context): String {
        return try {
            val startTime = System.currentTimeMillis()
            
            // 1. 检查服务状态
            val isRunning = ServiceForceStarter.isServiceRunning(context)
            val hasPermission = ServiceForceStarter.getServiceDetails(context)
            
            // 2. 检查传感器状态
            val sensorStatus = ServiceStabilityEnhancer.checkAndRestoreSensor(context)
            
            // 3. 获取稳定性报告
            val stabilityReport = ServiceStabilityEnhancer.getStabilityReport(context)
            
            // 4. 模拟传感器失效测试
            val sensorTestResult = simulateSensorFailure(context)
            
            val endTime = System.currentTimeMillis()
            val testDuration = endTime - startTime
            
            """
                后台服务稳定性测试报告
                =========================
                测试时间: ${testDuration}ms
                
                基础状态检查:
                - 服务运行: ${if (isRunning) "正常" else "异常"}
                - 传感器状态: ${if (sensorStatus) "正常" else "异常"}
                - 权限状态: $hasPermission
                
                稳定性报告:
                $stabilityReport
                
                传感器失效模拟测试:
                $sensorTestResult
                
                测试结论:
                ${if (isRunning && sensorStatus) "✅ 后台服务稳定性良好" else "❌ 后台服务存在问题，建议使用紧急恢复"}
            """.trimIndent()
            
        } catch (e: Exception) {
            "后台服务稳定性测试失败: ${e.message}"
        }
    }
    
    /**
     * 模拟传感器失效测试
     */
    private fun simulateSensorFailure(context: Context): String {
        return try {
            // 创建临时传感器管理器
            val tempSensorManager = LightSensorManager(context) { }
            
            // 检查初始状态
            val initialStatus = tempSensorManager.isListening()
            
            // 模拟停止监听
            tempSensorManager.stopListening()
            
            // 检查停止后状态
            val stoppedStatus = tempSensorManager.isListening()
            
            // 尝试强制重启
            val restartResult = tempSensorManager.forceRestartListening()
            
            // 检查重启后状态
            val finalStatus = tempSensorManager.isListening()
            
            """
                初始监听状态: ${if (initialStatus) "正常" else "异常"}
                停止后状态: ${if (stoppedStatus) "异常" else "正常"}
                强制重启结果: ${if (restartResult) "成功" else "失败"}
                最终状态: ${if (finalStatus) "正常" else "异常"}
                
                恢复能力评估: ${if (restartResult && finalStatus) "✅ 优秀" else "❌ 需要改进"}
            """.trimIndent()
            
        } catch (e: Exception) {
            "传感器失效模拟测试异常: ${e.message}"
        }
    }
} 