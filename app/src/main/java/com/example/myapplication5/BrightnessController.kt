package com.example.myapplication5

import android.app.Activity
import android.content.Context
import android.provider.Settings
import android.util.Log
import androidx.activity.ComponentActivity
import java.util.*

/**
 * 护眼亮度控制器 - 专为干眼症患者设计
 * 
 * 核心功能：
 * 1. 极暗环境下的精细亮度控制（最低0.5%）
 * 2. 三种护眼模式：标准、夜间、超敏感
 * 3. 渐进式亮度调节，避免突然变化
 * 4. 基于时间的智能夜间模式
 * 5. 用户偏好亮度偏移
 * 6. 支持在Service中运行
 * 7. 系统自动亮度检测和控制
 * 8. 后台亮度调节优化
 * 
 * <AUTHOR> v2.3
 * @since 2.3 - 修复后台亮度调节和系统冲突问题
 */
class BrightnessController(
    private val context: Context,
    private val activity: ComponentActivity? = null  // 在Service中为null
) {
    
    companion object {
        private const val TAG = "BrightnessController"
        
        /** 超极低亮度值 - 用于干眼症患者深夜使用 */
        const val SUPER_ULTRA_LOW_BRIGHTNESS = 0.002f  // 0.2%
        /** 极低亮度值 - 用于完全黑暗环境 */
        const val ULTRA_LOW_BRIGHTNESS = 0.003f  // 0.3%
        /** 最小亮度值 - 一般暗光环境 */
        const val MIN_BRIGHTNESS = 0.01f          // 1%
        /** 最大亮度值 - 避免过亮刺激眼部 */
        const val MAX_BRIGHTNESS = 0.95f          // 95% (提高户外可视性)
        /** 默认亮度值 - 启动时的初始亮度 */
        const val DEFAULT_BRIGHTNESS = 0.25f      // 25%
        
        /** 深夜极低亮度 - 干眼症患者专用 */
        const val DEEP_NIGHT_BRIGHTNESS = 0.001f       // 0.1%
        /** 夜间模式最大亮度 - 深夜使用 */
        const val NIGHT_MODE_MAX_BRIGHTNESS = 0.12f    // 12% (从15%降低)
        /** 室内最大亮度 - 超敏感模式 */
        const val INDOOR_MAX_BRIGHTNESS = 0.55f        // 55% (提高从40%)
        /** 户外最大亮度 - 确保强光下可见 */
        const val OUTDOOR_MAX_BRIGHTNESS = 0.95f       // 95% (户外专用)
        
        /** 基础亮度平滑调节因子 - 提高响应性 */
        const val BASE_SMOOTHING_FACTOR = 0.15f        // 提高响应性 (从0.08f提高)
        /** 户外环境平滑调节因子 - 提高响应性 */
        const val OUTDOOR_SMOOTHING_FACTOR = 0.20f     // 提高响应性 (从0.05f提高)
        /** 夜间环境平滑调节因子 - 提高响应性 */
        const val NIGHT_SMOOTHING_FACTOR = 0.12f       // 提高响应性 (从0.10f提高)
        /** 极低亮度环境平滑因子 - 提高响应性 */
        const val ULTRA_LOW_SMOOTHING_FACTOR = 0.08f   // 提高响应性 (从0.03f提高)
        /** 大幅变化时的平滑因子 - 提高响应性 */
        const val LARGE_CHANGE_SMOOTHING_FACTOR = 0.10f // 提高响应性 (从0.02f提高)
        
        /** 原生亮度降低比例 - 护眼亮度比原生低30% */
        const val NATIVE_BRIGHTNESS_REDUCTION_RATIO = 0.30f  // 30%
        /** 是否启用原生亮度对比模式 */
        const val ENABLE_NATIVE_BRIGHTNESS_COMPARISON = true
        
        /** 光照强度阈值定义 */
        const val DEEP_DARK_ENVIRONMENT_LUX = 0.5f     // 深度黑暗环境阈值
        const val EXTREME_DARK_ENVIRONMENT_LUX = 2.0f  // 极暗环境阈值
        const val DARK_ENVIRONMENT_LUX = 5.0f          // 暗光环境阈值
        const val INDOOR_ENVIRONMENT_LUX = 100.0f      // 室内环境阈值
        const val OUTDOOR_ENVIRONMENT_LUX = 500.0f     // 户外环境阈值
        
        /** 亮度变化阈值 - 提高响应性 */
        const val MICRO_BRIGHTNESS_THRESHOLD = 0.002f   // 0.2% 微变化阈值 (从0.5%降低)
        const val BRIGHTNESS_CHANGE_THRESHOLD = 0.008f  // 0.8% 小变化阈值 (从1.5%降低)
        /** 户外环境亮度变化阈值 - 提高户外响应性 */
        const val OUTDOOR_BRIGHTNESS_CHANGE_THRESHOLD = 0.01f  // 1%变化阈值 (从2%降低)
        /** 大幅亮度变化阈值 - 识别环境骤变 */
        const val LARGE_BRIGHTNESS_CHANGE_THRESHOLD = 0.10f    // 10%大变化阈值 (从15%降低)
        
        /** 平滑调节的最大步长限制 - 提高响应性 */
        const val MAX_BRIGHTNESS_STEP = 0.15f          // 单次最大调节15% (从10%提高)
        const val MAX_LOW_LIGHT_STEP = 0.05f           // 低光环境最大调节5% (从2%提高)
        const val MAX_ULTRA_LOW_STEP = 0.01f           // 极低亮度最大调节1% (从0.5%提高)
    }
    
    /**
     * 护眼模式枚举
     */
    enum class EyeCareMode {
        STANDARD,        // 标准护眼模式 - 日常使用
        NIGHT,          // 夜间护眼模式 - 深夜使用
        ULTRA_SENSITIVE // 超敏感模式 - 极暗环境或严重干眼症
    }
    
    /** 当前护眼模式 */
    private var currentEyeCareMode = EyeCareMode.STANDARD
    
    /** 上次设置的亮度值 - 用于平滑调节 */
    private var lastBrightness = DEFAULT_BRIGHTNESS
    
    /** 用户亮度偏好偏移 (-20% 到 +20%) */
    private var userBrightnessOffset = 0.0f
    
    /** 当前光照强度 - 用于平滑调节计算 */
    private var currentLightLevel = 0.0f
    
    /** 是否为后台模式（Service中运行） */
    private val isBackgroundMode = (activity == null)
    
    /** 是否启用个性化亮度设置 */
    private var isPersonalizedBrightnessEnabled = false
    
    /** 系统亮度监听器 */
    private var systemBrightnessMonitor: SystemBrightnessMonitor? = null
    
    init {
        // 初始化个性化亮度管理器
        ScenarioBrightnessManager.initialize(context)
        isPersonalizedBrightnessEnabled = ScenarioBrightnessManager.isPersonalizedBrightnessEnabled()
        
        // 初始化智能学习管理器
        BrightnessLearningManager.initialize(context)
        
        Log.d(TAG, "BrightnessController初始化完成，个性化设置: $isPersonalizedBrightnessEnabled")
        Log.d(TAG, "智能学习系统: ${if (BrightnessLearningManager.isLearningEnabled()) "已启用" else "已禁用"}")
    }
    
    /**
     * 设置屏幕亮度 - 支持前台和后台模式，支持户外快速响应
     *
     * @param brightness 目标亮度值 (0.0-1.0)
     * @param immediate 是否立即设置，false则使用平滑调节
     */
    fun setBrightness(brightness: Float, immediate: Boolean = false) {
        try {
            val targetBrightness = brightness.coerceIn(getMinBrightness(), getMaxBrightness())

            // 检查是否应该使用户外快速响应模式
            val shouldUseOutdoorFastResponse = OutdoorBrightnessEnhancer.shouldImmediatelyAdjust(
                currentLightLevel, getCurrentBrightness(), targetBrightness
            )

            val finalBrightness = if (immediate || shouldUseOutdoorFastResponse) {
                if (shouldUseOutdoorFastResponse) {
                    Log.d(TAG, "使用户外快速响应模式")
                }
                targetBrightness
            } else {
                calculateSmoothBrightness(targetBrightness)
            }
            
            lastBrightness = finalBrightness

            // 记录户外快速响应调节
            if (shouldUseOutdoorFastResponse) {
                OutdoorBrightnessEnhancer.recordBrightnessAdjustment()
            }

            // 前台模式：设置窗口亮度
            activity?.let { act ->
                val layoutParams = act.window.attributes
                layoutParams.screenBrightness = finalBrightness
                act.window.attributes = layoutParams
                val modeText = if (shouldUseOutdoorFastResponse) " (户外快速)" else ""
                Log.d(TAG, "前台窗口亮度设置: ${(finalBrightness * 100).toInt()}%$modeText")
            }
            
            // 设置系统亮度（前台和后台都需要）
            setSystemBrightness(finalBrightness)
            
            // 标记应用进行了自动调节（防止被系统监听器误判为用户手动调节）
            systemBrightnessMonitor?.markAppAdjustment()
            
        } catch (e: Exception) {
            Log.e(TAG, "设置亮度失败: ${e.message}")
        }
    }
    
    /**
     * 设置系统亮度 - 专门处理后台模式
     */
    private fun setSystemBrightness(brightness: Float) {
        try {
            val systemBrightness = (brightness * 255).toInt().coerceIn(1, 255)
            
            // 检查是否有权限
            if (!hasWriteSettingsPermission()) {
                Log.w(TAG, "没有系统设置权限，无法修改系统亮度")
                return
            }
            
            // 尝试设置系统亮度
            val success = Settings.System.putInt(
                context.contentResolver,
                Settings.System.SCREEN_BRIGHTNESS,
                systemBrightness
            )
            
            if (success) {
                Log.d(TAG, "系统亮度设置成功: ${(brightness * 100).toInt()}% (${systemBrightness}/255)")
                
                // 在后台模式下，还需要确保系统亮度模式为手动
                if (isBackgroundMode) {
                    ensureManualBrightnessMode()
                }
            } else {
                Log.w(TAG, "系统亮度设置失败")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "设置系统亮度异常: ${e.message}")
        }
    }
    
    /**
     * 确保系统处于手动亮度模式
     */
    private fun ensureManualBrightnessMode() {
        try {
            val currentMode = Settings.System.getInt(
                context.contentResolver,
                Settings.System.SCREEN_BRIGHTNESS_MODE,
                Settings.System.SCREEN_BRIGHTNESS_MODE_AUTOMATIC
            )
            
            if (currentMode == Settings.System.SCREEN_BRIGHTNESS_MODE_AUTOMATIC) {
                Log.i(TAG, "检测到系统自动亮度模式，建议用户手动关闭")
                // 注意：从 API 23 开始，应用无法直接修改系统亮度模式
                // 只能通过引导用户手动关闭
            }
        } catch (e: Exception) {
            Log.w(TAG, "检查系统亮度模式失败: ${e.message}")
        }
    }
    
    /**
     * 获取当前屏幕亮度
     * 
     * @return 当前亮度值 (0.0-1.0)
     */
    fun getCurrentBrightness(): Float {
        return try {
            // 优先从窗口获取（前台模式）
            activity?.window?.attributes?.screenBrightness?.let { windowBrightness ->
                if (windowBrightness >= 0) {
                    return windowBrightness
                }
            }
            
            // 从系统设置获取
            val systemBrightness = Settings.System.getInt(
                context.contentResolver,
                Settings.System.SCREEN_BRIGHTNESS,
                (DEFAULT_BRIGHTNESS * 255).toInt()
            )
            (systemBrightness / 255.0f).coerceIn(0.0f, 1.0f)
        } catch (e: Exception) {
            Log.w(TAG, "获取当前亮度失败: ${e.message}")
            DEFAULT_BRIGHTNESS
        }
    }
    
    /**
     * 检查系统是否开启了自动亮度
     */
    fun isSystemAutoBrightnessEnabled(): Boolean {
        return try {
            val mode = Settings.System.getInt(
                context.contentResolver,
                Settings.System.SCREEN_BRIGHTNESS_MODE,
                Settings.System.SCREEN_BRIGHTNESS_MODE_MANUAL
            )
            mode == Settings.System.SCREEN_BRIGHTNESS_MODE_AUTOMATIC
        } catch (e: Exception) {
            Log.w(TAG, "检查系统自动亮度状态失败: ${e.message}")
            false
        }
    }
    
    /**
     * 获取系统亮度模式描述
     */
    fun getSystemBrightnessModeDescription(): String {
        return if (isSystemAutoBrightnessEnabled()) {
            "系统自动亮度：已开启（可能与护眼调节冲突）"
        } else {
            "系统自动亮度：已关闭（推荐设置）"
        }
    }
    
    /**
     * 检查是否存在亮度调节冲突
     */
    fun hasBrightnessConflict(): Boolean {
        return isSystemAutoBrightnessEnabled()
    }
    
    /**
     * 获取亮度冲突解决建议
     */
    fun getBrightnessConflictAdvice(): String {
        return if (hasBrightnessConflict()) {
            "检测到系统自动亮度已开启，建议在系统设置中关闭以获得最佳护眼效果"
        } else {
            "亮度设置正常，护眼调节功能运行良好"
        }
    }
    
    /**
     * 检查是否有系统设置写入权限
     */
    fun hasWriteSettingsPermission(): Boolean {
        return try {
            // 检查是否有权限
            val hasPermission = Settings.System.canWrite(context)
            
            if (!hasPermission) {
                Log.w(TAG, "缺少系统设置写入权限")
                // 尝试检查是否为系统应用
                checkSystemAppStatus()
            } else {
                Log.d(TAG, "系统设置写入权限正常")
            }
            
            hasPermission
        } catch (e: Exception) {
            Log.e(TAG, "检查系统设置权限失败: ${e.message}")
            false
        }
    }
    
    /**
     * 检查是否为系统应用
     */
    private fun checkSystemAppStatus() {
        try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            val applicationInfo = packageInfo.applicationInfo
            val isSystemApp = (applicationInfo?.flags ?: 0 and android.content.pm.ApplicationInfo.FLAG_SYSTEM) != 0
            val isUpdatedSystemApp = (applicationInfo?.flags ?: 0 and android.content.pm.ApplicationInfo.FLAG_UPDATED_SYSTEM_APP) != 0
            
            Log.i(TAG, "应用类型检查: 系统应用=$isSystemApp, 更新系统应用=$isUpdatedSystemApp")
            
            if (isSystemApp || isUpdatedSystemApp) {
                Log.i(TAG, "检测到系统应用，尝试使用系统级权限")
            } else {
                Log.w(TAG, "非系统应用，需要用户手动授权或使用系统签名")
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查应用状态失败: ${e.message}")
        }
    }
    

    
    /**
     * 获取权限状态描述
     */
    fun getPermissionStatusDescription(): String {
        return if (hasWriteSettingsPermission()) {
            "✅ 系统设置权限：已授权"
        } else {
            "❌ 系统设置权限：未授权（需要手动授权）"
        }
    }
    
    /**
     * 检查权限是否可用
     */
    fun isPermissionAvailable(): Boolean {
        return hasWriteSettingsPermission()
    }
    
    /**
     * 根据光照强度计算护眼亮度 - 支持个性化亮度设置和智能学习
     * 
     * @param lightLevel 光照强度 (lux)
     * @param isManualAdjustment 是否为手动调节（用于学习记录）
     * @return 计算出的亮度值
     */
    fun calculateBrightnessFromLight(lightLevel: Float, isManualAdjustment: Boolean = false): Float {
        // 更新当前光照强度，用于平滑调节计算
        currentLightLevel = lightLevel
        
        // 获取当前场景
        val currentScenario = ScenarioBrightnessManager.getCurrentBestScenario(lightLevel)
        
        // 计算基础亮度（根据护眼模式）
        val baseBrightness = when (currentEyeCareMode) {
            EyeCareMode.NIGHT -> calculateNightModeBrightness(lightLevel)
            EyeCareMode.ULTRA_SENSITIVE -> calculateUltraSensitiveBrightness(lightLevel)
            else -> calculateStandardEyeCareBrightness(lightLevel)
        }
        
        // 优先检查智能学习系统的建议
        val learnedBrightness = BrightnessLearningManager.getLearnedBrightness(
            currentScenario.id, 
            baseBrightness
        )
        
        // 选择使用的亮度：学习亮度 > 个性化亮度 > 基础亮度
        val selectedBrightness = when {
            learnedBrightness != null -> {
                Log.v(TAG, "使用智能学习亮度: ${currentScenario.displayName} -> ${(learnedBrightness*100).toInt()}%")
                learnedBrightness
            }
            isPersonalizedBrightnessEnabled -> {
                ScenarioBrightnessManager.getPersonalizedBrightness(lightLevel, baseBrightness)
            }
            else -> baseBrightness
        }
        
        // 应用用户偏好偏移
        var adjustedBrightness = selectedBrightness + userBrightnessOffset
        
        // 🆕 原生亮度对比模式：护眼亮度比原生低30%
        if (EyeCareSettingsManager.isNativeBrightnessComparisonEnabled(context)) {
            val nativeBrightness = calculateNativeSystemBrightness(lightLevel)
            val targetReduction = nativeBrightness * NATIVE_BRIGHTNESS_REDUCTION_RATIO
            val targetBrightness = nativeBrightness - targetReduction
            
            // 在护眼亮度和原生降低亮度之间选择较低的值，确保护眼效果
            adjustedBrightness = kotlin.math.min(adjustedBrightness, targetBrightness)
            
            Log.d(TAG, "原生亮度对比: 原生=${(nativeBrightness*100).toInt()}% -> 目标=${(targetBrightness*100).toInt()}% -> 护眼=${(adjustedBrightness*100).toInt()}%")
        }
        
        val finalBrightness = adjustedBrightness.coerceIn(getMinBrightness(), getMaxBrightness())
        
        // 记录调试信息
        when {
            learnedBrightness != null && learnedBrightness != baseBrightness -> {
                Log.v(TAG, "智能学习亮度调节: 默认=${(baseBrightness*100).toInt()}% -> 学习=${(learnedBrightness*100).toInt()}% -> 最终=${(finalBrightness*100).toInt()}%")
            }
            isPersonalizedBrightnessEnabled && selectedBrightness != baseBrightness -> {
                Log.v(TAG, "个性化亮度调节: 默认=${(baseBrightness*100).toInt()}% -> 个性化=${(selectedBrightness*100).toInt()}% -> 最终=${(finalBrightness*100).toInt()}%")
            }
        }
        
        return finalBrightness
    }
    
    /**
     * 记录用户手动调节行为（用于智能学习）
     * 
     * @param originalBrightness 调节前亮度
     * @param newBrightness 调节后亮度
     * @param lightLevel 当前光照强度
     */
    fun recordManualAdjustment(originalBrightness: Float, newBrightness: Float, lightLevel: Float) {
        val currentScenario = ScenarioBrightnessManager.getCurrentBestScenario(lightLevel)
        
        BrightnessLearningManager.recordAdjustment(
            context = context,
            scenarioId = currentScenario.id,
            lightLevel = lightLevel,
            originalBrightness = originalBrightness,
            adjustedBrightness = newBrightness,
            eyeCareMode = currentEyeCareMode.name,
            userInitiated = true
        )
        
        Log.d(TAG, "记录手动调节: ${currentScenario.displayName} ${(originalBrightness*100).toInt()}% -> ${(newBrightness*100).toInt()}%")
    }
    
    /**
     * 标准护眼模式亮度计算 - 优化环境适应性，提高户外亮度，降低室内亮度
     */
    private fun calculateStandardEyeCareBrightness(lightLevel: Float): Float {
        return when {
            lightLevel < DEEP_DARK_ENVIRONMENT_LUX -> {
                // 完全黑暗环境 - 保持超低亮度
                SUPER_ULTRA_LOW_BRIGHTNESS  // 0.2%
            }
            lightLevel < EXTREME_DARK_ENVIRONMENT_LUX -> {
                // 极暗环境
                ULTRA_LOW_BRIGHTNESS  // 0.3%
            }
            lightLevel < DARK_ENVIRONMENT_LUX -> {
                // 很暗环境
                0.004f  // 0.4%
            }
            lightLevel < 8 -> {
                // 暗环境
                0.008f  // 0.8% (从1.0%降低)
            }
            lightLevel < 15 -> {
                // 昏暗环境
                0.015f  // 1.5% (从2.0%降低)
            }
            lightLevel < 30 -> {
                // 暗中等环境
                0.030f  // 3.0% (从5.0%降低)
            }
            lightLevel < 60 -> {
                // 中等环境
                0.060f  // 6.0% (从10.0%降低)
            }
            lightLevel < 120 -> {
                // 明中等环境
                0.120f  // 12.0% (从18.0%降低)
            }
            lightLevel < 250 -> {
                // 明亮环境
                0.200f  // 20.0% (从25.0%降低)
            }
            lightLevel < 500 -> {
                // 很明亮环境 - 室内明亮/户外阴天
                0.300f  // 30.0% (从35.0%降低)
            }
            lightLevel < 1000 -> {
                // 户外环境 - 大幅提高确保可视性
                0.750f  // 75.0% (从50.0%大幅提升)
            }
            lightLevel < 3000 -> {
                // 户外强光环境 - 确保强光下可见
                0.850f  // 85.0% (从60.0%大幅提升)
            }
            lightLevel < 10000 -> {
                // 户外阳光直射 - 确保清晰可见
                0.900f  // 90.0% (从70.0%大幅提升)
            }
            lightLevel < 20000 -> {
                // 极强阳光环境 - 确保任何情况下都可见
                0.950f  // 95.0% (从75.0%大幅提升)
            }
            else -> {
                // 超强阳光环境 - 最大亮度确保可见
                0.980f  // 98.0% (从80.0%大幅提升)
            }
        }
    }
    
    /**
     * 夜间护眼模式亮度计算 - 专为干眼症患者设计的极低亮度，优化室内和夜间亮度
     */
    private fun calculateNightModeBrightness(lightLevel: Float): Float {
        val currentHour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY)
        val isDeepNight = currentHour >= 23 || currentHour <= 5  // 深夜时段
        
        return when {
            lightLevel < DEEP_DARK_ENVIRONMENT_LUX -> {
                // 完全黑暗环境 - 使用最极端的低亮度
                if (isDeepNight) DEEP_NIGHT_BRIGHTNESS else SUPER_ULTRA_LOW_BRIGHTNESS
            }
            lightLevel < EXTREME_DARK_ENVIRONMENT_LUX -> {
                // 极暗环境 - 稍微提高但仍然很低
                if (isDeepNight) SUPER_ULTRA_LOW_BRIGHTNESS else ULTRA_LOW_BRIGHTNESS
            }
            lightLevel < DARK_ENVIRONMENT_LUX -> {
                // 很暗环境
                if (isDeepNight) 0.002f else 0.003f   // 0.2% / 0.3%
            }
            lightLevel < 10 -> {
                // 暗环境
                if (isDeepNight) 0.004f else 0.006f   // 0.4% / 0.6% (从0.5%/0.8%降低)
            }
            lightLevel < 20 -> {
                // 昏暗环境
                if (isDeepNight) 0.008f else 0.012f   // 0.8% / 1.2% (从1.0%/1.5%降低)
            }
            lightLevel < 40 -> {
                // 暗中等环境
                if (isDeepNight) 0.015f else 0.020f   // 1.5% / 2.0% (从1.8%/2.5%降低)
            }
            lightLevel < 80 -> {
                // 中等环境
                if (isDeepNight) 0.020f else 0.025f   // 2.0% / 2.5% (从2.5%/3.5%降低)
            }
            lightLevel < 200 -> {
                // 较亮环境
                if (isDeepNight) 0.025f else 0.030f   // 2.5% / 3.0% (从3.0%/4.0%降低)
            }
            lightLevel < 500 -> {
                // 明亮环境 - 夜间模式但需要可视性
                if (isDeepNight) 0.040f else 0.060f   // 4.0% / 6.0% (从5.0%/8.0%降低)
            }
            lightLevel < 1000 -> {
                // 很明亮环境 - 夜间模式在户外也要确保可见
                if (isDeepNight) 0.080f else 0.120f   // 8.0% / 12.0% (从8.0%/12.0%保持)
            }
            lightLevel < 3000 -> {
                // 户外强光环境 - 夜间模式最高亮度
                if (isDeepNight) 0.150f else 0.200f   // 15.0% / 20.0% (从12.0%/18.0%提升)
            }
            else -> {
                // 极强光环境 - 夜间模式在强光下也要确保可见
                if (isDeepNight) 0.200f else 0.250f   // 20.0% / 25.0% (从15.0%/20.0%提升)
            }
        }
    }
    
    /**
     * 超敏感护眼模式亮度计算 - 优化环境适应性，提高户外亮度，降低室内亮度
     */
    private fun calculateUltraSensitiveBrightness(lightLevel: Float): Float {
        return when {
            lightLevel < DEEP_DARK_ENVIRONMENT_LUX -> {
                // 完全黑暗环境 - 使用最极端的低亮度
                DEEP_NIGHT_BRIGHTNESS  // 0.1%
            }
            lightLevel < EXTREME_DARK_ENVIRONMENT_LUX -> {
                // 极暗环境
                ULTRA_LOW_BRIGHTNESS  // 0.3%
            }
            lightLevel < 12 -> {
                // 暗环境
                0.004f  // 0.4%
            }
            lightLevel < 25 -> {
                // 昏暗环境
                0.008f  // 0.8% (从1.0%降低)
            }
            lightLevel < 50 -> {
                // 暗中等环境
                0.020f  // 2.0% (从2.5%降低)
            }
            lightLevel < 100 -> {
                // 中等环境
                0.040f  // 4.0% (从5.0%降低)
            }
            lightLevel < 200 -> {
                // 明中等环境
                0.080f  // 8.0% (从10.0%降低)
            }
            lightLevel < 500 -> {
                // 明亮环境 - 室内明亮
                0.150f  // 15.0% (从18.0%降低)
            }
            lightLevel < 1000 -> {
                // 很明亮环境 - 户外环境，大幅提高确保可视性
                0.450f  // 45.0% (从28.0%大幅提升)
            }
            lightLevel < 3000 -> {
                // 户外强光环境 - 确保可见
                0.600f  // 60.0% (从40.0%大幅提升)
            }
            lightLevel < 10000 -> {
                // 户外阳光直射 - 超敏感模式上限
                0.700f  // 70.0% (从50.0%大幅提升)
            }
            else -> {
                // 极强光环境 - 超敏感模式最高亮度
                0.750f  // 75.0% (从55.0%大幅提升)
            }
        }
    }
    
    /**
     * 计算平滑亮度变化 - 多级防频闪处理系统
     */
    private fun calculateSmoothBrightness(targetBrightness: Float): Float {
        val currentBrightness = getCurrentBrightness()
        val brightnessChange = targetBrightness - currentBrightness
        val changeAmount = kotlin.math.abs(brightnessChange)
        
        Log.v(TAG, "平滑计算: 当前=${(currentBrightness*100).toInt()}% 目标=${(targetBrightness*100).toInt()}% 变化=${(changeAmount*100).toInt()}%")
        
        // 第一级过滤：微小变化直接忽略
        if (changeAmount < MICRO_BRIGHTNESS_THRESHOLD) {
            Log.v(TAG, "微小变化忽略: ${(changeAmount*100).toInt()}% < ${(MICRO_BRIGHTNESS_THRESHOLD*100).toInt()}%")
            return currentBrightness
        }
        
        // 第二级过滤：根据环境设定变化阈值
        val environmentThreshold = when {
            currentLightLevel > OUTDOOR_ENVIRONMENT_LUX -> OUTDOOR_BRIGHTNESS_CHANGE_THRESHOLD
            currentLightLevel < DARK_ENVIRONMENT_LUX -> MICRO_BRIGHTNESS_THRESHOLD  // 极暗环境更敏感
            currentLightLevel > 200.0f -> BRIGHTNESS_CHANGE_THRESHOLD * 0.8f  // 明亮环境更敏感
            else -> BRIGHTNESS_CHANGE_THRESHOLD
        }
        
        if (changeAmount < environmentThreshold) {
            Log.v(TAG, "环境阈值过滤: ${(changeAmount*100).toInt()}% < ${(environmentThreshold*100).toInt()}%")
            return currentBrightness
        }
        
        // 第三级：智能平滑因子选择（优先使用户外快速响应因子）
        val smoothingFactor = if (OutdoorBrightnessEnhancer.isInOutdoorMode()) {
            OutdoorBrightnessEnhancer.getOutdoorSmoothingFactor()
        } else {
            selectSmoothingFactor(currentBrightness, targetBrightness, changeAmount)
        }
        
        // 第四级：步长限制 - 防止大跳跃
        val maxStep = getMaxBrightnessStep(currentBrightness, targetBrightness)
        val limitedChange = if (changeAmount > maxStep) {
            if (brightnessChange > 0) maxStep else -maxStep
        } else {
            brightnessChange
        }
        
        // 第五级：应用平滑调节
        val smoothedChange = limitedChange * smoothingFactor
        val finalBrightness = currentBrightness + smoothedChange
        
        // 第六级：边界检查和最终调整
        val boundedBrightness = finalBrightness.coerceIn(getMinBrightness(), getMaxBrightness())
        
        Log.d(TAG, "平滑调节: ${(currentBrightness*100).toInt()}% -> ${(boundedBrightness*100).toInt()}% (步长=${(smoothedChange*100).toInt()}%)")
        
        return boundedBrightness
    }
    
    /**
     * 智能选择平滑因子 - 根据多种条件动态调整
     */
    private fun selectSmoothingFactor(currentBrightness: Float, targetBrightness: Float, changeAmount: Float): Float {
        return when {
            // 极低亮度环境 - 使用最平滑的调节
            currentBrightness < 0.01f || targetBrightness < 0.01f -> {
                Log.v(TAG, "极低亮度平滑因子: ${ULTRA_LOW_SMOOTHING_FACTOR}")
                ULTRA_LOW_SMOOTHING_FACTOR
            }
            // 户外环境 - 使用更快的调节
            currentLightLevel > OUTDOOR_ENVIRONMENT_LUX -> {
                val outdoorFactor = when {
                    changeAmount > 0.10f -> 0.08f  // 大变化：8%
                    changeAmount > 0.05f -> 0.12f  // 中等变化：12%
                    else -> 0.15f                   // 小变化：15%
                }
                Log.v(TAG, "户外环境平滑因子: $outdoorFactor")
                outdoorFactor
            }
            // 大幅变化 - 使用超平滑调节
            changeAmount > LARGE_BRIGHTNESS_CHANGE_THRESHOLD -> {
                Log.v(TAG, "大幅变化平滑因子: ${LARGE_CHANGE_SMOOTHING_FACTOR}")
                LARGE_CHANGE_SMOOTHING_FACTOR
            }
            // 暗光环境 - 使用较平滑的调节
            currentLightLevel < DARK_ENVIRONMENT_LUX -> {
                Log.v(TAG, "暗光环境平滑因子: ${NIGHT_SMOOTHING_FACTOR}")
                NIGHT_SMOOTHING_FACTOR
            }
            // 明亮环境 - 使用较快的调节
            currentLightLevel > 200.0f -> {
                val brightFactor = when {
                    changeAmount > 0.05f -> 0.10f  // 中等变化：10%
                    else -> 0.15f                   // 小变化：15%
                }
                Log.v(TAG, "明亮环境平滑因子: $brightFactor")
                brightFactor
            }
            // 标准环境 - 使用平衡的调节
            else -> {
                Log.v(TAG, "标准环境平滑因子: ${BASE_SMOOTHING_FACTOR}")
                BASE_SMOOTHING_FACTOR
            }
        }
    }
    
    /**
     * 获取最大亮度调节步长 - 防止突然跳变
     */
    private fun getMaxBrightnessStep(currentBrightness: Float, targetBrightness: Float): Float {
        return when {
            // 极低亮度环境 - 使用最小步长
            currentBrightness < 0.01f || targetBrightness < 0.01f -> {
                Log.v(TAG, "极低亮度最大步长: ${MAX_ULTRA_LOW_STEP}")
                MAX_ULTRA_LOW_STEP
            }
            
            // 低光环境 - 使用较小步长
            currentBrightness < 0.10f || targetBrightness < 0.10f -> {
                Log.v(TAG, "低光环境最大步长: ${MAX_LOW_LIGHT_STEP}")
                MAX_LOW_LIGHT_STEP
            }
            
            // 一般环境 - 使用标准步长
            else -> {
                Log.v(TAG, "标准环境最大步长: ${MAX_BRIGHTNESS_STEP}")
                MAX_BRIGHTNESS_STEP
            }
        }
    }
    
    /**
     * 设置护眼模式
     */
    fun setEyeCareMode(mode: EyeCareMode) {
        currentEyeCareMode = mode
        Log.d(TAG, "护眼模式切换为: $mode")
    }
    
    /**
     * 获取当前护眼模式
     */
    fun getCurrentEyeCareMode(): EyeCareMode {
        return currentEyeCareMode
    }
    
    /**
     * 获取当前模式的最小亮度 - 干眼症患者专用极低亮度
     */
    fun getMinBrightness(): Float {
        return when (currentEyeCareMode) {
            EyeCareMode.NIGHT -> DEEP_NIGHT_BRIGHTNESS  // 夜间模式使用最低亮度
            EyeCareMode.ULTRA_SENSITIVE -> DEEP_NIGHT_BRIGHTNESS  // 超敏感模式也使用最低亮度
            else -> SUPER_ULTRA_LOW_BRIGHTNESS  // 标准模式也降低最小亮度
        }
    }
    
    /**
     * 获取当前环境下的最大亮度 - 优化环境适应性，提高户外亮度上限
     */
    fun getMaxBrightness(): Float {
        return when (currentEyeCareMode) {
            EyeCareMode.NIGHT -> {
                // 夜间模式根据环境光照调整上限
                when {
                    currentLightLevel > 20000 -> 0.250f   // 超强光环境：25% (从20%提升)
                    currentLightLevel > 10000 -> 0.220f   // 极强光环境：22% (从18%提升)
                    currentLightLevel > 3000 -> 0.200f    // 户外强光：20% (从15%提升)
                    currentLightLevel > 1000 -> 0.150f    // 户外环境：15% (从12%提升)
                    currentLightLevel > 500 -> 0.080f     // 明亮环境：8% (从8%保持)
                    else -> NIGHT_MODE_MAX_BRIGHTNESS     // 一般环境：12%
                }
            }
            EyeCareMode.ULTRA_SENSITIVE -> {
                // 超敏感模式根据环境光照调整上限
                when {
                    currentLightLevel > 20000 -> 0.750f   // 超强光环境：75% (从55%大幅提升)
                    currentLightLevel > 10000 -> 0.700f   // 极强光环境：70% (从50%大幅提升)
                    currentLightLevel > 3000 -> 0.600f    // 户外强光：60% (从40%大幅提升)
                    currentLightLevel > 1000 -> 0.450f    // 户外环境：45% (从28%大幅提升)
                    currentLightLevel > 500 -> 0.150f     // 明亮环境：15% (从18%降低)
                    else -> 0.080f                        // 一般环境：8% (从10%降低)
                }
            }
            else -> {
                // 标准模式根据环境光照调整上限
                when {
                    currentLightLevel > 20000 -> 0.980f   // 超强阳光：98% (从80%大幅提升)
                    currentLightLevel > 10000 -> 0.900f   // 极强阳光：90% (从70%大幅提升)
                    currentLightLevel > 3000 -> 0.850f    // 户外强光：85% (从60%大幅提升)
                    currentLightLevel > 1000 -> 0.750f    // 户外环境：75% (从50%大幅提升)
                    currentLightLevel > 500 -> 0.300f     // 明亮环境：30% (从35%降低)
                    else -> 0.200f                        // 一般环境：20% (从25%降低)
                }
            }
        }
    }
    
    /**
     * 设置用户亮度偏好偏移
     */
    fun setUserBrightnessOffset(offset: Float) {
        userBrightnessOffset = offset.coerceIn(-0.2f, 0.2f)
        Log.d(TAG, "用户亮度偏移设置为: ${(userBrightnessOffset * 100).toInt()}%")
    }
    
    /**
     * 🆕 计算原生系统亮度 - 模拟系统自动亮度算法
     * 
     * 这个方法模拟Android系统的原生自动亮度算法，用于对比护眼亮度
     * 确保护眼应用的亮度始终比原生系统亮度低30%
     * 
     * @param lightLevel 光照强度 (lux)
     * @return 原生系统会设置的亮度值 (0.0-1.0)
     */
    private fun calculateNativeSystemBrightness(lightLevel: Float): Float {
        return when {
            lightLevel < 1.0f -> {
                // 完全黑暗环境 - 原生系统通常设置5-10%
                0.08f  // 8%
            }
            lightLevel < 5.0f -> {
                // 极暗环境 - 原生系统设置10-15%
                0.12f  // 12%
            }
            lightLevel < 15.0f -> {
                // 暗环境 - 原生系统设置15-25%
                0.20f  // 20%
            }
            lightLevel < 30.0f -> {
                // 昏暗环境 - 原生系统设置25-35%
                0.30f  // 30%
            }
            lightLevel < 60.0f -> {
                // 中等环境 - 原生系统设置35-45%
                0.40f  // 40%
            }
            lightLevel < 120.0f -> {
                // 明中等环境 - 原生系统设置45-55%
                0.50f  // 50%
            }
            lightLevel < 250.0f -> {
                // 明亮环境 - 原生系统设置55-65%
                0.60f  // 60%
            }
            lightLevel < 500.0f -> {
                // 很明亮环境 - 原生系统设置65-75%
                0.70f  // 70%
            }
            lightLevel < 1000.0f -> {
                // 户外环境 - 原生系统设置75-85%
                0.80f  // 80%
            }
            lightLevel < 3000.0f -> {
                // 户外强光环境 - 原生系统设置85-95%
                0.90f  // 90%
            }
            lightLevel < 10000.0f -> {
                // 户外阳光直射 - 原生系统设置95-100%
                0.95f  // 95%
            }
            else -> {
                // 极强阳光环境 - 原生系统设置最大亮度
                1.00f  // 100%
            }
        }
    }
    
    /**
     * 重置亮度到默认值
     */
    fun resetBrightness() {
        setBrightness(DEFAULT_BRIGHTNESS, immediate = true)
        Log.d(TAG, "亮度已重置为默认值")
    }
    
    /**
     * 根据时间判断是否应该使用夜间模式
     */
    fun shouldUseNightMode(currentHour: Int = Calendar.getInstance().get(Calendar.HOUR_OF_DAY)): Boolean {
        return currentHour >= 22 || currentHour <= 6
    }
    
    /**
     * 获取亮度护眼评级 - 针对干眼症患者优化评级标准
     */
    fun getBrightnessEyeCareRating(): String {
        val brightness = getCurrentBrightness()
        val percentage = (brightness * 100).toInt()
        
        return when (currentEyeCareMode) {
            EyeCareMode.NIGHT -> when {
                percentage <= 1 -> "完美护眼"     // 新增：0.1%-1%
                percentage <= 3 -> "极佳护眼"     // 1%-3%
                percentage <= 6 -> "优秀护眼"     // 3%-6%
                percentage <= 12 -> "良好护眼"    // 6%-12%
                else -> "需要调低"
            }
            EyeCareMode.ULTRA_SENSITIVE -> when {
                percentage <= 1 -> "完美护眼"     // 新增：0.1%-1%
                percentage <= 3 -> "极佳护眼"     // 1%-3%
                percentage <= 8 -> "优秀护眼"     // 3%-8%
                percentage <= 20 -> "良好护眼"    // 8%-20%
                percentage <= 40 -> "尚可护眼"    // 20%-40%
                else -> "需要调低"
            }
            else -> when {
                percentage <= 1 -> "完美护眼"     // 新增：0.1%-1%
                percentage <= 5 -> "极佳护眼"     // 1%-5%
                percentage <= 15 -> "优秀护眼"    // 5%-15%
                percentage <= 35 -> "良好护眼"    // 15%-35%
                percentage <= 60 -> "尚可护眼"    // 35%-60%
                else -> "过亮刺眼"
            }
        }
    }
    
    /**
     * 获取护眼建议
     */
    fun getEyeCareAdvice(lightLevel: Float): String {
        val currentHour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY)
        
        // 优先检查系统冲突
        if (hasBrightnessConflict()) {
            return "检测到系统自动亮度冲突，请在设置中关闭系统自动亮度以获得最佳护眼效果"
        }
        
        return when {
            currentHour >= 23 || currentHour <= 5 -> {
                val currentBrightness = (getCurrentBrightness() * 100).toInt()
                if (currentBrightness > 3) {
                    "深夜时段，建议使用夜间模式并调至1%以下亮度，为干眼症患者提供最佳保护"
                } else {
                    "深夜亮度设置良好，减少蓝光刺激有助于眼部恢复"
                }
            }
            lightLevel < DEEP_DARK_ENVIRONMENT_LUX -> {
                "完全黑暗环境，当前使用最低亮度(0.1-0.2%)，非常适合干眼症患者"
            }
            lightLevel < EXTREME_DARK_ENVIRONMENT_LUX -> {
                "极暗环境，建议使用夜间模式或超敏感模式，并保持0.3%以下亮度"
            }
            lightLevel < DARK_ENVIRONMENT_LUX && currentEyeCareMode != EyeCareMode.NIGHT -> {
                "暗光环境，强烈建议切换到夜间护眼模式，获得干眼症专用保护"
            }
            lightLevel < 15 && currentEyeCareMode == EyeCareMode.STANDARD -> {
                "光线较暗，建议切换到超敏感模式，亮度将自动降至2%以下"
            }
            lightLevel > 20000 -> {
                "极强阳光环境，当前亮度已自动提升至${(getCurrentBrightness() * 100).toInt()}%确保清晰可见，避免眯眼伤害。户外强光下看不清屏幕比适当提高亮度更伤眼睛"
            }
            lightLevel > 10000 -> {
                "户外阳光直射，亮度已调至${(getCurrentBrightness() * 100).toInt()}%，确保在强光下清晰可见，避免因看不清而眯眼"
            }
            lightLevel > 5000 -> {
                "户外强光环境，亮度已调至${(getCurrentBrightness() * 100).toInt()}%，避免眯眼造成的眼部疲劳，保护眼睛健康"
            }
            lightLevel > 1000 -> {
                "户外环境，亮度已优化至${(getCurrentBrightness() * 100).toInt()}%，保证舒适可视，平衡护眼与清晰度。户外看不清屏幕会迫使用户眯眼，比适当亮度更伤眼"
            }
            lightLevel > 500 -> {
                "明亮环境，当前亮度${(getCurrentBrightness() * 100).toInt()}%平衡了护眼和可视性，适合长时间使用"
            }
            getCurrentBrightness() > 0.50f && lightLevel < 100 -> {
                "室内环境亮度偏高，建议降至30%以下以更好保护眼睛"
            }
            lightLevel < 100 && getCurrentBrightness() > 0.25f -> {
                "室内环境亮度偏高，建议使用超敏感模式或手动调低至20%以下"
            }
            else -> {
                "当前亮度设置合理，适合当前环境使用。记住：户外环境需要足够亮度避免眯眼，室内环境可以适当降低亮度保护眼睛"
            }
        }
    }
    
    /**
     * 启用或禁用个性化亮度设置
     */
    fun setPersonalizedBrightnessEnabled(enabled: Boolean) {
        isPersonalizedBrightnessEnabled = enabled
        ScenarioBrightnessManager.setPersonalizedBrightnessEnabled(context, enabled)
        Log.d(TAG, "个性化亮度设置已${if (enabled) "启用" else "禁用"}")
    }
    
    /**
     * 检查是否启用了个性化亮度设置
     */
    fun isPersonalizedBrightnessEnabled(): Boolean {
        return isPersonalizedBrightnessEnabled
    }
    
    /**
     * 获取当前光照环境下的最佳匹配情境
     */
    fun getCurrentScenario(): ScenarioBrightnessManager.BrightnessScenario {
        return ScenarioBrightnessManager.getCurrentBestScenario(currentLightLevel)
    }
    
    /**
     * 刷新个性化亮度设置状态
     */
    fun refreshPersonalizedBrightnessState() {
        isPersonalizedBrightnessEnabled = ScenarioBrightnessManager.isPersonalizedBrightnessEnabled()
        Log.v(TAG, "个性化亮度状态已刷新: $isPersonalizedBrightnessEnabled")
    }
    
    /**
     * 获取个性化设置统计信息
     */
    fun getPersonalizationStats(): String {
        return if (isPersonalizedBrightnessEnabled) {
            ScenarioBrightnessManager.getPersonalizationStats()
        } else {
            "个性化亮度设置未启用"
        }
    }
    
    /**
     * 获取智能学习统计信息
     */
    fun getLearningStats(): String {
        return BrightnessLearningManager.getLearningStats()
    }
    
    /**
     * 获取学习建议
     */
    fun getLearningRecommendation(): String {
        val currentScenario = getCurrentScenario()
        val recommendation = BrightnessLearningManager.getLearningRecommendation(currentScenario.id)
        
        return if (recommendation != null) {
            recommendation.second
        } else {
            "当前场景(${currentScenario.displayName})暂无学习建议，请继续手动调节以收集数据。"
        }
    }
    
    /**
     * 应用学习建议到个性化设置
     */
    fun applyLearningRecommendation(): Boolean {
        val appliedCount = BrightnessLearningManager.applyAllLearningToPersonalization(context)
        
        if (appliedCount > 0) {
            // 刷新个性化设置状态
            refreshPersonalizedBrightnessState()
            Log.d(TAG, "已应用 $appliedCount 个场景的学习建议")
            return true
        }
        
        return false
    }
    
    /**
     * 启用或禁用智能学习
     */
    fun setLearningEnabled(enabled: Boolean) {
        BrightnessLearningManager.setLearningEnabled(context, enabled)
        Log.d(TAG, "智能学习已${if (enabled) "启用" else "禁用"}")
    }
    
    /**
     * 启用或禁用自动应用学习结果
     */
    fun setAutoApplyLearningEnabled(enabled: Boolean) {
        BrightnessLearningManager.setAutoApplyEnabled(context, enabled)
        Log.d(TAG, "自动应用学习结果已${if (enabled) "启用" else "禁用"}")
    }
    
    /**
     * 检查是否启用了智能学习
     */
    fun isLearningEnabled(): Boolean {
        return BrightnessLearningManager.isLearningEnabled()
    }
    
    /**
     * 检查是否启用了自动应用学习结果
     */
    fun isAutoApplyLearningEnabled(): Boolean {
        return BrightnessLearningManager.isAutoApplyEnabled()
    }
    
    /**
     * 获取学习使用建议
     */
    fun getLearningUsageAdvice(): String {
        return BrightnessLearningManager.getUsageAdvice()
    }
    
    /**
     * 获取详细学习报告
     */
    fun getDetailedLearningReport(): String {
        return BrightnessLearningManager.getDetailedLearningReport()
    }
    
    /**
     * 清除所有学习数据
     */
    fun clearLearningData() {
        BrightnessLearningManager.clearAllLearningData(context)
        Log.d(TAG, "所有学习数据已清除")
    }
    
    /**
     * 获取详细的系统状态报告
     */
    fun getSystemStatusReport(): String {
        val hasPermission = hasWriteSettingsPermission()
        val autoEnabled = isSystemAutoBrightnessEnabled()
        val currentBrightness = (getCurrentBrightness() * 100).toInt()
        val mode = if (isBackgroundMode) "后台模式" else "前台模式"
        val personalizedStatus = if (isPersonalizedBrightnessEnabled) {
            val currentScenario = getCurrentScenario()
            "已启用 (当前情境: ${currentScenario.displayName})"
        } else {
            "未启用"
        }
        
        return """
            运行模式: $mode
            系统权限: ${if (hasPermission) "已授权" else "未授权"}
            系统自动亮度: ${if (autoEnabled) "已开启（冲突）" else "已关闭（正常）"}
            当前亮度: ${currentBrightness}%
            护眼评级: ${getBrightnessEyeCareRating()}
            个性化设置: $personalizedStatus
        """.trimIndent()
    }
    
    /**
     * 获取高信心场景数量
     */
    fun getHighConfidenceScenariosCount(): Int {
        return BrightnessLearningManager.getHighConfidenceScenariosCount()
    }
    
    /**
     * 启用系统亮度监听器（需要传入光传感器管理器）
     */
    fun enableSystemBrightnessMonitoring(lightSensorManager: LightSensorManager) {
        if (systemBrightnessMonitor == null) {
            systemBrightnessMonitor = SystemBrightnessMonitor(
                context = context,
                lightnessController = this,
                lightSensorManager = lightSensorManager
            )
        }
        
        systemBrightnessMonitor?.startMonitoring()
        Log.i(TAG, "系统亮度监听器已启用")
    }
    
    /**
     * 禁用系统亮度监听器
     */
    fun disableSystemBrightnessMonitoring() {
        systemBrightnessMonitor?.stopMonitoring()
        Log.i(TAG, "系统亮度监听器已禁用")
    }
    
    /**
     * 检查系统亮度监听器是否运行
     */
    fun isSystemBrightnessMonitoringActive(): Boolean {
        return systemBrightnessMonitor?.isMonitoringActive() ?: false
    }
    
    /**
     * 获取系统亮度监听统计信息
     */
    fun getSystemBrightnessMonitoringStats(): String {
        return systemBrightnessMonitor?.getMonitoringStats() ?: "系统亮度监听器未启用"
    }
    
    /**
     * 获取系统亮度监听使用提示
     */
    fun getSystemBrightnessMonitoringTips(): String {
        return systemBrightnessMonitor?.getUsageTips() ?: """
            💡 系统亮度监听器使用提示
            
            系统亮度监听器当前未启用。
            启用后可以自动学习您通过系统界面进行的亮度调节习惯：
            
            • 下拉通知栏的亮度滑块
            • 设置 → 显示 → 亮度  
            • 音量键 + 亮度快捷键（部分手机）
            
            这样您就可以继续使用熟悉的系统调节方式，
            同时让应用学习您的习惯偏好。
        """.trimIndent()
    }
    
    /**
     * 重置系统亮度监听统计
     */
    fun resetSystemBrightnessMonitoringStats() {
        systemBrightnessMonitor?.resetStats()
        Log.i(TAG, "系统亮度监听统计已重置")
    }
}
