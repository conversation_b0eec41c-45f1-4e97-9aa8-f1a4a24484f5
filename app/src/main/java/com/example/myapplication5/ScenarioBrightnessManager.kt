package com.example.myapplication5

import android.content.Context
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * 情境亮度管理器 - 个性化亮度设置系统
 * 
 * 功能说明：
 * 1. 为不同使用场景定义个性化亮度值
 * 2. 根据环境光照自动匹配最适合的情境设置
 * 3. 支持用户自定义每种情境下的舒适亮度
 * 4. 考虑不同干眼症患者的敏感程度差异
 * 5. 保持与现有护眼模式的兼容性
 * 
 * <AUTHOR> v1.0
 * @since 1.0 - 支持情境化个性亮度设置
 */
object ScenarioBrightnessManager {
    
    private const val TAG = "ScenarioBrightness"
    private const val PREFS_NAME = "scenario_brightness_settings"
    private const val KEY_CUSTOM_SCENARIOS = "custom_scenarios"
    private const val KEY_ENABLED = "personalized_enabled"
    
    /**
     * 亮度情境定义 - 覆盖日常使用的主要场景
     */
    enum class BrightnessScenario(
        val id: String,
        val displayName: String,
        val description: String,
        val lightRangeMin: Float,    // 光照强度最小值 (lux)
        val lightRangeMax: Float,    // 光照强度最大值 (lux)
        val defaultBrightness: Float, // 默认亮度值 (0.0-1.0)
        val icon: String,            // 图标标识
        val tips: String             // 使用建议
    ) {
        DEEP_NIGHT(
            "deep_night",
            "深夜睡前",
            "完全黑暗环境，睡前阅读或紧急查看",
            0.0f, 0.5f, 0.001f, "🌙",
            "建议0.1-0.5%亮度，减少蓝光刺激，保护睡眠质量"
        ),
        
        BEDTIME(
            "bedtime", 
            "床头夜灯",
            "有微弱夜灯的卧室环境",
            0.5f, 2.0f, 0.003f, "🛏️",
            "建议0.3-1%亮度，适合床头阅读和轻度使用"
        ),
        
        DARK_ROOM(
            "dark_room",
            "暗室环境", 
            "关灯的房间或影院环境",
            2.0f, 8.0f, 0.008f, "🌚",
            "建议0.8-2%亮度，适合看视频或暗室办公"
        ),
        
        DIM_INDOOR(
            "dim_indoor",
            "昏暗室内",
            "开一盏灯的房间或傍晚室内",
            8.0f, 30.0f, 0.015f, "💡",  // 从2.5%降低到1.5%
            "建议1.5-3%亮度，适合放松时光或护眼休息"
        ),
        
        NORMAL_INDOOR(
            "normal_indoor",
            "室内照明",
            "正常照明的室内环境",
            30.0f, 150.0f, 0.060f, "🏠",  // 从8%降低到6%
            "建议6-12%亮度，适合日常室内活动"
        ),
        
        BRIGHT_OFFICE(
            "bright_office",
            "办公环境",
            "明亮的办公室或学习环境",
            150.0f, 400.0f, 0.120f, "💼",  // 从18%降低到12%
            "建议12-20%亮度，保证工作效率的同时保护眼睛"
        ),
        
        OUTDOOR_SHADE(
            "outdoor_shade",
            "户外阴影",
            "户外阴凉处或阴天户外",
            400.0f, 1000.0f, 0.300f, "🌤️",  // 从35%降低到30%
            "建议30-50%亮度，确保户外环境下的清晰可视"
        ),
        
        OUTDOOR_CLOUDY(
            "outdoor_cloudy",
            "户外多云",
            "多云天气的户外环境",
            1000.0f, 5000.0f, 0.750f, "☁️",  // 从55%大幅提升到75%
            "建议75-85%亮度，适应变化的户外光线，避免眯眼"
        ),
        
        OUTDOOR_SUNNY(
            "outdoor_sunny",
            "户外晴天",
            "晴天的户外环境",
            5000.0f, 20000.0f, 0.850f, "☀️",  // 从75%提升到85%
            "建议85-95%亮度，确保强光下的清晰显示，避免眯眼伤害"
        ),
        
        OUTDOOR_BRIGHT(
            "outdoor_bright",
            "户外强光",
            "阳光直射或雪地反光等极强光环境",
            20000.0f, Float.MAX_VALUE, 0.950f, "🌞",  // 从90%提升到95%
            "建议95-98%亮度，应对极强光线环境，确保任何情况下都清晰可见"
        );
        
        companion object {
            /**
             * 根据光照强度查找最匹配的情境
             */
            fun findBestMatch(lightLevel: Float): BrightnessScenario {
                return values().find { scenario ->
                    lightLevel >= scenario.lightRangeMin && lightLevel <= scenario.lightRangeMax
                } ?: run {
                    // 如果没有精确匹配，选择最接近的
                    values().minByOrNull { scenario ->
                        when {
                            lightLevel < scenario.lightRangeMin -> scenario.lightRangeMin - lightLevel
                            lightLevel > scenario.lightRangeMax -> lightLevel - scenario.lightRangeMax
                            else -> 0.0f
                        }
                    } ?: NORMAL_INDOOR
                }
            }
            
            /**
             * 获取所有情境的列表
             */
            fun getAllScenarios(): List<BrightnessScenario> {
                return values().toList()
            }
        }
    }
    
    /**
     * 用户自定义情境亮度数据类
     */
    data class CustomScenarioBrightness(
        val scenarioId: String,          // 情境ID
        val customBrightness: Float,     // 用户自定义亮度 (0.0-1.0)
        val lastModified: Long = System.currentTimeMillis(), // 最后修改时间
        val useCount: Int = 0            // 使用次数（用于统计和优化）
    ) {
        /**
         * 检查亮度值是否合理
         */
        fun isValidBrightness(): Boolean {
            return customBrightness in 0.0f..1.0f
        }
        
        /**
         * 获取亮度百分比描述
         */
        fun getBrightnessPercentage(): String {
            return "${(customBrightness * 100).toInt()}%"
        }
    }
    
    /**
     * 个性化亮度设置状态
     */
    data class PersonalizedBrightnessState(
        val isEnabled: Boolean = false,              // 是否启用个性化设置
        val customSettings: Map<String, CustomScenarioBrightness> = emptyMap(), // 自定义设置
        val lastUsedScenario: String? = null,       // 最后使用的情境
        val totalUsageCount: Int = 0                 // 总使用次数
    )
    
    /** 当前个性化设置状态 */
    private var currentState = PersonalizedBrightnessState()
    
    /** Gson实例用于序列化 */
    private val gson = Gson()
    
    /**
     * 初始化个性化亮度管理器
     */
    fun initialize(context: Context) {
        try {
            loadSettings(context)
            Log.d(TAG, "个性化亮度管理器初始化完成")
            Log.d(TAG, "当前设置: 启用=${currentState.isEnabled}, 自定义情境=${currentState.customSettings.size}个")
        } catch (e: Exception) {
            Log.e(TAG, "初始化个性化亮度管理器失败: ${e.message}")
            // 使用默认状态
            currentState = PersonalizedBrightnessState()
        }
    }
    
    /**
     * 检查是否启用了个性化亮度设置
     */
    fun isPersonalizedBrightnessEnabled(): Boolean {
        return currentState.isEnabled
    }
    
    /**
     * 启用或禁用个性化亮度设置
     */
    fun setPersonalizedBrightnessEnabled(context: Context, enabled: Boolean) {
        currentState = currentState.copy(isEnabled = enabled)
        saveSettings(context)
        Log.d(TAG, "个性化亮度设置已${if (enabled) "启用" else "禁用"}")
    }
    
    /**
     * 根据光照强度获取个性化亮度值
     * 
     * @param lightLevel 当前光照强度 (lux)
     * @param fallbackBrightness 如果没有个性化设置时的备用亮度
     * @return 个性化亮度值或备用亮度值
     */
    fun getPersonalizedBrightness(lightLevel: Float, fallbackBrightness: Float): Float {
        if (!currentState.isEnabled) {
            return fallbackBrightness
        }
        
        val bestScenario = BrightnessScenario.findBestMatch(lightLevel)
        val customSetting = currentState.customSettings[bestScenario.id]
        
        return if (customSetting != null && customSetting.isValidBrightness()) {
            Log.v(TAG, "使用个性化亮度: 情境=${bestScenario.displayName}, 亮度=${customSetting.getBrightnessPercentage()}")
            
            // 记录使用情况
            recordUsage(bestScenario.id)
            
            customSetting.customBrightness
        } else {
            Log.v(TAG, "使用默认亮度: 情境=${bestScenario.displayName}, 亮度=${(fallbackBrightness*100).toInt()}%")
            fallbackBrightness
        }
    }
    
    /**
     * 设置特定情境的个性化亮度
     */
    fun setScenarioBrightness(context: Context, scenarioId: String, brightness: Float) {
        if (brightness !in 0.0f..1.0f) {
            Log.w(TAG, "无效的亮度值: $brightness")
            return
        }
        
        val updatedSettings = currentState.customSettings.toMutableMap()
        updatedSettings[scenarioId] = CustomScenarioBrightness(
            scenarioId = scenarioId,
            customBrightness = brightness,
            lastModified = System.currentTimeMillis(),
            useCount = updatedSettings[scenarioId]?.useCount ?: 0
        )
        
        currentState = currentState.copy(customSettings = updatedSettings)
        saveSettings(context)
        
        val scenario = BrightnessScenario.values().find { it.id == scenarioId }
        Log.d(TAG, "更新情境亮度: ${scenario?.displayName ?: scenarioId} -> ${(brightness*100).toInt()}%")
    }
    
    /**
     * 获取特定情境的亮度设置
     */
    fun getScenarioBrightness(scenarioId: String): Float? {
        return currentState.customSettings[scenarioId]?.customBrightness
    }
    
    /**
     * 获取特定情境的亮度设置，如果没有则返回默认值
     */
    fun getScenarioBrightnessOrDefault(scenarioId: String): Float {
        val customBrightness = currentState.customSettings[scenarioId]?.customBrightness
        if (customBrightness != null) {
            return customBrightness
        }
        
        val scenario = BrightnessScenario.values().find { it.id == scenarioId }
        return scenario?.defaultBrightness ?: 0.25f
    }
    
    /**
     * 重置特定情境到默认亮度
     */
    fun resetScenarioBrightness(context: Context, scenarioId: String) {
        val updatedSettings = currentState.customSettings.toMutableMap()
        updatedSettings.remove(scenarioId)
        
        currentState = currentState.copy(customSettings = updatedSettings)
        saveSettings(context)
        
        val scenario = BrightnessScenario.values().find { it.id == scenarioId }
        Log.d(TAG, "重置情境亮度: ${scenario?.displayName ?: scenarioId}")
    }
    
    /**
     * 重置所有情境到默认亮度
     */
    fun resetAllScenarios(context: Context) {
        currentState = currentState.copy(customSettings = emptyMap())
        saveSettings(context)
        Log.d(TAG, "所有情境亮度已重置为默认值")
    }
    
    /**
     * 获取当前最佳匹配的情境
     */
    fun getCurrentBestScenario(lightLevel: Float): BrightnessScenario {
        return BrightnessScenario.findBestMatch(lightLevel)
    }
    
    /**
     * 获取个性化设置的统计信息
     */
    fun getPersonalizationStats(): String {
        val totalCustomized = currentState.customSettings.size
        val totalScenarios = BrightnessScenario.values().size
        val customizationRate = if (totalScenarios > 0) {
            (totalCustomized * 100) / totalScenarios
        } else 0
        
        val mostUsedScenario = currentState.customSettings.values
            .maxByOrNull { it.useCount }
            ?.let { setting ->
                BrightnessScenario.values().find { it.id == setting.scenarioId }?.displayName
            } ?: "无"
        
        return """
            个性化设置统计：
            • 启用状态：${if (currentState.isEnabled) "已启用" else "已禁用"}
            • 自定义情境：$totalCustomized/$totalScenarios (${customizationRate}%)
            • 总使用次数：${currentState.totalUsageCount}
            • 最常用情境：$mostUsedScenario
            • 最后使用：${currentState.lastUsedScenario ?: "无"}
        """.trimIndent()
    }
    
    /**
     * 记录使用情况
     */
    private fun recordUsage(scenarioId: String) {
        val updatedSettings = currentState.customSettings.toMutableMap()
        val currentSetting = updatedSettings[scenarioId]
        
        if (currentSetting != null) {
            updatedSettings[scenarioId] = currentSetting.copy(
                useCount = currentSetting.useCount + 1
            )
        }
        
        currentState = currentState.copy(
            customSettings = updatedSettings,
            lastUsedScenario = scenarioId,
            totalUsageCount = currentState.totalUsageCount + 1
        )
    }
    
    /**
     * 导出个性化设置
     */
    fun exportSettings(): String {
        return try {
            gson.toJson(currentState)
        } catch (e: Exception) {
            Log.e(TAG, "导出设置失败: ${e.message}")
            ""
        }
    }
    
    /**
     * 导入个性化设置
     */
    fun importSettings(context: Context, settingsJson: String): Boolean {
        return try {
            val importedState = gson.fromJson(settingsJson, PersonalizedBrightnessState::class.java)
            
            // 验证导入的数据
            val validatedSettings = importedState.customSettings.filter { (_, setting) ->
                setting.isValidBrightness()
            }
            
            currentState = importedState.copy(customSettings = validatedSettings)
            saveSettings(context)
            
            Log.d(TAG, "成功导入个性化设置: ${validatedSettings.size}个情境")
            true
        } catch (e: Exception) {
            Log.e(TAG, "导入设置失败: ${e.message}")
            false
        }
    }
    
    /**
     * 保存设置到SharedPreferences
     */
    private fun saveSettings(context: Context) {
        try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val settingsJson = gson.toJson(currentState)
            
            prefs.edit().apply {
                putString(KEY_CUSTOM_SCENARIOS, settingsJson)
                putBoolean(KEY_ENABLED, currentState.isEnabled)
                apply()
            }
            
            Log.v(TAG, "个性化设置已保存")
        } catch (e: Exception) {
            Log.e(TAG, "保存个性化设置失败: ${e.message}")
        }
    }
    
    /**
     * 从SharedPreferences加载设置
     */
    private fun loadSettings(context: Context) {
        try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val settingsJson = prefs.getString(KEY_CUSTOM_SCENARIOS, null)
            
            currentState = if (settingsJson != null) {
                gson.fromJson(settingsJson, PersonalizedBrightnessState::class.java)
            } else {
                PersonalizedBrightnessState(
                    isEnabled = prefs.getBoolean(KEY_ENABLED, false)
                )
            }
            
            Log.v(TAG, "个性化设置已加载")
        } catch (e: Exception) {
            Log.e(TAG, "加载个性化设置失败: ${e.message}")
            currentState = PersonalizedBrightnessState()
        }
    }
} 