package com.example.myapplication5

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import android.widget.Toast

/**
 * 通知操作接收器
 * 
 * 处理护眼服务通知栏中的操作按钮：
 * 1. 停止服务按钮
 * 2. 打开应用按钮
 * 
 * <AUTHOR>
 * @since 2.1
 */
class NotificationReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "NotificationReceiver"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.action
        Log.d(TAG, "收到通知操作: $action")
        
        when (action) {
            EyeCareBackgroundService.ACTION_STOP_SERVICE -> {
                handleStopService(context)
            }
            EyeCareBackgroundService.ACTION_OPEN_APP -> {
                handleOpenApp(context)
            }
        }
    }
    
    /**
     * 处理停止服务操作
     */
    private fun handleStopService(context: Context) {
        try {
            // 停止后台服务
            val serviceIntent = Intent(context, EyeCareBackgroundService::class.java)
            context.stopService(serviceIntent)
            
            // 显示提示信息
            Toast.makeText(context, "护眼保护已停止", Toast.LENGTH_SHORT).show()
            
            Log.d(TAG, "护眼服务已通过通知停止")
            
        } catch (e: Exception) {
            Log.e(TAG, "停止服务失败: ${e.message}")
            Toast.makeText(context, "停止服务失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 处理打开应用操作
     */
    private fun handleOpenApp(context: Context) {
        try {
            // 创建启动应用的Intent
            val appIntent = Intent(context, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or 
                       Intent.FLAG_ACTIVITY_CLEAR_TOP or 
                       Intent.FLAG_ACTIVITY_SINGLE_TOP
            }
            
            // 启动应用
            context.startActivity(appIntent)
            
            Log.d(TAG, "从通知启动应用")
            
        } catch (e: Exception) {
            Log.e(TAG, "启动应用失败: ${e.message}")
            Toast.makeText(context, "启动应用失败", Toast.LENGTH_SHORT).show()
        }
    }
} 