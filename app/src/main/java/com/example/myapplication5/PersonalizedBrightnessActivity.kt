package com.example.myapplication5

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.myapplication5.ui.theme.*

/**
 * 个性化亮度设置活动
 * 
 * 功能：
 * 1. 显示所有使用情境及其当前亮度设置
 * 2. 允许用户为每种情境自定义亮度值
 * 3. 提供实时预览功能
 * 4. 支持重置单个或所有情境设置
 * 5. 显示使用统计和建议
 * 
 * <AUTHOR> v1.0
 */
class PersonalizedBrightnessActivity : ComponentActivity() {
    
    private lateinit var brightnessController: BrightnessController
    
    // 状态变量
    private val isPersonalizedEnabled = mutableStateOf(false)
    private val scenarioList = mutableStateOf(ScenarioBrightnessManager.BrightnessScenario.getAllScenarios())
    private val customBrightnessMap = mutableStateOf(mapOf<String, Float>())
    private val showResetDialog = mutableStateOf(false)
    private val resetTargetScenario = mutableStateOf<String?>(null)
    private val showStatsDialog = mutableStateOf(false)
    
    companion object {
        /**
         * 启动个性化亮度设置界面
         */
        fun start(context: Context) {
            val intent = Intent(context, PersonalizedBrightnessActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // 初始化亮度控制器
        brightnessController = BrightnessController(this, this)
        
        // 初始化个性化亮度管理器
        ScenarioBrightnessManager.initialize(this)
        
        // 加载当前设置
        loadCurrentSettings()
        
        setContent {
            EyeCareTheme {
                Scaffold(
                    modifier = Modifier.fillMaxSize(),
                    topBar = {
                        PersonalizedBrightnessTopBar()
                    }
                ) { innerPadding ->
                    PersonalizedBrightnessScreen(
                        modifier = Modifier.padding(innerPadding)
                    )
                }
            }
        }
    }
    
    /**
     * 加载当前设置
     */
    private fun loadCurrentSettings() {
        isPersonalizedEnabled.value = ScenarioBrightnessManager.isPersonalizedBrightnessEnabled()
        
        // 加载每个情境的自定义亮度值
        val customMap = mutableMapOf<String, Float>()
        for (scenario in scenarioList.value) {
            val customBrightness = ScenarioBrightnessManager.getScenarioBrightness(scenario.id)
            if (customBrightness != null) {
                customMap[scenario.id] = customBrightness
            }
        }
        customBrightnessMap.value = customMap
    }
    
    /**
     * 顶部工具栏
     */
    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    private fun PersonalizedBrightnessTopBar() {
        TopAppBar(
            title = {
                Text(
                    text = "个性化亮度设置",
                    style = MaterialTheme.typography.titleLarge
                )
            },
            navigationIcon = {
                IconButton(onClick = { finish() }) {
                    Icon(
                        imageVector = Icons.Filled.ArrowBack,
                        contentDescription = "返回"
                    )
                }
            },
            actions = {
                // 统计信息按钮
                IconButton(onClick = { showStatsDialog.value = true }) {
                    Icon(
                        imageVector = Icons.Filled.Info,
                        contentDescription = "统计信息"
                    )
                }
                
                // 帮助按钮
                IconButton(onClick = { showHelpDialog() }) {
                    Icon(
                        imageVector = Icons.Filled.Info,
                        contentDescription = "帮助"
                    )
                }
            }
        )
    }
    
    /**
     * 主界面内容
     */
    @Composable
    private fun PersonalizedBrightnessScreen(modifier: Modifier = Modifier) {
        Column(
            modifier = modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 个性化开关和说明
            PersonalizedBrightnessHeader()
            
            // 情境亮度设置列表
            if (isPersonalizedEnabled.value) {
                ScenarioBrightnessSettingsList()
            } else {
                DisabledStateMessage()
            }
        }
        
        // 重置确认对话框
        if (showResetDialog.value) {
            ResetConfirmationDialog()
        }
        
        // 统计信息对话框
        if (showStatsDialog.value) {
            StatsDialog()
        }
    }
    
    /**
     * 个性化设置头部
     */
    @Composable
    private fun PersonalizedBrightnessHeader() {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainer
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(20.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "启用个性化亮度",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onSurface,
                            fontWeight = FontWeight.Bold
                        )
                        
                        Text(
                            text = "为不同使用场景自定义舒适的亮度值",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    Switch(
                        checked = isPersonalizedEnabled.value,
                        onCheckedChange = { enabled ->
                            togglePersonalizedBrightness(enabled)
                        },
                        modifier = Modifier.size(60.dp, 32.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 功能说明
                Text(
                    text = if (isPersonalizedEnabled.value) {
                        "✅ 个性化亮度已启用！根据环境光照自动选择您自定义的舒适亮度值"
                    } else {
                        "💡 启用后，您可以为不同的使用场景（如深夜阅读、办公环境、户外使用等）设置专属的舒适亮度值"
                    },
                    style = MaterialTheme.typography.bodySmall,
                    color = if (isPersonalizedEnabled.value) 
                        MaterialTheme.colorScheme.primary 
                    else 
                        MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
    
    /**
     * 情境亮度设置列表
     */
    @Composable
    private fun ScenarioBrightnessSettingsList() {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainer
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "情境亮度设置",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface,
                        fontWeight = FontWeight.Bold
                    )
                    
                    OutlinedButton(
                        onClick = {
                            resetTargetScenario.value = null // null表示重置全部
                            showResetDialog.value = true
                        }
                    ) {
                        Text("重置全部")
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 情境列表
                LazyColumn(
                    modifier = Modifier.heightIn(max = 600.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(scenarioList.value) { scenario ->
                        ScenarioBrightnessItem(scenario = scenario)
                    }
                }
            }
        }
    }
    
    /**
     * 单个情境亮度设置项
     */
    @Composable
    private fun ScenarioBrightnessItem(scenario: ScenarioBrightnessManager.BrightnessScenario) {
        val currentBrightness = customBrightnessMap.value[scenario.id] ?: scenario.defaultBrightness
        val brightnessState = remember { mutableStateOf(currentBrightness) }
        
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                // 情境信息头部
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = scenario.icon,
                            fontSize = 24.sp
                        )
                        
                        Spacer(modifier = Modifier.width(12.dp))
                        
                        Column {
                            Text(
                                text = scenario.displayName,
                                style = MaterialTheme.typography.titleSmall,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = FontWeight.Medium
                            )
                            
                            Text(
                                text = scenario.description,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                    
                    // 重置按钮
                    IconButton(
                        onClick = {
                            resetTargetScenario.value = scenario.id
                            showResetDialog.value = true
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Refresh,
                            contentDescription = "重置",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 亮度调节部分
                Text(
                    text = "亮度设置: ${(brightnessState.value * 100).toInt()}% " + 
                           if (customBrightnessMap.value.containsKey(scenario.id)) "(已自定义)" else "(默认值)",
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (customBrightnessMap.value.containsKey(scenario.id)) 
                        MaterialTheme.colorScheme.primary 
                    else 
                        MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Slider(
                    value = brightnessState.value,
                    onValueChange = { brightness ->
                        brightnessState.value = brightness
                    },
                    onValueChangeFinished = {
                        saveScenarioBrightness(scenario.id, brightnessState.value)
                    },
                    valueRange = 0.001f..1.0f,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 情境提示
                Text(
                    text = "💡 ${scenario.tips}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                // 光照范围信息
                Text(
                    text = "适用光照范围: ${scenario.lightRangeMin.toInt()}-${if (scenario.lightRangeMax == Float.MAX_VALUE) "∞" else scenario.lightRangeMax.toInt()} lux",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
    
    /**
     * 禁用状态提示
     */
    @Composable
    private fun DisabledStateMessage() {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainer
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Filled.Settings,
                    contentDescription = "设置",
                    modifier = Modifier.size(64.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "个性化亮度设置未启用",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "请开启上方的个性化亮度开关，即可为不同使用场景自定义舒适的亮度值",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
    
    /**
     * 重置确认对话框
     */
    @Composable
    private fun ResetConfirmationDialog() {
        AlertDialog(
            onDismissRequest = { showResetDialog.value = false },
            title = {
                Text(
                    text = if (resetTargetScenario.value == null) "重置所有设置" else "重置情境设置",
                    style = MaterialTheme.typography.titleMedium
                )
            },
            text = {
                val scenarioName = resetTargetScenario.value?.let { id ->
                    scenarioList.value.find { it.id == id }?.displayName
                }
                
                Text(
                    text = if (resetTargetScenario.value == null) {
                        "确定要重置所有情境的亮度设置为默认值吗？\n\n此操作无法撤销。"
                    } else {
                        "确定要重置「$scenarioName」的亮度设置为默认值吗？\n\n此操作无法撤销。"
                    },
                    style = MaterialTheme.typography.bodyMedium
                )
            },
            confirmButton = {
                Button(
                    onClick = {
                        performReset()
                        showResetDialog.value = false
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("确定重置")
                }
            },
            dismissButton = {
                OutlinedButton(
                    onClick = { showResetDialog.value = false }
                ) {
                    Text("取消")
                }
            }
        )
    }
    
    /**
     * 统计信息对话框
     */
    @Composable
    private fun StatsDialog() {
        AlertDialog(
            onDismissRequest = { showStatsDialog.value = false },
            title = {
                Text(
                    text = "个性化设置统计",
                    style = MaterialTheme.typography.titleMedium
                )
            },
            text = {
                Text(
                    text = brightnessController.getPersonalizationStats(),
                    style = MaterialTheme.typography.bodyMedium
                )
            },
            confirmButton = {
                Button(
                    onClick = { showStatsDialog.value = false }
                ) {
                    Text("知道了")
                }
            }
        )
    }
    
    // === 辅助方法 ===
    
    /**
     * 切换个性化亮度设置
     */
    private fun togglePersonalizedBrightness(enabled: Boolean) {
        isPersonalizedEnabled.value = enabled
        ScenarioBrightnessManager.setPersonalizedBrightnessEnabled(this, enabled)
        brightnessController.setPersonalizedBrightnessEnabled(enabled)
        
        val message = if (enabled) "个性化亮度设置已启用" else "个性化亮度设置已禁用"
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 保存情境亮度设置
     */
    private fun saveScenarioBrightness(scenarioId: String, brightness: Float) {
        ScenarioBrightnessManager.setScenarioBrightness(this, scenarioId, brightness)
        
        // 更新本地状态
        val updatedMap = customBrightnessMap.value.toMutableMap()
        updatedMap[scenarioId] = brightness
        customBrightnessMap.value = updatedMap
        
        val scenario = scenarioList.value.find { it.id == scenarioId }
        Toast.makeText(
            this, 
            "已保存${scenario?.displayName ?: ""}亮度设置: ${(brightness * 100).toInt()}%", 
            Toast.LENGTH_SHORT
        ).show()
    }
    
    /**
     * 执行重置操作
     */
    private fun performReset() {
        if (resetTargetScenario.value == null) {
            // 重置所有设置
            ScenarioBrightnessManager.resetAllScenarios(this)
            customBrightnessMap.value = emptyMap()
            Toast.makeText(this, "所有情境亮度设置已重置为默认值", Toast.LENGTH_LONG).show()
        } else {
            // 重置单个情境
            val scenarioId = resetTargetScenario.value!!
            ScenarioBrightnessManager.resetScenarioBrightness(this, scenarioId)
            
            val updatedMap = customBrightnessMap.value.toMutableMap()
            updatedMap.remove(scenarioId)
            customBrightnessMap.value = updatedMap
            
            val scenario = scenarioList.value.find { it.id == scenarioId }
            Toast.makeText(this, "${scenario?.displayName ?: ""}亮度设置已重置为默认值", Toast.LENGTH_SHORT).show()
        }
        
        resetTargetScenario.value = null
    }
    
    /**
     * 显示帮助对话框
     */
    private fun showHelpDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("个性化亮度设置帮助")
            .setMessage("""
                🎯 功能说明：
                • 为不同使用场景设置专属的舒适亮度值
                • 应用会根据环境光照自动选择对应的亮度设置
                • 完全基于您的个人使用习惯和眼部敏感度
                
                📱 使用方法：
                1. 开启"启用个性化亮度"开关
                2. 针对常用场景调整亮度滑块到舒适值
                3. 系统会自动保存您的设置
                4. 日常使用中会根据环境自动匹配
                
                💡 使用建议：
                • 建议在实际使用环境中调节亮度
                • 从最常用的场景开始设置
                • 可随时调整，系统会记住您的偏好
                • 如不满意可随时重置为默认值
                
                ⚠️ 注意事项：
                • 过低的亮度可能影响视觉清晰度
                • 建议根据干眼症严重程度合理设置
                • 重置操作无法撤销，请谨慎操作
            """.trimIndent())
            .setPositiveButton("知道了") { dialog, _ -> 
                dialog.dismiss() 
            }
            .show()
    }
} 