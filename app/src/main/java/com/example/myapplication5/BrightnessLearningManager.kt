package com.example.myapplication5

import android.content.Context
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlin.math.abs
import kotlin.math.pow

/**
 * 智能亮度学习管理器 - 用户习惯学习和个性化推荐系统
 * 
 * 核心功能：
 * 1. 记录用户在不同场景下的手动调节行为
 * 2. 分析用户的亮度偏好模式
 * 3. 智能推荐个性化亮度设置
 * 4. 提供学习进度和使用建议
 * 5. 自动优化和完善个性化设置
 * 
 * 学习算法：
 * - 基于时间加权的学习算法，优先考虑最近的调节行为
 * - 场景聚类分析，识别用户的使用模式
 * - 置信度评估，确保推荐的可靠性
 * - 渐进式学习，避免过度调整
 * 
 * <AUTHOR> v1.0
 * @since 1.0 - 支持用户习惯智能学习
 */
object BrightnessLearningManager {
    
    private const val TAG = "BrightnessLearning"
    private const val PREFS_NAME = "brightness_learning_data"
    private const val KEY_LEARNING_DATA = "learning_records"
    private const val KEY_LEARNING_ENABLED = "learning_enabled"
    private const val KEY_AUTO_APPLY_ENABLED = "auto_apply_enabled"
    
    /** 学习阶段定义 */
    enum class LearningPhase {
        INITIAL,        // 初始阶段：0-10次调节
        LEARNING,       // 学习阶段：10-50次调节
        OPTIMIZING,     // 优化阶段：50-100次调节
        STABLE          // 稳定阶段：100+次调节
    }
    
    /** 学习信心等级 */
    enum class ConfidenceLevel {
        LOW,           // 信心不足：少于5次相同场景调节
        MEDIUM,        // 中等信心：5-15次相同场景调节
        HIGH,          // 高信心：15-30次相同场景调节
        VERY_HIGH      // 很高信心：30+次相同场景调节
    }
    
    /**
     * 用户调节记录数据类
     */
    data class BrightnessAdjustmentRecord(
        val timestamp: Long,                    // 调节时间戳
        val scenarioId: String,                 // 场景ID
        val lightLevel: Float,                  // 光照强度 (lux)
        val originalBrightness: Float,          // 调节前亮度
        val adjustedBrightness: Float,          // 调节后亮度
        val adjustmentDelta: Float,             // 调节幅度
        val eyeCareMode: String,                // 护眼模式
        val timeOfDay: Int,                     // 一天中的小时
        val sessionDuration: Long = 0L,         // 本次使用时长（毫秒）
        val userInitiated: Boolean = true,      // 是否用户主动调节
        val confidence: Float = 1.0f            // 记录置信度 (0.0-1.0)
    ) {
        /**
         * 获取调节类型描述
         */
        fun getAdjustmentTypeDescription(): String {
            return when {
                adjustmentDelta > 0.05f -> "大幅提亮"
                adjustmentDelta > 0.02f -> "适度提亮"
                adjustmentDelta > 0.005f -> "微调提亮"
                adjustmentDelta < -0.05f -> "大幅调暗"
                adjustmentDelta < -0.02f -> "适度调暗"
                adjustmentDelta < -0.005f -> "微调调暗"
                else -> "几乎无变化"
            }
        }
        
        /**
         * 检查记录是否有效
         */
        fun isValid(): Boolean {
            return originalBrightness in 0.0f..1.0f &&
                   adjustedBrightness in 0.0f..1.0f &&
                   lightLevel >= 0.0f &&
                   timeOfDay in 0..23 &&
                   confidence in 0.0f..1.0f
        }
        
        /**
         * 计算时间权重（最近的记录权重更高）
         */
        fun getTimeWeight(): Float {
            val ageInDays = (System.currentTimeMillis() - timestamp) / (24 * 60 * 60 * 1000f)
            return when {
                ageInDays <= 1 -> 1.0f      // 1天内：100%权重
                ageInDays <= 3 -> 0.8f      // 3天内：80%权重
                ageInDays <= 7 -> 0.6f      // 7天内：60%权重
                ageInDays <= 14 -> 0.4f     // 14天内：40%权重
                ageInDays <= 30 -> 0.2f     // 30天内：20%权重
                else -> 0.1f                // 30天以上：10%权重
            }
        }
    }
    
    /**
     * 场景学习数据
     */
    data class ScenarioLearningData(
        val scenarioId: String,                 // 场景ID
        val adjustmentRecords: List<BrightnessAdjustmentRecord>, // 调节记录
        val learnedBrightness: Float,           // 学习到的最佳亮度
        val confidenceLevel: ConfidenceLevel,   // 置信度等级
        val lastUpdated: Long,                  // 最后更新时间
        val totalAdjustments: Int,              // 总调节次数
        val avgSessionDuration: Long            // 平均使用时长
    ) {
        /**
         * 计算学习完成度 (0.0-1.0)
         */
        fun getLearningCompleteness(): Float {
            return when (confidenceLevel) {
                ConfidenceLevel.LOW -> totalAdjustments / 5.0f
                ConfidenceLevel.MEDIUM -> 0.2f + (totalAdjustments - 5) / 10.0f * 0.3f
                ConfidenceLevel.HIGH -> 0.5f + (totalAdjustments - 15) / 15.0f * 0.3f
                ConfidenceLevel.VERY_HIGH -> 0.8f + minOf((totalAdjustments - 30) / 20.0f * 0.2f, 0.2f)
            }.coerceIn(0.0f, 1.0f)
        }
        
        /**
         * 获取置信度描述
         */
        fun getConfidenceDescription(): String {
            return when (confidenceLevel) {
                ConfidenceLevel.LOW -> "信心不足"
                ConfidenceLevel.MEDIUM -> "中等信心"
                ConfidenceLevel.HIGH -> "高信心"
                ConfidenceLevel.VERY_HIGH -> "很高信心"
            }
        }
    }
    
    /**
     * 学习系统状态
     */
    data class LearningSystemState(
        val isLearningEnabled: Boolean = true,      // 是否启用学习
        val isAutoApplyEnabled: Boolean = false,    // 是否自动应用学习结果
        val scenarioLearningData: Map<String, ScenarioLearningData> = emptyMap(), // 场景学习数据
        val totalAdjustments: Int = 0,              // 总调节次数
        val learningStartTime: Long = System.currentTimeMillis(), // 学习开始时间
        val lastAnalysisTime: Long = 0L             // 最后分析时间
    ) {
        /**
         * 获取当前学习阶段
         */
        fun getCurrentPhase(): LearningPhase {
            return when (totalAdjustments) {
                in 0..9 -> LearningPhase.INITIAL
                in 10..49 -> LearningPhase.LEARNING
                in 50..99 -> LearningPhase.OPTIMIZING
                else -> LearningPhase.STABLE
            }
        }
        
        /**
         * 获取学习进度描述
         */
        fun getProgressDescription(): String {
            val phase = getCurrentPhase()
            return when (phase) {
                LearningPhase.INITIAL -> "初始学习阶段 ($totalAdjustments/10)"
                LearningPhase.LEARNING -> "深度学习阶段 ($totalAdjustments/50)"
                LearningPhase.OPTIMIZING -> "优化调整阶段 ($totalAdjustments/100)"
                LearningPhase.STABLE -> "稳定成熟阶段 ($totalAdjustments 次调节)"
            }
        }
        
        /**
         * 计算总体学习完成度
         */
        fun getOverallCompleteness(): Float {
            val phaseProgress = when (getCurrentPhase()) {
                LearningPhase.INITIAL -> totalAdjustments / 10.0f * 0.2f
                LearningPhase.LEARNING -> 0.2f + (totalAdjustments - 10) / 40.0f * 0.3f
                LearningPhase.OPTIMIZING -> 0.5f + (totalAdjustments - 50) / 50.0f * 0.3f
                LearningPhase.STABLE -> 0.8f + minOf((totalAdjustments - 100) / 100.0f * 0.2f, 0.2f)
            }
            return phaseProgress.coerceIn(0.0f, 1.0f)
        }
    }
    
    /** 当前学习系统状态 */
    private var currentState = LearningSystemState()
    
    /** Gson实例用于序列化 */
    private val gson = Gson()
    
    /** 最小有效调节阈值 */
    private const val MIN_ADJUSTMENT_THRESHOLD = 0.005f  // 0.5%
    
    /** 最大记录保存数量（避免数据过大） */
    private const val MAX_RECORDS_PER_SCENARIO = 100
    
    /** 分析间隔（毫秒） */
    private const val ANALYSIS_INTERVAL = 6 * 60 * 60 * 1000L  // 6小时
    
    /**
     * 初始化学习管理器
     */
    fun initialize(context: Context) {
        try {
            loadLearningData(context)
            Log.d(TAG, "智能学习管理器初始化完成")
            Log.d(TAG, "学习状态: ${currentState.getProgressDescription()}")
        } catch (e: Exception) {
            Log.e(TAG, "初始化学习管理器失败: ${e.message}")
            currentState = LearningSystemState()
        }
    }
    
    /**
     * 记录用户的亮度调节行为
     */
    fun recordAdjustment(
        context: Context,
        scenarioId: String,
        lightLevel: Float,
        originalBrightness: Float,
        adjustedBrightness: Float,
        eyeCareMode: String,
        userInitiated: Boolean = true
    ) {
        if (!currentState.isLearningEnabled) {
            Log.v(TAG, "学习功能已禁用，跳过记录")
            return
        }
        
        val adjustmentDelta = adjustedBrightness - originalBrightness
        
        // 过滤微小调节，避免噪声数据
        if (abs(adjustmentDelta) < MIN_ADJUSTMENT_THRESHOLD) {
            Log.v(TAG, "调节幅度过小，跳过记录: ${(adjustmentDelta * 100).toInt()}%")
            return
        }
        
        val record = BrightnessAdjustmentRecord(
            timestamp = System.currentTimeMillis(),
            scenarioId = scenarioId,
            lightLevel = lightLevel,
            originalBrightness = originalBrightness,
            adjustedBrightness = adjustedBrightness,
            adjustmentDelta = adjustmentDelta,
            eyeCareMode = eyeCareMode,
            timeOfDay = java.util.Calendar.getInstance().get(java.util.Calendar.HOUR_OF_DAY),
            userInitiated = userInitiated
        )
        
        if (!record.isValid()) {
            Log.w(TAG, "无效的调节记录，跳过保存")
            return
        }
        
        addRecordToLearningData(context, record)
        
        Log.d(TAG, "记录用户调节: ${getScenarioDisplayName(scenarioId)} ${record.getAdjustmentTypeDescription()} ${(adjustmentDelta * 100).toInt()}%")
        
        // 检查是否需要进行分析
        checkAndPerformAnalysis(context)
    }
    
    /**
     * 获取学习到的个性化亮度建议
     */
    fun getLearnedBrightness(scenarioId: String, fallbackBrightness: Float): Float? {
        if (!currentState.isAutoApplyEnabled) {
            return null
        }
        
        val learningData = currentState.scenarioLearningData[scenarioId]
        
        return if (learningData != null && learningData.confidenceLevel != ConfidenceLevel.LOW) {
            Log.v(TAG, "使用学习到的亮度: ${getScenarioDisplayName(scenarioId)} -> ${(learningData.learnedBrightness * 100).toInt()}% (${learningData.getConfidenceDescription()})")
            learningData.learnedBrightness
        } else {
            null
        }
    }
    
    /**
     * 获取学习建议（手动应用模式）
     */
    fun getLearningRecommendation(scenarioId: String): Pair<Float, String>? {
        val learningData = currentState.scenarioLearningData[scenarioId] ?: return null
        
        if (learningData.confidenceLevel == ConfidenceLevel.LOW) {
            return null
        }
        
        val confidence = learningData.getConfidenceDescription()
        val completeness = (learningData.getLearningCompleteness() * 100).toInt()
        val scenarioName = getScenarioDisplayName(scenarioId)
        
        val recommendation = """
            💡 智能学习建议
            
            场景：$scenarioName
            建议亮度：${(learningData.learnedBrightness * 100).toInt()}%
            学习信心：$confidence
            学习完成度：$completeness%
            调节次数：${learningData.totalAdjustments}次
            
            基于您在该场景下${learningData.totalAdjustments}次的调节习惯，
            建议将此场景的亮度设置为${(learningData.learnedBrightness * 100).toInt()}%
        """.trimIndent()
        
        return Pair(learningData.learnedBrightness, recommendation)
    }
    
    /**
     * 启用或禁用智能学习
     */
    fun setLearningEnabled(context: Context, enabled: Boolean) {
        currentState = currentState.copy(isLearningEnabled = enabled)
        saveLearningData(context)
        Log.d(TAG, "智能学习已${if (enabled) "启用" else "禁用"}")
    }
    
    /**
     * 启用或禁用自动应用学习结果
     */
    fun setAutoApplyEnabled(context: Context, enabled: Boolean) {
        currentState = currentState.copy(isAutoApplyEnabled = enabled)
        saveLearningData(context)
        Log.d(TAG, "自动应用学习结果已${if (enabled) "启用" else "禁用"}")
    }
    
    /**
     * 应用所有学习到的设置到个性化亮度管理器
     */
    fun applyAllLearningToPersonalization(context: Context): Int {
        var appliedCount = 0
        
        currentState.scenarioLearningData.forEach { (scenarioId, learningData) ->
            if (learningData.confidenceLevel != ConfidenceLevel.LOW) {
                ScenarioBrightnessManager.setScenarioBrightness(
                    context, 
                    scenarioId, 
                    learningData.learnedBrightness
                )
                appliedCount++
                Log.d(TAG, "应用学习结果: ${getScenarioDisplayName(scenarioId)} -> ${(learningData.learnedBrightness * 100).toInt()}%")
            }
        }
        
        if (appliedCount > 0) {
            // 启用个性化亮度设置
            ScenarioBrightnessManager.setPersonalizedBrightnessEnabled(context, true)
            Log.d(TAG, "已应用 $appliedCount 个场景的学习结果到个性化设置")
        }
        
        return appliedCount
    }
    
    /**
     * 获取学习统计信息
     */
    fun getLearningStats(): String {
        val totalScenarios = ScenarioBrightnessManager.BrightnessScenario.values().size
        val learnedScenarios = currentState.scenarioLearningData.size
        val highConfidenceScenarios = currentState.scenarioLearningData.values.count { 
            it.confidenceLevel in listOf(ConfidenceLevel.HIGH, ConfidenceLevel.VERY_HIGH) 
        }
        
        val learningDays = (System.currentTimeMillis() - currentState.learningStartTime) / (24 * 60 * 60 * 1000L)
        val avgAdjustmentsPerDay = if (learningDays > 0) currentState.totalAdjustments / learningDays.toFloat() else 0f
        
        return """
            📊 智能学习统计
            
            • 学习阶段：${currentState.getProgressDescription()}
            • 总体完成度：${(currentState.getOverallCompleteness() * 100).toInt()}%
            • 学习场景：$learnedScenarios/$totalScenarios
            • 高信心场景：$highConfidenceScenarios
            • 学习天数：${learningDays}天
            • 日均调节：${String.format("%.1f", avgAdjustmentsPerDay)}次
            • 自动应用：${if (currentState.isAutoApplyEnabled) "已启用" else "已禁用"}
        """.trimIndent()
    }
    
    /**
     * 获取详细的场景学习报告
     */
    fun getDetailedLearningReport(): String {
        if (currentState.scenarioLearningData.isEmpty()) {
            return "暂无学习数据，请先使用手动调节功能进行一段时间的学习。"
        }
        
        val report = StringBuilder()
        report.append("📋 详细学习报告\n\n")
        
        currentState.scenarioLearningData.values
            .sortedByDescending { it.totalAdjustments }
            .forEach { data ->
                val scenarioName = getScenarioDisplayName(data.scenarioId)
                val completeness = (data.getLearningCompleteness() * 100).toInt()
                
                report.append("🎯 $scenarioName\n")
                report.append("   建议亮度：${(data.learnedBrightness * 100).toInt()}%\n")
                report.append("   学习信心：${data.getConfidenceDescription()}\n")
                report.append("   完成度：$completeness%\n")
                report.append("   调节次数：${data.totalAdjustments}次\n")
                report.append("   最后更新：${formatTime(data.lastUpdated)}\n\n")
            }
        
        return report.toString()
    }
    
    /**
     * 清除所有学习数据
     */
    fun clearAllLearningData(context: Context) {
        currentState = LearningSystemState(
            isLearningEnabled = currentState.isLearningEnabled,
            isAutoApplyEnabled = currentState.isAutoApplyEnabled,
            learningStartTime = System.currentTimeMillis()
        )
        saveLearningData(context)
        Log.d(TAG, "所有学习数据已清除")
    }
    
    /**
     * 获取使用建议
     */
    fun getUsageAdvice(): String {
        val phase = currentState.getCurrentPhase()
        
        return when (phase) {
            LearningPhase.INITIAL -> {
                """
                🚀 开始智能学习
                
                为了更好地学习您的习惯，建议：
                • 在不同时间和环境下手动调节亮度
                • 每个场景至少调节3-5次
                • 调节时请根据您的舒适度进行
                • 完成10次调节后将进入深度学习阶段
                
                当前进度：${currentState.totalAdjustments}/10次
                """.trimIndent()
            }
            LearningPhase.LEARNING -> {
                """
                🧠 深度学习中
                
                学习系统正在分析您的使用习惯：
                • 已记录${currentState.totalAdjustments}次调节
                • 继续在不同场景下调节亮度
                • 系统将逐步识别您的偏好模式
                • 50次调节后将进入优化阶段
                
                已学习场景：${currentState.scenarioLearningData.size}个
                """.trimIndent()
            }
            LearningPhase.OPTIMIZING -> {
                """
                ⚡ 优化调整中
                
                系统已初步了解您的习惯：
                • 建议启用"自动应用学习结果"
                • 或手动应用高信心的学习建议
                • 继续调节以提高学习精度
                • 100次调节后将进入稳定阶段
                
                高信心场景：${currentState.scenarioLearningData.values.count { it.confidenceLevel in listOf(ConfidenceLevel.HIGH, ConfidenceLevel.VERY_HIGH) }}个
                """.trimIndent()
            }
            LearningPhase.STABLE -> {
                """
                ✅ 学习完成
                
                智能学习系统已成熟：
                • 所有主要场景都有可靠的学习数据
                • 建议启用"自动应用学习结果"
                • 系统将持续微调和优化
                • 享受完全个性化的护眼体验
                
                学习完成度：${(currentState.getOverallCompleteness() * 100).toInt()}%
                """.trimIndent()
            }
        }
    }
    
    // === 私有方法 ===
    
    /**
     * 添加记录到学习数据
     */
    private fun addRecordToLearningData(context: Context, record: BrightnessAdjustmentRecord) {
        val existingData = currentState.scenarioLearningData[record.scenarioId]
        val updatedRecords = if (existingData != null) {
            val newRecords = (existingData.adjustmentRecords + record)
                .sortedByDescending { it.timestamp }
                .take(MAX_RECORDS_PER_SCENARIO)
            newRecords
        } else {
            listOf(record)
        }
        
        val newLearningData = updateScenarioLearningData(record.scenarioId, updatedRecords)
        
        val updatedScenarioData = currentState.scenarioLearningData.toMutableMap()
        updatedScenarioData[record.scenarioId] = newLearningData
        
        currentState = currentState.copy(
            scenarioLearningData = updatedScenarioData,
            totalAdjustments = currentState.totalAdjustments + 1
        )
        
        saveLearningData(context)
    }
    
    /**
     * 更新场景学习数据
     */
    private fun updateScenarioLearningData(scenarioId: String, records: List<BrightnessAdjustmentRecord>): ScenarioLearningData {
        if (records.isEmpty()) {
            return ScenarioLearningData(
                scenarioId = scenarioId,
                adjustmentRecords = emptyList(),
                learnedBrightness = 0.25f,
                confidenceLevel = ConfidenceLevel.LOW,
                lastUpdated = System.currentTimeMillis(),
                totalAdjustments = 0,
                avgSessionDuration = 0L
            )
        }
        
        // 计算时间加权平均亮度
        val weightedSum = records.sumOf { record ->
            (record.adjustedBrightness * record.getTimeWeight() * record.confidence).toDouble()
        }
        val totalWeight = records.sumOf { (it.getTimeWeight() * it.confidence).toDouble() }
        
        val learnedBrightness = if (totalWeight > 0) {
            (weightedSum / totalWeight).toFloat().coerceIn(0.0f, 1.0f)
        } else {
            records.map { it.adjustedBrightness }.average().toFloat()
        }
        
        // 计算置信度等级
        val confidenceLevel = when (records.size) {
            in 0..4 -> ConfidenceLevel.LOW
            in 5..14 -> ConfidenceLevel.MEDIUM
            in 15..29 -> ConfidenceLevel.HIGH
            else -> ConfidenceLevel.VERY_HIGH
        }
        
        // 计算平均使用时长
        val avgSessionDuration = if (records.isNotEmpty()) {
            records.map { it.sessionDuration }.average().toLong()
        } else 0L
        
        return ScenarioLearningData(
            scenarioId = scenarioId,
            adjustmentRecords = records,
            learnedBrightness = learnedBrightness,
            confidenceLevel = confidenceLevel,
            lastUpdated = System.currentTimeMillis(),
            totalAdjustments = records.size,
            avgSessionDuration = avgSessionDuration
        )
    }
    
    /**
     * 检查并执行分析
     */
    private fun checkAndPerformAnalysis(context: Context) {
        val now = System.currentTimeMillis()
        if (now - currentState.lastAnalysisTime > ANALYSIS_INTERVAL) {
            performDetailedAnalysis(context)
        }
    }
    
    /**
     * 执行详细分析
     */
    private fun performDetailedAnalysis(context: Context) {
        Log.d(TAG, "执行智能学习分析...")
        
        // 重新计算所有场景的学习数据
        val updatedScenarioData = currentState.scenarioLearningData.mapValues { (scenarioId, data) ->
            updateScenarioLearningData(scenarioId, data.adjustmentRecords)
        }
        
        currentState = currentState.copy(
            scenarioLearningData = updatedScenarioData,
            lastAnalysisTime = System.currentTimeMillis()
        )
        
        saveLearningData(context)
        
        Log.d(TAG, "智能分析完成，已更新${updatedScenarioData.size}个场景的学习数据")
    }
    
    /**
     * 获取场景显示名称
     */
    private fun getScenarioDisplayName(scenarioId: String): String {
        return ScenarioBrightnessManager.BrightnessScenario.values()
            .find { it.id == scenarioId }?.displayName ?: scenarioId
    }
    
    /**
     * 格式化时间
     */
    private fun formatTime(timestamp: Long): String {
        val diff = System.currentTimeMillis() - timestamp
        val days = diff / (24 * 60 * 60 * 1000)
        val hours = (diff % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000)
        
        return when {
            days > 0 -> "${days}天前"
            hours > 0 -> "${hours}小时前"
            else -> "刚刚"
        }
    }
    
    /**
     * 保存学习数据
     */
    private fun saveLearningData(context: Context) {
        try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val dataJson = gson.toJson(currentState)
            
            prefs.edit().apply {
                putString(KEY_LEARNING_DATA, dataJson)
                putBoolean(KEY_LEARNING_ENABLED, currentState.isLearningEnabled)
                putBoolean(KEY_AUTO_APPLY_ENABLED, currentState.isAutoApplyEnabled)
                apply()
            }
            
            Log.v(TAG, "学习数据已保存")
        } catch (e: Exception) {
            Log.e(TAG, "保存学习数据失败: ${e.message}")
        }
    }
    
    /**
     * 加载学习数据
     */
    private fun loadLearningData(context: Context) {
        try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val dataJson = prefs.getString(KEY_LEARNING_DATA, null)
            
            currentState = if (dataJson != null) {
                gson.fromJson(dataJson, LearningSystemState::class.java)
            } else {
                LearningSystemState(
                    isLearningEnabled = prefs.getBoolean(KEY_LEARNING_ENABLED, true),
                    isAutoApplyEnabled = prefs.getBoolean(KEY_AUTO_APPLY_ENABLED, false)
                )
            }
            
            Log.v(TAG, "学习数据已加载")
        } catch (e: Exception) {
            Log.e(TAG, "加载学习数据失败: ${e.message}")
            currentState = LearningSystemState()
        }
    }
    
    // === 公开API ===
    
    /**
     * 检查是否启用了学习功能
     */
    fun isLearningEnabled(): Boolean = currentState.isLearningEnabled
    
    /**
     * 检查是否启用了自动应用
     */
    fun isAutoApplyEnabled(): Boolean = currentState.isAutoApplyEnabled
    
    /**
     * 获取当前学习阶段
     */
    fun getCurrentPhase(): LearningPhase = currentState.getCurrentPhase()
    
    /**
     * 获取总调节次数
     */
    fun getTotalAdjustments(): Int = currentState.totalAdjustments
    
    /**
     * 获取学习场景数量
     */
    fun getLearnedScenariosCount(): Int = currentState.scenarioLearningData.size
    
    /**
     * 获取高信心场景数量
     */
    fun getHighConfidenceScenariosCount(): Int {
        return currentState.scenarioLearningData.values.count { 
            it.confidenceLevel in listOf(ConfidenceLevel.HIGH, ConfidenceLevel.VERY_HIGH) 
        }
    }
} 