package com.example.myapplication5

import android.content.Context
import android.database.ContentObserver
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import kotlinx.coroutines.*

/**
 * 系统亮度监听器 - 监听用户手动调节系统亮度的行为
 * 
 * 核心功能：
 * 1. 监听系统亮度设置变化
 * 2. 区分用户手动调节和应用自动调节
 * 3. 记录用户的手动调节习惯
 * 4. 与智能学习系统集成
 * 5. 提供调节行为分析
 * 
 * 工作原理：
 * - 使用ContentObserver监听Settings.System.SCREEN_BRIGHTNESS变化
 * - 通过时间戳和调节模式区分调节来源
 * - 结合光照传感器数据分析调节场景
 * - 自动记录到智能学习系统
 * 
 * <AUTHOR> v1.0
 * @since 1.0 - 支持系统级亮度调节学习
 */
class SystemBrightnessMonitor(
    private val context: Context,
    private val lightnessController: BrightnessController,
    private val lightSensorManager: LightSensorManager
) {
    
    companion object {
        private const val TAG = "SystemBrightnessMonitor"
        
        /** 调节间隔阈值 - 用于过滤频繁调节 */
        private const val ADJUSTMENT_INTERVAL_THRESHOLD = 1000L  // 1秒
        
        /** 应用调节标记超时 - 超过此时间的调节视为用户手动 */
        private const val APP_ADJUSTMENT_TIMEOUT = 3000L  // 3秒
        
        /** 最小调节幅度阈值 - 过滤微小调节 */
        private const val MIN_BRIGHTNESS_CHANGE = 0.02f  // 2%
    }
    
    /** 亮度变化监听器 */
    private var brightnessObserver: BrightnessContentObserver? = null
    
    /** 监听协程作用域 */
    private val monitorScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    
    /** 上次记录的亮度值 */
    private var lastRecordedBrightness = 0f
    
    /** 上次调节时间 */
    private var lastAdjustmentTime = 0L
    
    /** 应用最后一次自动调节的时间 */
    private var lastAppAdjustmentTime = 0L
    
    /** 是否正在监听 */
    private var isMonitoring = false
    
    /** 调节统计 */
    private var userAdjustmentCount = 0
    private var filteredAdjustmentCount = 0
    private var recordedAdjustmentCount = 0
    
    /**
     * 亮度变化内容观察者
     */
    private inner class BrightnessContentObserver(handler: Handler) : ContentObserver(handler) {
        override fun onChange(selfChange: Boolean, uri: Uri?) {
            super.onChange(selfChange, uri)
            
            Log.v(TAG, "检测到系统亮度变化: selfChange=$selfChange, uri=$uri")
            
            // 在协程中处理亮度变化
            monitorScope.launch {
                handleBrightnessChange()
            }
        }
    }
    
    /**
     * 开始监听系统亮度变化
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Log.w(TAG, "系统亮度监听已经启动")
            return
        }
        
        try {
            // 创建内容观察者
            val handler = Handler(Looper.getMainLooper())
            brightnessObserver = BrightnessContentObserver(handler)
            
            // 注册监听器
            val uri = Settings.System.getUriFor(Settings.System.SCREEN_BRIGHTNESS)
            context.contentResolver.registerContentObserver(
                uri,
                false,
                brightnessObserver!!
            )
            
            // 初始化当前亮度值
            lastRecordedBrightness = getCurrentSystemBrightness()
            lastAdjustmentTime = System.currentTimeMillis()
            
            isMonitoring = true
            Log.i(TAG, "系统亮度监听器启动成功，当前亮度: ${(lastRecordedBrightness * 100).toInt()}%")
            
        } catch (e: Exception) {
            Log.e(TAG, "启动系统亮度监听失败: ${e.message}")
            isMonitoring = false
        }
    }
    
    /**
     * 停止监听系统亮度变化
     */
    fun stopMonitoring() {
        if (!isMonitoring) {
            return
        }
        
        try {
            brightnessObserver?.let { observer ->
                context.contentResolver.unregisterContentObserver(observer)
            }
            brightnessObserver = null
            
            // 取消所有协程
            monitorScope.cancel()
            
            isMonitoring = false
            Log.i(TAG, "系统亮度监听器已停止")
            
        } catch (e: Exception) {
            Log.e(TAG, "停止系统亮度监听失败: ${e.message}")
        }
    }
    
    /**
     * 处理亮度变化事件
     */
    private suspend fun handleBrightnessChange() {
        try {
            val currentTime = System.currentTimeMillis()
            val newBrightness = getCurrentSystemBrightness()
            
            // 计算亮度变化
            val brightnessChange = kotlin.math.abs(newBrightness - lastRecordedBrightness)
            val timeSinceLastAdjustment = currentTime - lastAdjustmentTime
            val timeSinceAppAdjustment = currentTime - lastAppAdjustmentTime
            
            Log.v(TAG, "亮度变化检测: ${(lastRecordedBrightness*100).toInt()}% -> ${(newBrightness*100).toInt()}% (变化=${(brightnessChange*100).toInt()}%)")
            
            // 第一级过滤：时间间隔过短的调节
            if (timeSinceLastAdjustment < ADJUSTMENT_INTERVAL_THRESHOLD) {
                Log.v(TAG, "调节间隔过短，跳过: ${timeSinceLastAdjustment}ms < ${ADJUSTMENT_INTERVAL_THRESHOLD}ms")
                return
            }
            
            // 第二级过滤：变化幅度过小的调节
            if (brightnessChange < MIN_BRIGHTNESS_CHANGE) {
                Log.v(TAG, "亮度变化过小，跳过: ${(brightnessChange*100).toInt()}% < ${(MIN_BRIGHTNESS_CHANGE*100).toInt()}%")
                filteredAdjustmentCount++
                return
            }
            
            // 第三级过滤：判断是否为应用自动调节
            val isAppAdjustment = timeSinceAppAdjustment < APP_ADJUSTMENT_TIMEOUT
            if (isAppAdjustment) {
                Log.v(TAG, "检测到应用自动调节，跳过学习: 距离上次应用调节${timeSinceAppAdjustment}ms")
                return
            }
            
            // 识别为用户手动调节
            userAdjustmentCount++
            Log.d(TAG, "用户手动调节检测: ${(lastRecordedBrightness*100).toInt()}% -> ${(newBrightness*100).toInt()}%")
            
            // 获取当前光照环境
            val currentLightLevel = lightSensorManager.getCurrentLightLevel()
            
            // 记录到智能学习系统
            recordUserAdjustment(
                originalBrightness = lastRecordedBrightness,
                newBrightness = newBrightness,
                lightLevel = currentLightLevel
            )
            
            // 更新记录值
            lastRecordedBrightness = newBrightness
            lastAdjustmentTime = currentTime
            
        } catch (e: Exception) {
            Log.e(TAG, "处理亮度变化失败: ${e.message}")
        }
    }
    
    /**
     * 记录用户手动调节到智能学习系统
     */
    private fun recordUserAdjustment(
        originalBrightness: Float,
        newBrightness: Float,
        lightLevel: Float
    ) {
        try {
            // 调用智能学习管理器记录调节
            lightnessController.recordManualAdjustment(
                originalBrightness = originalBrightness,
                newBrightness = newBrightness,
                lightLevel = lightLevel
            )
            
            recordedAdjustmentCount++
            
            Log.i(TAG, "用户调节已记录到学习系统: ${(originalBrightness*100).toInt()}% -> ${(newBrightness*100).toInt()}% (光照: ${lightLevel.toInt()} lux)")
            
            // 分析调节模式
            analyzeAdjustmentPattern(originalBrightness, newBrightness, lightLevel)
            
        } catch (e: Exception) {
            Log.e(TAG, "记录用户调节失败: ${e.message}")
        }
    }
    
    /**
     * 分析用户调节模式
     */
    private fun analyzeAdjustmentPattern(
        originalBrightness: Float,
        newBrightness: Float,
        lightLevel: Float
    ) {
        val adjustmentDirection = if (newBrightness > originalBrightness) "提亮" else "调暗"
        val adjustmentMagnitude = kotlin.math.abs(newBrightness - originalBrightness)
        
        val magnitudeDescription = when {
            adjustmentMagnitude > 0.20f -> "大幅"
            adjustmentMagnitude > 0.10f -> "显著" 
            adjustmentMagnitude > 0.05f -> "适度"
            else -> "微调"
        }
        
        val environmentDescription = ScenarioBrightnessManager.getCurrentBestScenario(lightLevel).displayName
        
        Log.d(TAG, "调节模式分析: 在${environmentDescription}环境下${magnitudeDescription}${adjustmentDirection} ${(adjustmentMagnitude*100).toInt()}%")
    }
    
    /**
     * 获取当前系统亮度 (0.0-1.0)
     */
    private fun getCurrentSystemBrightness(): Float {
        return try {
            val brightness = Settings.System.getInt(
                context.contentResolver,
                Settings.System.SCREEN_BRIGHTNESS,
                128
            )
            (brightness / 255.0f).coerceIn(0.0f, 1.0f)
        } catch (e: Exception) {
            Log.w(TAG, "获取系统亮度失败: ${e.message}")
            0.25f
        }
    }
    
    /**
     * 标记应用进行了自动调节 - 用于过滤应用调节
     */
    fun markAppAdjustment() {
        lastAppAdjustmentTime = System.currentTimeMillis()
        Log.v(TAG, "标记应用自动调节时间: $lastAppAdjustmentTime")
    }
    
    /**
     * 获取监听统计信息
     */
    fun getMonitoringStats(): String {
        val totalDetectedAdjustments = userAdjustmentCount + filteredAdjustmentCount
        val filteringRate = if (totalDetectedAdjustments > 0) {
            (filteredAdjustmentCount.toFloat() / totalDetectedAdjustments * 100).toInt()
        } else 0
        
        val recordingRate = if (userAdjustmentCount > 0) {
            (recordedAdjustmentCount.toFloat() / userAdjustmentCount * 100).toInt()
        } else 0
        
        return """
            📊 系统亮度监听统计
            
            • 监听状态：${if (isMonitoring) "运行中" else "已停止"}
            • 检测到调节：$totalDetectedAdjustments 次
            • 用户手动调节：$userAdjustmentCount 次
            • 已记录学习：$recordedAdjustmentCount 次
            • 过滤调节：$filteredAdjustmentCount 次 (${filteringRate}%)
            • 记录率：${recordingRate}%
            • 当前亮度：${(getCurrentSystemBrightness()*100).toInt()}%
        """.trimIndent()
    }
    
    /**
     * 获取使用提示
     */
    fun getUsageTips(): String {
        return """
            💡 系统亮度调节学习使用提示
            
            • 使用系统原生亮度控制：
              - 下拉通知栏的亮度滑块
              - 设置 → 显示 → 亮度
              - 音量键 + 亮度快捷键（部分手机）
              
            • 系统会自动学习您的调节习惯
            • 避免频繁小幅度调节（系统会过滤）
            • 在不同环境下调节效果更佳
            • 与应用内学习功能互补使用
            
            当前已学习 $recordedAdjustmentCount 次您的系统调节习惯
        """.trimIndent()
    }
    
    /**
     * 重置监听统计
     */
    fun resetStats() {
        userAdjustmentCount = 0
        filteredAdjustmentCount = 0
        recordedAdjustmentCount = 0
        Log.i(TAG, "系统亮度监听统计已重置")
    }
    
    /**
     * 检查监听器状态
     */
    fun isMonitoringActive(): Boolean = isMonitoring
    
    /**
     * 获取最后记录的亮度
     */
    fun getLastRecordedBrightness(): Float = lastRecordedBrightness
    
    /**
     * 获取用户调节次数
     */
    fun getUserAdjustmentCount(): Int = userAdjustmentCount
} 