package com.example.myapplication5

import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import kotlinx.coroutines.*

/**
 * 服务稳定性增强器 - 解决后台服务失灵问题
 * 
 * 功能：
 * 1. 增强的传感器状态监控
 * 2. 自动恢复失效的传感器
 * 3. 服务健康度评估
 * 4. 智能重启机制
 * 5. 电池优化绕过
 * 
 * <AUTHOR>
 * @since 2.6 - 解决后台传感器失灵问题
 */
object ServiceStabilityEnhancer {
    
    private const val TAG = "ServiceStabilityEnhancer"
    
    /**
     * 增强服务启动 - 确保传感器正常工作
     */
    fun enhancedStartService(context: Context): Boolean {
        return try {
            Log.d(TAG, "开始增强服务启动")
            
            // 先启动基础服务
            val serviceIntent = Intent(context, EyeCareBackgroundService::class.java).apply {
                putExtra("eye_care_mode", "STANDARD")
                putExtra("auto_adjustment", true)
                putExtra("enhanced_start", true)
                putExtra("start_time", System.currentTimeMillis())
            }
            
            val success = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
                true
            } else {
                context.startService(serviceIntent) != null
            }
            
            if (success) {
                Log.d(TAG, "增强服务启动命令已发送")
                
                // 启动监控协程
                CoroutineScope(Dispatchers.IO).launch {
                    monitorServiceHealth(context)
                }
                
                return true
            } else {
                Log.e(TAG, "增强服务启动失败")
                return false
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "增强服务启动异常: ${e.message}")
            false
        }
    }
    
    /**
     * 监控服务健康状态
     */
    private suspend fun monitorServiceHealth(context: Context) {
        try {
            Log.d(TAG, "开始监控服务健康状态")
            
            var consecutiveFailures = 0
            val maxFailures = 3
            
            while (true) {
                delay(20000) // 每20秒检查一次
                
                try {
                    // 检查服务是否运行
                    val isRunning = ServiceForceStarter.isServiceRunning(context)
                    
                    if (!isRunning) {
                        Log.w(TAG, "检测到服务未运行，尝试恢复")
                        consecutiveFailures++
                        
                        if (consecutiveFailures >= maxFailures) {
                            Log.e(TAG, "服务连续失败${maxFailures}次，执行强制重启")
                            forceRestartService(context)
                            consecutiveFailures = 0
                        } else {
                            // 尝试普通重启
                            val restartSuccess = ServiceForceStarter.restartService(context)
                            if (restartSuccess) {
                                Log.d(TAG, "服务重启成功")
                                consecutiveFailures = 0
                            }
                        }
                    } else {
                        // 服务运行正常，重置失败计数
                        consecutiveFailures = 0
                        Log.v(TAG, "服务健康检查正常")
                    }
                    
                } catch (e: Exception) {
                    Log.e(TAG, "服务健康检查异常: ${e.message}")
                    consecutiveFailures++
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "服务健康监控异常: ${e.message}")
        }
    }
    
    /**
     * 强制重启服务
     */
    private fun forceRestartService(context: Context): Boolean {
        return try {
            Log.d(TAG, "执行强制服务重启")
            
            // 1. 强制停止服务
            ServiceForceStarter.forceStopService(context)
            
            // 2. 等待完全停止
            Thread.sleep(3000)
            
            // 3. 清理可能的僵尸进程
            ServiceForceStarter.cleanupZombieServices(context)
            
            // 4. 重新启动服务
            val success = enhancedStartService(context)
            
            if (success) {
                Log.d(TAG, "强制服务重启成功")
            } else {
                Log.e(TAG, "强制服务重启失败")
            }
            
            success
            
        } catch (e: Exception) {
            Log.e(TAG, "强制服务重启异常: ${e.message}")
            false
        }
    }
    
    /**
     * 检查传感器状态并尝试恢复
     */
    fun checkAndRestoreSensor(context: Context): Boolean {
        return try {
            Log.d(TAG, "检查并恢复传感器状态")
            
            // 创建临时的传感器管理器来检查状态
            val tempSensorManager = LightSensorManager(context) { }
            
            val sensorAvailable = tempSensorManager.isLightSensorAvailable()
            val sensorListening = tempSensorManager.isListening()
            
            if (!sensorAvailable) {
                Log.w(TAG, "传感器不可用")
                return false
            }
            
            if (!sensorListening) {
                Log.w(TAG, "传感器未在监听，尝试恢复")
                return tempSensorManager.forceRestartListening()
            }
            
            // 检查数据更新
            val lastUpdateTime = tempSensorManager.getLastUpdateTime()
            val currentTime = System.currentTimeMillis()
            val timeSinceLastUpdate = currentTime - lastUpdateTime
            
            if (timeSinceLastUpdate > 30000) {
                Log.w(TAG, "传感器数据更新超时，尝试恢复")
                return tempSensorManager.forceRestartListening()
            }
            
            Log.d(TAG, "传感器状态正常")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "检查传感器状态异常: ${e.message}")
            false
        }
    }
    
    /**
     * 获取服务稳定性报告 - 包含户外快速响应状态
     */
    fun getStabilityReport(context: Context): String {
        return try {
            val isRunning = ServiceForceStarter.isServiceRunning(context)
            val sensorStatus = checkAndRestoreSensor(context)
            val hasPermission = ServiceForceStarter.getServiceDetails(context)
            val outdoorStatus = OutdoorBrightnessEnhancer.getOutdoorModeStatus()
            val ultraBrightStatus = OutdoorUltraBrightnessEnhancer.getUltraBrightStatus()
            val indoorStabilizerStatus = IndoorBrightnessStabilizer.getStabilizerStatus()

            """
                服务稳定性报告:
                ====================
                服务运行状态: ${if (isRunning) "正常" else "异常"}
                传感器状态: ${if (sensorStatus) "正常" else "异常"}
                权限状态: $hasPermission
                增强监控: 已启用
                自动恢复: 已启用

                $outdoorStatus

                $ultraBrightStatus

                $indoorStabilizerStatus

                如果发现问题，系统会自动尝试恢复
            """.trimIndent()

        } catch (e: Exception) {
            "获取稳定性报告失败: ${e.message}"
        }
    }
    
    /**
     * 执行紧急恢复
     */
    fun emergencyRecovery(context: Context): Boolean {
        return try {
            Log.d(TAG, "执行紧急恢复")
            
            // 1. 停止所有相关服务
            ServiceForceStarter.forceStopService(context)
            
            // 2. 清理资源
            Thread.sleep(2000)
            
            // 3. 重新初始化传感器
            checkAndRestoreSensor(context)
            
            // 4. 重新启动服务
            val success = enhancedStartService(context)
            
            if (success) {
                Log.d(TAG, "紧急恢复成功")
            } else {
                Log.e(TAG, "紧急恢复失败")
            }
            
            success
            
        } catch (e: Exception) {
            Log.e(TAG, "紧急恢复异常: ${e.message}")
            false
        }
    }
} 