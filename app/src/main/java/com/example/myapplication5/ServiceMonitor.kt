package com.example.myapplication5

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.util.Log
import android.widget.Toast
import androidx.core.content.ContextCompat
import kotlinx.coroutines.*

/**
 * 服务监控器 - 确保后台服务持续运行，防止被用户误清理
 * 
 * 功能：
 * 1. 快速检查服务状态（15秒间隔）
 * 2. 智能检测服务被清理情况
 * 3. 自动重启失败的服务
 * 4. 监控系统事件（如重启、应用更新）
 * 5. 提供服务健康报告和用户通知
 * 
 * <AUTHOR> v2.0 - 防清理增强版
 * @since 2.6
 */
object ServiceMonitor {
    
    private const val TAG = "ServiceMonitor"
    private const val MONITOR_ACTION = "com.example.myapplication5.MONITOR_SERVICE"
    private const val FAST_MONITOR_INTERVAL = 15000L // 15秒快速检查，防止被清理
    private const val CLEANUP_DETECTION_THRESHOLD = 3 // 连续失败3次认为被清理
    
    private var isMonitoring = false
    private var alarmManager: AlarmManager? = null
    private var monitorPendingIntent: PendingIntent? = null
    private var monitorReceiver: BroadcastReceiver? = null
    private var consecutiveFailures = 0 // 连续失败次数
    
    /**
     * 开始监控服务
     */
    fun startMonitoring(context: Context) {
        if (isMonitoring) {
            Log.d(TAG, "监控已在运行中")
            return
        }
        
        try {
            Log.d(TAG, "开始监控后台服务")
            
            // 注册广播接收器
            registerMonitorReceiver(context)
            
            // 设置定时器
            setupAlarmManager(context)
            
            isMonitoring = true
            Log.d(TAG, "服务监控已启动")
            
        } catch (e: Exception) {
            Log.e(TAG, "启动监控失败: ${e.message}")
        }
    }
    
    /**
     * 停止监控服务
     */
    fun stopMonitoring(context: Context) {
        if (!isMonitoring) {
            return
        }
        
        try {
            Log.d(TAG, "停止监控后台服务")
            
            // 取消定时器
            cancelAlarmManager()
            
            // 注销广播接收器
            unregisterMonitorReceiver(context)
            
            isMonitoring = false
            Log.d(TAG, "服务监控已停止")
            
        } catch (e: Exception) {
            Log.e(TAG, "停止监控失败: ${e.message}")
        }
    }
    
    /**
     * 注册监控广播接收器
     */
    private fun registerMonitorReceiver(context: Context) {
        monitorReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                if (intent?.action == MONITOR_ACTION) {
                    checkAndRestoreService(context)
                }
            }
        }
        
        val filter = IntentFilter(MONITOR_ACTION)
        ContextCompat.registerReceiver(
            context, 
            monitorReceiver, 
            filter, 
            ContextCompat.RECEIVER_NOT_EXPORTED
        )
    }
    
    /**
     * 注销监控广播接收器
     */
    private fun unregisterMonitorReceiver(context: Context) {
        monitorReceiver?.let {
            try {
                context.unregisterReceiver(it)
                monitorReceiver = null
            } catch (e: Exception) {
                Log.w(TAG, "注销广播接收器失败: ${e.message}")
            }
        }
    }
    
    /**
     * 设置定时器
     */
    private fun setupAlarmManager(context: Context) {
        alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        
        val intent = Intent(MONITOR_ACTION)
        monitorPendingIntent = PendingIntent.getBroadcast(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        // 设置重复定时器
        monitorPendingIntent?.let { pendingIntent ->
            alarmManager?.setRepeating(
                AlarmManager.RTC_WAKEUP,
                System.currentTimeMillis() + FAST_MONITOR_INTERVAL,
                FAST_MONITOR_INTERVAL,
                pendingIntent
            )
        }
    }
    
    /**
     * 取消定时器
     */
    private fun cancelAlarmManager() {
        monitorPendingIntent?.let { pendingIntent ->
            alarmManager?.cancel(pendingIntent)
            monitorPendingIntent = null
        }
    }
    
    /**
     * 检查并恢复服务 - 防清理增强版
     */
    private fun checkAndRestoreService(context: Context?) {
        context?.let {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    Log.d(TAG, "执行快速服务健康检查（防清理模式）")
                    
                    // 检查用户设置
                    val backgroundServiceEnabled = EyeCareSettingsManager.isBackgroundServiceEnabled(it)
                    val autoStartEnabled = EyeCareSettingsManager.isAutoStartEnabled(it)
                    
                    if (!backgroundServiceEnabled) {
                        Log.d(TAG, "用户未启用后台服务，跳过检查")
                        consecutiveFailures = 0
                        return@launch
                    }
                    
                    // 检查服务状态
                    val isRunning = ServiceForceStarter.isServiceRunning(it)
                    
                    if (!isRunning) {
                        consecutiveFailures++
                        Log.w(TAG, "检测到服务未运行，连续失败次数: $consecutiveFailures")
                        
                        // 判断是否为用户清理导致
                        val wasCleanedUp = consecutiveFailures >= CLEANUP_DETECTION_THRESHOLD
                        
                        if (wasCleanedUp) {
                            Log.w(TAG, "疑似用户清理后台导致服务停止，开始智能恢复")
                            
                            // 显示用户通知
                            withContext(Dispatchers.Main) {
                                showServiceRestoredNotification(it)
                            }
                        }
                        
                        // 尝试重启服务
                        val success = ServiceForceStarter.forceStartService(it)
                        
                        if (success) {
                            Log.d(TAG, "服务恢复成功")
                            consecutiveFailures = 0 // 重置失败计数
                            
                            if (wasCleanedUp) {
                                // 如果是从清理中恢复，显示用户教育提醒
                                withContext(Dispatchers.Main) {
                                    showCleanupEducationToast(it)
                                }
                            }
                        } else {
                            Log.e(TAG, "服务恢复失败，连续失败次数: $consecutiveFailures")
                        }
                        
                    } else {
                        // 服务运行正常，重置失败计数
                        if (consecutiveFailures > 0) {
                            Log.d(TAG, "服务恢复正常，重置失败计数")
                            consecutiveFailures = 0
                        }
                    }
                    
                } catch (e: Exception) {
                    Log.e(TAG, "服务检查异常: ${e.message}")
                    consecutiveFailures++
                }
            }
        }
    }
    
    /**
     * 显示服务恢复通知
     */
    private fun showServiceRestoredNotification(context: Context) {
        try {
            Toast.makeText(
                context,
                "🛡️ 护眼保护已自动恢复运行",
                Toast.LENGTH_LONG
            ).show()
        } catch (e: Exception) {
            Log.w(TAG, "显示恢复通知失败: ${e.message}")
        }
    }
    
    /**
     * 显示防清理教育提醒
     */
    private fun showCleanupEducationToast(context: Context) {
        try {
            Toast.makeText(
                context,
                "⚠️ 检测到护眼应用被清理，建议加入后台白名单",
                Toast.LENGTH_LONG
            ).show()
        } catch (e: Exception) {
            Log.w(TAG, "显示教育提醒失败: ${e.message}")
        }
    }
    
    /**
     * 立即执行一次检查
     */
    fun performHealthCheck(context: Context): String {
        return try {
            val backgroundServiceEnabled = EyeCareSettingsManager.isBackgroundServiceEnabled(context)
            val isRunning = ServiceForceStarter.isServiceRunning(context)
            val hasPermission = ServiceForceStarter.getServiceDetails(context)
            
            val status = when {
                !backgroundServiceEnabled -> "用户未启用后台服务"
                !isRunning -> "服务未运行"
                else -> "服务运行正常"
            }
            
            """
                监控状态: ${if (isMonitoring) "运行中" else "已停止"}
                后台服务: $status
                权限状态: $hasPermission
            """.trimIndent()
            
        } catch (e: Exception) {
            "健康检查失败: ${e.message}"
        }
    }
    
    /**
     * 获取监控统计信息
     */
    fun getMonitoringStats(): String {
        return """
            监控状态: ${if (isMonitoring) "运行中" else "已停止"}
            检查间隔: ${FAST_MONITOR_INTERVAL / 1000}秒
            监控动作: $MONITOR_ACTION
        """.trimIndent()
    }
    
    /**
     * 强制重启监控
     */
    fun forceRestartMonitoring(context: Context): Boolean {
        return try {
            Log.d(TAG, "强制重启监控")
            
            stopMonitoring(context)
            Thread.sleep(1000) // 等待1秒
            startMonitoring(context)
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "强制重启监控失败: ${e.message}")
            false
        }
    }
} 