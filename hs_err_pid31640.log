#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1048576 bytes. Error detail: AllocateHeap
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (allocation.cpp:44), pid=31640, tid=20164
#
# JRE version:  (21.0.7+6) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-0f266c1a9e1c8fe2d49a96326fcc4bf4-sock

Host: 12th Gen Intel(R) Core(TM) i9-12900HX, 24 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4484)
Time: Mon Jul 21 22:28:06 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4484) elapsed time: 0.075957 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000016ef122de00):  JavaThread "Unknown thread" [_thread_in_vm, id=20164, stack(0x0000005e1e500000,0x0000005e1e600000) (1024K)]

Stack: [0x0000005e1e500000,0x0000005e1e600000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xbfba7]
V  [jvm.dll+0x702092]
V  [jvm.dll+0x702dcc]
V  [jvm.dll+0x6dcc63]
V  [jvm.dll+0x871dbc]
V  [jvm.dll+0x3bc47c]
V  [jvm.dll+0x85a848]
V  [jvm.dll+0x45080e]
V  [jvm.dll+0x452451]
C  [jli.dll+0x5278]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000016ef12592e0, length=1, elements={
0x0000016ef122de00
}

Java Threads: ( => current thread )
=>0x0000016ef122de00 JavaThread "Unknown thread"             [_thread_in_vm, id=20164, stack(0x0000005e1e500000,0x0000005e1e600000) (1024K)]
Total: 1

Other Threads:
  0x0000016ef922ca00 WatcherThread "VM Periodic Task Thread"        [id=12748, stack(0x0000005e1e700000,0x0000005e1e800000) (1024K)]
  0x0000016ef124b6b0 WorkerThread "GC Thread#0"                     [id=14420, stack(0x0000005e1e600000,0x0000005e1e700000) (1024K)]
Total: 2

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000016e8f000000-0x0000016e8fba0000-0x0000016e8fba0000), size 12189696, SharedBaseAddress: 0x0000016e8f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000016e90000000-0x0000016ed0000000, reserved size: 1073741824
Narrow klass base: 0x0000016e8f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 24 total, 24 available
 Memory: 16121M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 18

Heap:
 PSYoungGen      total 29696K, used 512K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 2% used [0x00000000d5580000,0x00000000d5600020,0x00000000d6e80000)
  from space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 0K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080000000,0x0000000084300000)
 Metaspace       used 0K, committed 0K, reserved 1048576K
  class space    used 0K, committed 0K, reserved 1048576K

Card table byte_map: [0x0000016ef4660000,0x0000016ef4a70000] _byte_map_base: 0x0000016ef4260000

Marking Bits: (ParMarkBitMap*) 0x00007fffbf2531f0
 Begin Bits: [0x0000016ef4d20000, 0x0000016ef6d20000)
 End Bits:   [0x0000016ef6d20000, 0x0000016ef8d20000)

Polling page: 0x0000016ef12f0000

Metaspace:

Usage:
  Non-class:      0 bytes used.
      Class:      0 bytes used.
       Both:      0 bytes used.

Virtual space:
  Non-class space:        0 bytes reserved,       0 bytes (  ?%) committed,  0 nodes.
      Class space:        1.00 GB reserved,       0 bytes (  0%) committed,  1 nodes.
             Both:        1.00 GB reserved,       0 bytes (  0%) committed. 

Chunk freelists:
   Non-Class:  0 bytes
       Class:  16.00 MB
        Both:  16.00 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 17179869184.00 GB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 0.
num_arena_deaths: 0.
num_vsnodes_births: 1.
num_vsnodes_deaths: 0.
num_space_committed: 0.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 1.
num_chunk_merges: 0.
num_chunk_splits: 1.
num_chunks_enlarged: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=0Kb max_used=0Kb free=119168Kb
 bounds [0x0000016e87ba0000, 0x0000016e87e10000, 0x0000016e8f000000]
CodeHeap 'profiled nmethods': size=119104Kb used=0Kb max_used=0Kb free=119104Kb
 bounds [0x0000016e80000000, 0x0000016e80270000, 0x0000016e87450000]
CodeHeap 'non-nmethods': size=7488Kb used=194Kb max_used=342Kb free=7293Kb
 bounds [0x0000016e87450000, 0x0000016e876c0000, 0x0000016e87ba0000]
 total_blobs=70 nmethods=0 adapters=48
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (0 events):
No events

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.006 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (1 events):
Event: 0.020 Thread 0x0000016ef122de00 Thread added: 0x0000016ef122de00


Dynamic libraries:
0x00007ff7daa60000 - 0x00007ff7daa6e000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff854d20000 - 0x00007ff854f88000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff853e10000 - 0x00007ff853ed9000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff8526d0000 - 0x00007ff852abd000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff8522a0000 - 0x00007ff8523eb000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff82fb20000 - 0x00007ff82fb38000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff8331a0000 - 0x00007ff8331be000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff853430000 - 0x00007ff8535fc000 	C:\WINDOWS\System32\USER32.dll
0x00007ff852040000 - 0x00007ff852067000 	C:\WINDOWS\System32\win32u.dll
0x00007ff831500000 - 0x00007ff83179a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3\COMCTL32.dll
0x00007ff854440000 - 0x00007ff85446b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff854290000 - 0x00007ff854339000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff851f00000 - 0x00007ff852037000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff852070000 - 0x00007ff852113000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff854340000 - 0x00007ff85436f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff84c870000 - 0x00007ff84c87c000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff823400000 - 0x00007ff82348d000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007fffbe5a0000 - 0x00007fffbf330000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff8541d0000 - 0x00007ff854284000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff854380000 - 0x00007ff854426000 	C:\WINDOWS\System32\sechost.dll
0x00007ff854470000 - 0x00007ff854588000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff853140000 - 0x00007ff8531b4000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff850a90000 - 0x00007ff850aee000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff84b030000 - 0x00007ff84b03b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff842b20000 - 0x00007ff842b55000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff850a70000 - 0x00007ff850a84000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff850d30000 - 0x00007ff850d4b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff84c860000 - 0x00007ff84c86a000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff84f070000 - 0x00007ff84f2b1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff853910000 - 0x00007ff853c96000 	C:\WINDOWS\System32\combase.dll
0x00007ff8537b0000 - 0x00007ff853890000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff823260000 - 0x00007ff8232a3000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff8523f0000 - 0x00007ff852489000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff84c810000 - 0x00007ff84c81f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff823980000 - 0x00007ff82399f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3;c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-0f266c1a9e1c8fe2d49a96326fcc4bf4-sock
java_class_path (initial): c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-23
PATH=D:\Ӱ��;C;\Program Files\Java\jdk-23\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\ProgramData\chocolatey\bin;C:\Program Files\nodejs\;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;D:\΢�ſ�;�߹���\΢��web�����߹���\dll;;C:\Program Files\Git\cmd;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;d:\Trae��̹���\Trae CN\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\VSCOD\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Java\jdk-23\bin;C:\Users\<USER>\.android\platform-tools-latest-windows\platform-tools;
USERNAME=91668
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4484)
OS uptime: 0 days 14:32 hours

CPU: total 24 (initial active 24) (12 cores per cpu, 2 threads per core) family 6 model 151 stepping 2 microcode 0x2c, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, serialize, rdtscp, rdpid, fsrm, f16c, pku, cet_ibt, cet_ss
Processor Information for processor 0
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 1
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 2
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 3
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 4
  Max Mhz: 2300, Current Mhz: 1506, Mhz Limit: 2300
Processor Information for processor 5
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 6
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 7
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 8
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 9
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 10
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 11
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 12
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 13
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 14
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 15
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 16
  Max Mhz: 2300, Current Mhz: 1488, Mhz Limit: 2300
Processor Information for processor 17
  Max Mhz: 2300, Current Mhz: 1488, Mhz Limit: 2300
Processor Information for processor 18
  Max Mhz: 2300, Current Mhz: 1488, Mhz Limit: 2300
Processor Information for processor 19
  Max Mhz: 2300, Current Mhz: 1488, Mhz Limit: 2300
Processor Information for processor 20
  Max Mhz: 2300, Current Mhz: 1488, Mhz Limit: 2300
Processor Information for processor 21
  Max Mhz: 2300, Current Mhz: 1488, Mhz Limit: 2300
Processor Information for processor 22
  Max Mhz: 2300, Current Mhz: 1488, Mhz Limit: 2300
Processor Information for processor 23
  Max Mhz: 2300, Current Mhz: 1488, Mhz Limit: 2300

Memory: 4k page, system-wide physical 16121M (2266M free)
TotalPageFile size 28921M (AvailPageFile size 60M)
current process WorkingSet (physical memory assigned to process): 26M, peak: 26M
current process commit charge ("private bytes"): 210M, peak: 211M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
