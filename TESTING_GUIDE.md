# 护眼应用测试指南

## 🧪 测试目标

验证护眼应用在退出界面后仍能正常进行自动亮度调节。

## 📋 测试前准备

### 1. 权限设置
- [ ] 授予"修改系统设置"权限
- [ ] 授予通知权限
- [ ] 关闭电池优化（推荐）

### 2. 系统设置
- [ ] 关闭系统自动亮度
- [ ] 确保应用可以在后台运行

### 3. 应用设置
- [ ] 开启"后台护眼保护"
- [ ] 开启"自动护眼调节"
- [ ] 选择护眼模式（推荐"标准"）

## 🔍 基础功能测试

### 测试1：前台亮度调节
**步骤：**
1. 打开护眼应用
2. 用手遮挡光传感器
3. 观察亮度是否自动降低
4. 移开遮挡，观察亮度是否恢复

**预期结果：** ✅ 亮度能够根据光照变化自动调节

### 测试2：传感器响应
**步骤：**
1. 在应用内查看当前光照值
2. 用手电筒照射传感器
3. 观察光照值是否变化
4. 观察亮度是否相应调整

**预期结果：** ✅ 传感器响应正常，亮度调节及时

## 🚀 后台服务测试

### 测试3：服务启动测试
**步骤：**
1. 开启"后台护眼保护"
2. 点击"强制启动"按钮
3. 检查通知栏是否显示"护眼保护正在运行"
4. 查看通知内容是否包含亮度和光照信息

**预期结果：** ✅ 服务成功启动，通知正常显示

### 测试4：退出后持续工作
**步骤：**
1. 确保后台服务正在运行
2. 按Home键退出应用
3. 等待30秒
4. 用手遮挡光传感器
5. 观察亮度是否仍然自动调节

**预期结果：** ✅ 退出应用后亮度调节仍然工作

### 测试5：长时间运行测试
**步骤：**
1. 启动后台服务
2. 退出应用
3. 等待5-10分钟
4. 检查通知是否仍然存在
5. 测试亮度调节功能

**预期结果：** ✅ 服务能够长时间稳定运行

## 🔧 诊断工具测试

### 测试6：运行诊断
**步骤：**
1. 打开应用，滚动到底部
2. 点击"运行诊断"按钮
3. 查看诊断信息
4. 检查日志输出

**预期结果：** ✅ 诊断信息完整，包含服务状态

### 测试7：健康检查
**步骤：**
1. 点击"健康检查"按钮
2. 查看健康报告
3. 确认监控状态为"运行中"

**预期结果：** ✅ 健康检查通过，监控正常运行

### 测试8：服务重启测试
**步骤：**
1. 点击"重启服务"按钮
2. 等待服务重启完成
3. 测试亮度调节功能
4. 检查通知状态

**预期结果：** ✅ 服务重启成功，功能正常

## 🛠️ 故障恢复测试

### 测试9：强制启动测试
**步骤：**
1. 手动停止后台服务
2. 点击"强制启动"按钮
3. 等待启动完成
4. 验证功能正常

**预期结果：** ✅ 强制启动成功，服务正常运行

### 测试10：清理服务测试
**步骤：**
1. 点击"清理服务"按钮
2. 等待清理完成
3. 重新启动服务
4. 测试功能

**预期结果：** ✅ 清理成功，服务重新启动正常

### 测试11：重启监控测试
**步骤：**
1. 点击"重启监控"按钮
2. 等待监控重启
3. 检查监控状态
4. 验证自动恢复功能

**预期结果：** ✅ 监控重启成功，自动恢复功能正常

## 📱 不同场景测试

### 测试12：锁屏测试
**步骤：**
1. 启动后台服务
2. 锁屏
3. 解锁后检查服务状态
4. 测试亮度调节

**预期结果：** ✅ 锁屏后服务仍然运行

### 测试13：多应用切换测试
**步骤：**
1. 启动后台服务
2. 打开其他应用
3. 在不同应用间切换
4. 检查护眼服务状态

**预期结果：** ✅ 应用切换后服务仍然运行

### 测试14：系统重启测试
**步骤：**
1. 开启"开机自启"
2. 重启设备
3. 检查服务是否自动启动
4. 测试功能

**预期结果：** ✅ 系统重启后服务自动启动

## 🚨 异常情况测试

### 测试15：权限丢失测试
**步骤：**
1. 启动服务
2. 手动撤销"修改系统设置"权限
3. 观察服务行为
4. 重新授予权限
5. 检查服务恢复

**预期结果：** ✅ 权限丢失时服务能够检测并报告

### 测试16：传感器故障测试
**步骤：**
1. 在设备不支持光传感器的环境下测试
2. 观察应用行为
3. 检查错误处理

**预期结果：** ✅ 传感器不可用时应用能够优雅处理

### 测试17：内存压力测试
**步骤：**
1. 启动多个大型应用
2. 观察护眼服务是否被杀死
3. 检查自动恢复功能

**预期结果：** ✅ 内存压力下服务能够自动恢复

## 📊 性能测试

### 测试18：电池消耗测试
**步骤：**
1. 记录当前电池电量
2. 运行护眼服务1小时
3. 记录电池消耗
4. 评估电池影响

**预期结果：** ✅ 电池消耗在合理范围内

### 测试19：CPU使用率测试
**步骤：**
1. 监控CPU使用率
2. 运行护眼服务
3. 观察CPU占用情况
4. 评估性能影响

**预期结果：** ✅ CPU使用率在合理范围内

## 🎯 测试结果评估

### 成功标准：
- [ ] 前台亮度调节正常
- [ ] 后台服务能够启动
- [ ] 退出应用后功能持续工作
- [ ] 诊断工具正常工作
- [ ] 故障恢复机制有效
- [ ] 不同场景下服务稳定
- [ ] 性能影响在可接受范围

### 失败情况：
- ❌ 服务无法启动
- ❌ 退出后功能停止
- ❌ 诊断工具无法使用
- ❌ 故障恢复失败
- ❌ 性能影响过大

## 🔄 持续监控

### 日常使用监控：
1. **定期检查**：每周检查一次服务状态
2. **功能验证**：定期测试亮度调节功能
3. **日志查看**：遇到问题时查看应用日志
4. **设置备份**：定期备份应用设置

### 问题报告：
如果发现问题，请提供以下信息：
- 设备型号和Android版本
- 问题发生的具体步骤
- 诊断工具的输出结果
- 应用日志信息

---

**注意**：测试过程中如果遇到问题，请参考 `TROUBLESHOOTING.md` 文件中的解决方案。 