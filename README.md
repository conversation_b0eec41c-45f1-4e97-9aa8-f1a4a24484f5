# 护眼光感调节应用

专为干眼症患者设计的Android护眼应用，提供智能亮度调节和护眼模式。

## 🔧 权限问题说明

### 问题描述
从Android 6.0 (API 23)开始，`WRITE_SETTINGS`权限被归类为**签名级别权限**，这意味着：

1. **只有系统应用或使用系统签名的应用**才能获得此权限
2. **普通开发者应用无法直接获取**，即使声明了权限也无法通过系统校验
3. **调试模式下可能被临时授予**，但正式安装后会被系统拒绝

### 解决方案

#### 方案1：开发测试模式（推荐用于开发）
1. 进入系统设置 → 开发者选项
2. 开启"调试应用"模式
3. 重新安装应用
4. 应用将获得临时权限进行测试

#### 方案2：用户手动授权（推荐用于正式使用）
1. 进入系统设置 → 应用管理
2. 找到"护眼光感调节"应用
3. 进入权限设置
4. 手动开启"修改系统设置"权限

#### 方案3：系统应用安装（高级用户）
1. 将应用安装到`/system/app`目录
2. 使用系统签名证书签名
3. 重启设备
4. 应用将获得系统级权限

### 安装说明

**注意**: 应用现在使用标准权限声明，可以直接安装。如果需要系统级权限，请参考方案3。

### 权限诊断

应用内置了详细的权限诊断工具，可以：
- 检查当前权限状态
- 分析应用类型（系统应用/普通应用）
- 检测调试模式状态
- 提供针对性的解决方案

### 技术细节

#### 权限声明
```xml
<uses-permission android:name="android.permission.WRITE_SETTINGS" 
                 android:protectionLevel="signature" />
<uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" 
                 android:protectionLevel="signature" />
```

#### 权限检查
```kotlin
// 检查是否有权限
val hasPermission = Settings.System.canWrite(context)

// 检查应用类型
val isSystemApp = (packageInfo.applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0
val isDebugMode = (packageInfo.applicationInfo.flags and ApplicationInfo.FLAG_DEBUGGABLE) != 0
```

## 功能特性

- 🌙 三种护眼模式：标准、夜间、超敏感
- 🎨 护眼色彩主题：绿色舒缓、橙色温暖
- 🔄 自动亮度调节：根据环境光照智能调节
- 🛡️ 防闪频优化：户外使用智能防闪频机制
- 🌟 **干眼症专用优化**：极低亮度(0.1-0.3%)，深夜智能识别
- 📱 后台运行：应用关闭后继续保护
- 🚀 开机自启：开机后自动启动护眼保护
- 🔧 系统冲突检测：检测并解决系统自动亮度冲突
- 🌍 环境自适应：智能识别室内、户外、夜间环境

## 使用说明

1. 首次使用需要授予系统设置权限
2. 选择适合的护眼模式和色彩主题
3. 开启自动护眼调节功能
4. 可选择开启后台运行和开机自启

### 户外使用优化

应用内置了专门的户外使用优化功能：

- **智能环境检测**：自动识别室内、户外、夜间环境
- **防闪频机制**：户外环境下减少亮度频繁跳动
- **动态调节频率**：根据环境自动调整传感器更新频率
- **数据平滑处理**：减少传感器噪声，提供更稳定的调节

户外使用时，系统会自动：
- 延长传感器更新间隔（3秒/次）
- 提高变化阈值（25 lux）
- 使用更平滑的调节算法
- 过滤云层、阴影等微小变化

### 干眼症患者专用优化

专为干眼症患者在夜间和极暗环境下设计：

- **极低亮度支持**：最低可达0.1%亮度，完美适应完全黑暗环境
- **深夜智能识别**：23:00-05:00时段自动使用最低亮度
- **三档超低亮度**：
  - 深夜极低亮度：0.1% (深夜专用)
  - 超极低亮度：0.2% (完全黑暗)
  - 极低亮度：0.3% (极暗环境)
- **护眼模式优化**：
  - 夜间模式：最大12-20%亮度（根据环境自适应）
  - 超敏感模式：最大35-55%亮度（确保户外可见）
  - 标准模式：0.2-95%亮度范围（极暗到强光全覆盖）
- **智能护眼建议**：基于时间、环境、亮度的专业建议

## 系统要求

- Android 6.0 (API 23) 或更高版本
- 支持光传感器的设备
- 需要系统设置权限（见权限说明）

## 开发说明

### 权限问题排查
1. 运行"权限测试"功能
2. 查看详细诊断信息
3. 根据建议进行相应设置

### 调试模式
开发测试时建议开启调试模式，可以临时获得系统权限进行功能测试。

### 正式发布
正式发布时需要用户手动授权权限，或使用系统签名证书。 