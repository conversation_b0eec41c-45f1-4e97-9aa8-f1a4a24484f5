#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1576976 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=28004, tid=6280
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-1c3b119d2dcd847b1270c78e824c1bcf-sock

Host: 12th Gen Intel(R) Core(TM) i9-12900HX, 24 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4484)
Time: Mon Jul 21 22:27:55 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4484) elapsed time: 5.055717 seconds (0d 0h 0m 5s)

---------------  T H R E A D  ---------------

Current thread (0x0000025a564a59f0):  JavaThread "C2 CompilerThread6" daemon [_thread_in_native, id=6280, stack(0x000000fa4b800000,0x000000fa4b900000) (1024K)]


Current CompileTask:
C2:5055 7560       4       org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer::extractReferenceFromConstantPool (358 bytes)

Stack: [0x000000fa4b800000,0x000000fa4b900000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0x3b692c]
V  [jvm.dll+0x1dfe33]
V  [jvm.dll+0x247c42]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000025a56564300, length=65, elements={
0x0000025a7371e940, 0x0000025a737cd350, 0x0000025a737ce260, 0x0000025a7cd89f90,
0x0000025a7cdac750, 0x0000025a7cdad3a0, 0x0000025a737d1fb0, 0x0000025a737d5690,
0x0000025a737db710, 0x0000025a7ce7b460, 0x0000025a7cf42fe0, 0x0000025a7cf493d0,
0x0000025a7cf5fbe0, 0x0000025a7cf60e30, 0x0000025a7f6b7dc0, 0x0000025a7f8a03a0,
0x0000025a54357b90, 0x0000025a5426b010, 0x0000025a546465c0, 0x0000025a54767020,
0x0000025a5433f900, 0x0000025a54c19330, 0x0000025a54d99ce0, 0x0000025a54c11550,
0x0000025a54c10ec0, 0x0000025a54c12270, 0x0000025a54c11be0, 0x0000025a54c12900,
0x0000025a54c13620, 0x0000025a54c14340, 0x0000025a567a3540, 0x0000025a567a2eb0,
0x0000025a567a4260, 0x0000025a567a69c0, 0x0000025a567a7050, 0x0000025a567a4f80,
0x0000025a567a5610, 0x0000025a567a5ca0, 0x0000025a567a3bd0, 0x0000025a567a8a90,
0x0000025a567a76e0, 0x0000025a567a97b0, 0x0000025a567a7d70, 0x0000025a567a6330,
0x0000025a567a8400, 0x0000025a567a9120, 0x0000025a567a9e40, 0x0000025a567aa4d0,
0x0000025a58c4bf10, 0x0000025a58c4ab60, 0x0000025a58c4c5a0, 0x0000025a58c49e40,
0x0000025a58c4cc30, 0x0000025a58c4b1f0, 0x0000025a58c497b0, 0x0000025a58c4d2c0,
0x0000025a58c4d950, 0x0000025a58c4a4d0, 0x0000025a564a4c50, 0x0000025a564a59f0,
0x0000025a564a60c0, 0x0000025a58c4dfe0, 0x0000025a58c4e670, 0x0000025a58c4fa20,
0x0000025a58c500b0
}

Java Threads: ( => current thread )
  0x0000025a7371e940 JavaThread "main"                              [_thread_blocked, id=25072, stack(0x000000fa46600000,0x000000fa46700000) (1024K)]
  0x0000025a737cd350 JavaThread "Reference Handler"          daemon [_thread_blocked, id=25964, stack(0x000000fa46a00000,0x000000fa46b00000) (1024K)]
  0x0000025a737ce260 JavaThread "Finalizer"                  daemon [_thread_blocked, id=22716, stack(0x000000fa46b00000,0x000000fa46c00000) (1024K)]
  0x0000025a7cd89f90 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=25356, stack(0x000000fa46c00000,0x000000fa46d00000) (1024K)]
  0x0000025a7cdac750 JavaThread "Attach Listener"            daemon [_thread_blocked, id=23044, stack(0x000000fa46d00000,0x000000fa46e00000) (1024K)]
  0x0000025a7cdad3a0 JavaThread "Service Thread"             daemon [_thread_blocked, id=31620, stack(0x000000fa46e00000,0x000000fa46f00000) (1024K)]
  0x0000025a737d1fb0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=10528, stack(0x000000fa46f00000,0x000000fa47000000) (1024K)]
  0x0000025a737d5690 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=24424, stack(0x000000fa47000000,0x000000fa47100000) (1024K)]
  0x0000025a737db710 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=1676, stack(0x000000fa47100000,0x000000fa47200000) (1024K)]
  0x0000025a7ce7b460 JavaThread "C1 CompilerThread1"         daemon [_thread_blocked, id=7772, stack(0x000000fa47200000,0x000000fa47300000) (1024K)]
  0x0000025a7cf42fe0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=24708, stack(0x000000fa47300000,0x000000fa47400000) (1024K)]
  0x0000025a7cf493d0 JavaThread "C1 CompilerThread2"         daemon [_thread_blocked, id=28660, stack(0x000000fa47400000,0x000000fa47500000) (1024K)]
  0x0000025a7cf5fbe0 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=32280, stack(0x000000fa47500000,0x000000fa47600000) (1024K)]
  0x0000025a7cf60e30 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=8772, stack(0x000000fa47600000,0x000000fa47700000) (1024K)]
  0x0000025a7f6b7dc0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=32404, stack(0x000000fa47800000,0x000000fa47900000) (1024K)]
  0x0000025a7f8a03a0 JavaThread "C2 CompilerThread3"         daemon [_thread_blocked, id=11788, stack(0x000000fa47f00000,0x000000fa48000000) (1024K)]
  0x0000025a54357b90 JavaThread "C2 CompilerThread4"         daemon [_thread_in_native, id=21428, stack(0x000000fa48000000,0x000000fa48100000) (1024K)]
  0x0000025a5426b010 JavaThread "Active Thread: Equinox Container: 51269fcc-3a1b-43e2-9105-c0488998ea40"        [_thread_blocked, id=27360, stack(0x000000fa48100000,0x000000fa48200000) (1024K)]
  0x0000025a546465c0 JavaThread "Refresh Thread: Equinox Container: 51269fcc-3a1b-43e2-9105-c0488998ea40" daemon [_thread_blocked, id=13260, stack(0x000000fa48300000,0x000000fa48400000) (1024K)]
  0x0000025a54767020 JavaThread "Framework Event Dispatcher: Equinox Container: 51269fcc-3a1b-43e2-9105-c0488998ea40" daemon [_thread_blocked, id=7496, stack(0x000000fa48400000,0x000000fa48500000) (1024K)]
  0x0000025a5433f900 JavaThread "Start Level: Equinox Container: 51269fcc-3a1b-43e2-9105-c0488998ea40" daemon [_thread_blocked, id=6620, stack(0x000000fa48500000,0x000000fa48600000) (1024K)]
  0x0000025a54c19330 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=13836, stack(0x000000fa48900000,0x000000fa48a00000) (1024K)]
  0x0000025a54d99ce0 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=8128, stack(0x000000fa48a00000,0x000000fa48b00000) (1024K)]
  0x0000025a54c11550 JavaThread "SCR Component Registry"     daemon [_thread_blocked, id=32492, stack(0x000000fa48b00000,0x000000fa48c00000) (1024K)]
  0x0000025a54c10ec0 JavaThread "Worker-JM"                         [_thread_blocked, id=21160, stack(0x000000fa48d00000,0x000000fa48e00000) (1024K)]
  0x0000025a54c12270 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=17228, stack(0x000000fa49100000,0x000000fa49200000) (1024K)]
  0x0000025a54c11be0 JavaThread "Worker-0"                          [_thread_blocked, id=20724, stack(0x000000fa49200000,0x000000fa49300000) (1024K)]
  0x0000025a54c12900 JavaThread "Worker-1: Initialize After Load"        [_thread_blocked, id=31588, stack(0x000000fa49300000,0x000000fa49400000) (1024K)]
  0x0000025a54c13620 JavaThread "Worker-2"                          [_thread_blocked, id=19544, stack(0x000000fa49500000,0x000000fa49600000) (1024K)]
  0x0000025a54c14340 JavaThread "Java indexing"              daemon [_thread_blocked, id=31604, stack(0x000000fa49800000,0x000000fa49900000) (1024K)]
  0x0000025a567a3540 JavaThread "Worker-3: Repository registry initialization"        [_thread_blocked, id=29984, stack(0x000000fa49b00000,0x000000fa49c00000) (1024K)]
  0x0000025a567a2eb0 JavaThread "Worker-4: Updating Maven Dependencies"        [_thread_blocked, id=6628, stack(0x000000fa49c00000,0x000000fa49d00000) (1024K)]
  0x0000025a567a4260 JavaThread "Thread-2"                   daemon [_thread_in_native, id=24808, stack(0x000000fa49d00000,0x000000fa49e00000) (1024K)]
  0x0000025a567a69c0 JavaThread "Thread-3"                   daemon [_thread_in_native, id=20788, stack(0x000000fa49e00000,0x000000fa49f00000) (1024K)]
  0x0000025a567a7050 JavaThread "Thread-4"                   daemon [_thread_in_native, id=28832, stack(0x000000fa49f00000,0x000000fa4a000000) (1024K)]
  0x0000025a567a4f80 JavaThread "Thread-5"                   daemon [_thread_in_native, id=25204, stack(0x000000fa4a000000,0x000000fa4a100000) (1024K)]
  0x0000025a567a5610 JavaThread "Thread-6"                   daemon [_thread_in_native, id=28852, stack(0x000000fa4a100000,0x000000fa4a200000) (1024K)]
  0x0000025a567a5ca0 JavaThread "Thread-7"                   daemon [_thread_in_native, id=30576, stack(0x000000fa4a200000,0x000000fa4a300000) (1024K)]
  0x0000025a567a3bd0 JavaThread "Thread-8"                   daemon [_thread_in_native, id=14608, stack(0x000000fa4a300000,0x000000fa4a400000) (1024K)]
  0x0000025a567a8a90 JavaThread "Thread-9"                   daemon [_thread_in_native, id=24976, stack(0x000000fa4a400000,0x000000fa4a500000) (1024K)]
  0x0000025a567a76e0 JavaThread "Thread-10"                  daemon [_thread_in_native, id=21168, stack(0x000000fa4a500000,0x000000fa4a600000) (1024K)]
  0x0000025a567a97b0 JavaThread "Thread-11"                  daemon [_thread_in_native, id=32024, stack(0x000000fa4a600000,0x000000fa4a700000) (1024K)]
  0x0000025a567a7d70 JavaThread "Thread-12"                  daemon [_thread_in_native, id=29484, stack(0x000000fa4a700000,0x000000fa4a800000) (1024K)]
  0x0000025a567a6330 JavaThread "Thread-13"                  daemon [_thread_in_native, id=27540, stack(0x000000fa4a800000,0x000000fa4a900000) (1024K)]
  0x0000025a567a8400 JavaThread "Thread-14"                  daemon [_thread_in_native, id=5488, stack(0x000000fa4a900000,0x000000fa4aa00000) (1024K)]
  0x0000025a567a9120 JavaThread "Thread-15"                  daemon [_thread_in_native, id=29500, stack(0x000000fa4aa00000,0x000000fa4ab00000) (1024K)]
  0x0000025a567a9e40 JavaThread "Thread-16"                  daemon [_thread_in_native, id=5100, stack(0x000000fa4ab00000,0x000000fa4ac00000) (1024K)]
  0x0000025a567aa4d0 JavaThread "Thread-17"                  daemon [_thread_in_native, id=23344, stack(0x000000fa4ac00000,0x000000fa4ad00000) (1024K)]
  0x0000025a58c4bf10 JavaThread "Thread-18"                  daemon [_thread_in_native, id=4276, stack(0x000000fa4ad00000,0x000000fa4ae00000) (1024K)]
  0x0000025a58c4ab60 JavaThread "Thread-19"                  daemon [_thread_in_native, id=20128, stack(0x000000fa4ae00000,0x000000fa4af00000) (1024K)]
  0x0000025a58c4c5a0 JavaThread "Thread-20"                  daemon [_thread_in_native, id=14576, stack(0x000000fa4af00000,0x000000fa4b000000) (1024K)]
  0x0000025a58c49e40 JavaThread "Thread-21"                  daemon [_thread_in_native, id=19400, stack(0x000000fa4b000000,0x000000fa4b100000) (1024K)]
  0x0000025a58c4cc30 JavaThread "Thread-22"                  daemon [_thread_in_native, id=20688, stack(0x000000fa4b100000,0x000000fa4b200000) (1024K)]
  0x0000025a58c4b1f0 JavaThread "Thread-23"                  daemon [_thread_in_native, id=31836, stack(0x000000fa4b200000,0x000000fa4b300000) (1024K)]
  0x0000025a58c497b0 JavaThread "Thread-24"                  daemon [_thread_in_native, id=31284, stack(0x000000fa4b300000,0x000000fa4b400000) (1024K)]
  0x0000025a58c4d2c0 JavaThread "Thread-25"                  daemon [_thread_in_native, id=32696, stack(0x000000fa4b400000,0x000000fa4b500000) (1024K)]
  0x0000025a58c4d950 JavaThread "Thread-26"                  daemon [_thread_in_native, id=29012, stack(0x000000fa4b500000,0x000000fa4b600000) (1024K)]
  0x0000025a58c4a4d0 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=32600, stack(0x000000fa4b600000,0x000000fa4b700000) (1024K)]
  0x0000025a564a4c50 JavaThread "C2 CompilerThread5"         daemon [_thread_blocked, id=19948, stack(0x000000fa4b700000,0x000000fa4b800000) (1024K)]
=>0x0000025a564a59f0 JavaThread "C2 CompilerThread6"         daemon [_thread_in_native, id=6280, stack(0x000000fa4b800000,0x000000fa4b900000) (1024K)]
  0x0000025a564a60c0 JavaThread "C2 CompilerThread7"         daemon [_thread_blocked, id=30664, stack(0x000000fa4b900000,0x000000fa4ba00000) (1024K)]
  0x0000025a58c4dfe0 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=5000, stack(0x000000fa4ba00000,0x000000fa4bb00000) (1024K)]
  0x0000025a58c4e670 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=20308, stack(0x000000fa4bb00000,0x000000fa4bc00000) (1024K)]
  0x0000025a58c4fa20 JavaThread "Worker-5: Java indexing... "        [_thread_blocked, id=31700, stack(0x000000fa4bc00000,0x000000fa4bd00000) (1024K)]
  0x0000025a58c500b0 JavaThread "Worker-6"                          [_thread_blocked, id=20148, stack(0x000000fa4bd00000,0x000000fa4be00000) (1024K)]
Total: 65

Other Threads:
  0x0000025a7cd89680 VMThread "VM Thread"                           [id=12812, stack(0x000000fa46900000,0x000000fa46a00000) (1024K)]
  0x0000025a7b6fca00 WatcherThread "VM Periodic Task Thread"        [id=28384, stack(0x000000fa46800000,0x000000fa46900000) (1024K)]
  0x0000025a7373bda0 WorkerThread "GC Thread#0"                     [id=4452, stack(0x000000fa46700000,0x000000fa46800000) (1024K)]
  0x0000025a5422b270 WorkerThread "GC Thread#1"                     [id=25532, stack(0x000000fa47900000,0x000000fa47a00000) (1024K)]
  0x0000025a541e6620 WorkerThread "GC Thread#2"                     [id=25924, stack(0x000000fa47a00000,0x000000fa47b00000) (1024K)]
  0x0000025a5422b610 WorkerThread "GC Thread#3"                     [id=32084, stack(0x000000fa47b00000,0x000000fa47c00000) (1024K)]
  0x0000025a5422b9b0 WorkerThread "GC Thread#4"                     [id=30760, stack(0x000000fa47c00000,0x000000fa47d00000) (1024K)]
  0x0000025a7fb182b0 WorkerThread "GC Thread#5"                     [id=26392, stack(0x000000fa47d00000,0x000000fa47e00000) (1024K)]
  0x0000025a7fb17b70 WorkerThread "GC Thread#6"                     [id=32272, stack(0x000000fa47e00000,0x000000fa47f00000) (1024K)]
  0x0000025a7fb189f0 WorkerThread "GC Thread#7"                     [id=32352, stack(0x000000fa48200000,0x000000fa48300000) (1024K)]
  0x0000025a7fb18650 WorkerThread "GC Thread#8"                     [id=3928, stack(0x000000fa48600000,0x000000fa48700000) (1024K)]
  0x0000025a7fb18d90 WorkerThread "GC Thread#9"                     [id=9120, stack(0x000000fa48700000,0x000000fa48800000) (1024K)]
  0x0000025a7fb19130 WorkerThread "GC Thread#10"                    [id=28724, stack(0x000000fa48800000,0x000000fa48900000) (1024K)]
  0x0000025a7fb194d0 WorkerThread "GC Thread#11"                    [id=24720, stack(0x000000fa48c00000,0x000000fa48d00000) (1024K)]
  0x0000025a541fe7c0 WorkerThread "GC Thread#12"                    [id=25948, stack(0x000000fa48e00000,0x000000fa48f00000) (1024K)]
  0x0000025a541fef00 WorkerThread "GC Thread#13"                    [id=24204, stack(0x000000fa48f00000,0x000000fa49000000) (1024K)]
  0x0000025a541ff2a0 WorkerThread "GC Thread#14"                    [id=28712, stack(0x000000fa49000000,0x000000fa49100000) (1024K)]
  0x0000025a541ff640 WorkerThread "GC Thread#15"                    [id=29756, stack(0x000000fa49400000,0x000000fa49500000) (1024K)]
  0x0000025a54200860 WorkerThread "GC Thread#16"                    [id=31756, stack(0x000000fa49600000,0x000000fa49700000) (1024K)]
  0x0000025a541fd940 WorkerThread "GC Thread#17"                    [id=21756, stack(0x000000fa49700000,0x000000fa49800000) (1024K)]
Total: 20

Threads with active compile tasks:
C2 CompilerThread0  5071 7929       4       java.net.URI::<init> (51 bytes)
C2 CompilerThread1  5071 8018       4       org.eclipse.jdt.internal.compiler.ExtraFlags::getExtraFlags (94 bytes)
C2 CompilerThread2  5071 7962       4       org.eclipse.jdt.internal.compiler.util.JrtFileSystem::getClassfileContent (71 bytes)
C2 CompilerThread3  5071 7865   !   4       org.eclipse.jdt.internal.compiler.classfmt.ClassFileReader::<init> (2135 bytes)
C2 CompilerThread4  5071 7393   !   4       org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer::indexDocument (1168 bytes)
C2 CompilerThread5  5071 7930       4       java.lang.invoke.MemberName::getMethodOrFieldType (72 bytes)
C2 CompilerThread6  5071 7560       4       org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer::extractReferenceFromConstantPool (358 bytes)
C2 CompilerThread7  5071 7436   !   4       org.eclipse.osgi.internal.loader.classpath.ClasspathManager::defineClass (636 bytes)
Total: 8

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007fffbf24e308] Threads_lock - owner thread: 0x0000025a7cd89680
[0x00007fffbf24e408] Heap_lock - owner thread: 0x0000025a567a2eb0
[0x00007fffbf24e888] PSOldGenExpand_lock - owner thread: 0x0000025a7fb19130

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000025a0f000000-0x0000025a0fba0000-0x0000025a0fba0000), size 12189696, SharedBaseAddress: 0x0000025a0f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000025a10000000-0x0000025a50000000, reserved size: 1073741824
Narrow klass base: 0x0000025a0f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 24 total, 24 available
 Memory: 16121M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 18

Heap:
 PSYoungGen      total 19456K, used 19353K [0x00000000d5580000, 0x00000000d6d80000, 0x0000000100000000)
  eden space 15360K, 100% used [0x00000000d5580000,0x00000000d6480000,0x00000000d6480000)
  from space 4096K, 97% used [0x00000000d6580000,0x00000000d6966478,0x00000000d6980000)
  to   space 4096K, 41% used [0x00000000d6980000,0x00000000d6b2de68,0x00000000d6d80000)
 ParOldGen       total 87552K, used 87551K [0x0000000080000000, 0x0000000085580000, 0x00000000d5580000)
  object space 87552K, 99% used [0x0000000080000000,0x000000008557fd50,0x0000000085580000)
 Metaspace       used 48759K, committed 49984K, reserved 1114112K
  class space    used 5106K, committed 5696K, reserved 1048576K

Card table byte_map: [0x0000025a76b30000,0x0000025a76f40000] _byte_map_base: 0x0000025a76730000

Marking Bits: (ParMarkBitMap*) 0x00007fffbf2531f0
 Begin Bits: [0x0000025a771f0000, 0x0000025a791f0000)
 End Bits:   [0x0000025a791f0000, 0x0000025a7b1f0000)

Polling page: 0x0000025a737e0000

Metaspace:

Usage:
  Non-class:     42.63 MB used.
      Class:      4.99 MB used.
       Both:     47.62 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      43.25 MB ( 68%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       5.56 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      48.81 MB (  4%) committed. 

Chunk freelists:
   Non-Class:  4.77 MB
       Class:  10.34 MB
        Both:  15.11 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 58.38 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 860.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 781.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 20.
num_chunks_taken_from_freelist: 3080.
num_chunk_merges: 12.
num_chunk_splits: 1898.
num_chunks_enlarged: 1075.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=4635Kb max_used=4635Kb free=114532Kb
 bounds [0x0000025a07ba0000, 0x0000025a08030000, 0x0000025a0f000000]
CodeHeap 'profiled nmethods': size=119104Kb used=15942Kb max_used=15942Kb free=103162Kb
 bounds [0x0000025a00000000, 0x0000025a00fa0000, 0x0000025a07450000]
CodeHeap 'non-nmethods': size=7488Kb used=2520Kb max_used=3158Kb free=4968Kb
 bounds [0x0000025a07450000, 0x0000025a07770000, 0x0000025a07ba0000]
 total_blobs=8087 nmethods=7387 adapters=604
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 3.979 Thread 0x0000025a737db710 nmethod 8017 0x0000025a00f8cd10 code [0x0000025a00f8cf00, 0x0000025a00f8d3d0]
Event: 3.979 Thread 0x0000025a7f5d24c0 nmethod 8009 0x0000025a00f8d510 code [0x0000025a00f8d6a0, 0x0000025a00f8d798]
Event: 3.979 Thread 0x0000025a7ce7b460 nmethod 8008 0x0000025a00f8d810 code [0x0000025a00f8d9c0, 0x0000025a00f8db70]
Event: 3.979 Thread 0x0000025a737db710 8011       3       com.google.common.collect.AbstractMultimap::isEmpty (13 bytes)
Event: 3.979 Thread 0x0000025a7cf493d0 nmethod 8010 0x0000025a00f8dc90 code [0x0000025a00f8de40, 0x0000025a00f8e098]
Event: 3.979 Thread 0x0000025a7f5d24c0 8015       3       com.google.common.collect.AbstractSetMultimap::get (6 bytes)
Event: 3.979 Thread 0x0000025a7ce7b460 8016       3       com.google.common.collect.AbstractSetMultimap::get (9 bytes)
Event: 3.979 Thread 0x0000025a7cf493d0 8019       3       com.google.inject.internal.InternalInjectorCreator::isEagerSingleton (62 bytes)
Event: 3.979 Thread 0x0000025a737db710 nmethod 8011 0x0000025a00f8e190 code [0x0000025a00f8e340, 0x0000025a00f8e558]
Event: 3.979 Thread 0x0000025a7f5d24c0 nmethod 8015 0x0000025a00f8e610 code [0x0000025a00f8e7e0, 0x0000025a00f8eae8]
Event: 3.980 Thread 0x0000025a737db710 8020       3       com.google.inject.internal.Scoping::isEagerSingleton (38 bytes)
Event: 3.980 Thread 0x0000025a7ce7b460 nmethod 8016 0x0000025a00f8ec10 code [0x0000025a00f8ee00, 0x0000025a00f8f428]
Event: 3.980 Thread 0x0000025a7f5d24c0 8012       3       com.google.common.collect.LinkedHashMultimap$ValueEntry::getPredecessorInMultimap (11 bytes)
Event: 3.980 Thread 0x0000025a737db710 nmethod 8020 0x0000025a00f8f590 code [0x0000025a00f8f740, 0x0000025a00f8f9b0]
Event: 3.980 Thread 0x0000025a7ce7b460 8013       3       com.google.common.collect.Sets$ImprovedAbstractSet::<init> (5 bytes)
Event: 3.980 Thread 0x0000025a737db710 8014       3       com.google.common.collect.Hashing::closedTableSize (37 bytes)
Event: 3.980 Thread 0x0000025a7cf493d0 nmethod 8019 0x0000025a00f8fa90 code [0x0000025a00f8fcc0, 0x0000025a00f905b8]
Event: 3.980 Thread 0x0000025a7f5d24c0 nmethod 8012 0x0000025a00f90810 code [0x0000025a00f909c0, 0x0000025a00f90c20]
Event: 3.980 Thread 0x0000025a7ce7b460 nmethod 8013 0x0000025a00f90d10 code [0x0000025a00f90ec0, 0x0000025a00f910e8]
Event: 3.981 Thread 0x0000025a737db710 nmethod 8014 0x0000025a00f91210 code [0x0000025a00f913e0, 0x0000025a00f91700]

GC Heap History (20 events):
Event: 3.583 GC heap before
{Heap before GC invocations=32 (full 2):
 PSYoungGen      total 18944K, used 18819K [0x00000000d5580000, 0x00000000d6e80000, 0x0000000100000000)
  eden space 15360K, 100% used [0x00000000d5580000,0x00000000d6480000,0x00000000d6480000)
  from space 3584K, 96% used [0x00000000d6480000,0x00000000d67e0d58,0x00000000d6800000)
  to   space 5120K, 0% used [0x00000000d6980000,0x00000000d6980000,0x00000000d6e80000)
 ParOldGen       total 68608K, used 53176K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 77% used [0x0000000080000000,0x00000000833ee148,0x0000000084300000)
 Metaspace       used 46615K, committed 47808K, reserved 1114112K
  class space    used 4816K, committed 5376K, reserved 1048576K
}
Event: 3.587 GC heap after
{Heap after GC invocations=32 (full 2):
 PSYoungGen      total 19456K, used 3728K [0x00000000d5580000, 0x00000000d6d80000, 0x0000000100000000)
  eden space 15360K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6480000)
  from space 4096K, 91% used [0x00000000d6980000,0x00000000d6d24380,0x00000000d6d80000)
  to   space 4096K, 0% used [0x00000000d6580000,0x00000000d6580000,0x00000000d6980000)
 ParOldGen       total 68608K, used 56332K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 82% used [0x0000000080000000,0x00000000837030c8,0x0000000084300000)
 Metaspace       used 46615K, committed 47808K, reserved 1114112K
  class space    used 4816K, committed 5376K, reserved 1048576K
}
Event: 3.624 GC heap before
{Heap before GC invocations=33 (full 2):
 PSYoungGen      total 19456K, used 19088K [0x00000000d5580000, 0x00000000d6d80000, 0x0000000100000000)
  eden space 15360K, 100% used [0x00000000d5580000,0x00000000d6480000,0x00000000d6480000)
  from space 4096K, 91% used [0x00000000d6980000,0x00000000d6d24380,0x00000000d6d80000)
  to   space 4096K, 0% used [0x00000000d6580000,0x00000000d6580000,0x00000000d6980000)
 ParOldGen       total 68608K, used 56332K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 82% used [0x0000000080000000,0x00000000837030c8,0x0000000084300000)
 Metaspace       used 46841K, committed 48064K, reserved 1114112K
  class space    used 4844K, committed 5440K, reserved 1048576K
}
Event: 3.628 GC heap after
{Heap after GC invocations=33 (full 2):
 PSYoungGen      total 18944K, used 3536K [0x00000000d5580000, 0x00000000d6c80000, 0x0000000100000000)
  eden space 15360K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6480000)
  from space 3584K, 98% used [0x00000000d6580000,0x00000000d68f4290,0x00000000d6900000)
  to   space 3584K, 0% used [0x00000000d6900000,0x00000000d6900000,0x00000000d6c80000)
 ParOldGen       total 68608K, used 59592K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 86% used [0x0000000080000000,0x0000000083a320f0,0x0000000084300000)
 Metaspace       used 46841K, committed 48064K, reserved 1114112K
  class space    used 4844K, committed 5440K, reserved 1048576K
}
Event: 3.663 GC heap before
{Heap before GC invocations=34 (full 2):
 PSYoungGen      total 18944K, used 18896K [0x00000000d5580000, 0x00000000d6c80000, 0x0000000100000000)
  eden space 15360K, 100% used [0x00000000d5580000,0x00000000d6480000,0x00000000d6480000)
  from space 3584K, 98% used [0x00000000d6580000,0x00000000d68f4290,0x00000000d6900000)
  to   space 3584K, 0% used [0x00000000d6900000,0x00000000d6900000,0x00000000d6c80000)
 ParOldGen       total 68608K, used 59592K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 86% used [0x0000000080000000,0x0000000083a320f0,0x0000000084300000)
 Metaspace       used 47205K, committed 48448K, reserved 1114112K
  class space    used 4880K, committed 5440K, reserved 1048576K
}
Event: 3.666 GC heap after
{Heap after GC invocations=34 (full 2):
 PSYoungGen      total 18944K, used 3376K [0x00000000d5580000, 0x00000000d6c80000, 0x0000000100000000)
  eden space 15360K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6480000)
  from space 3584K, 94% used [0x00000000d6900000,0x00000000d6c4c210,0x00000000d6c80000)
  to   space 3584K, 0% used [0x00000000d6580000,0x00000000d6580000,0x00000000d6900000)
 ParOldGen       total 68608K, used 62755K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 91% used [0x0000000080000000,0x0000000083d48e80,0x0000000084300000)
 Metaspace       used 47205K, committed 48448K, reserved 1114112K
  class space    used 4880K, committed 5440K, reserved 1048576K
}
Event: 3.702 GC heap before
{Heap before GC invocations=35 (full 2):
 PSYoungGen      total 18944K, used 18736K [0x00000000d5580000, 0x00000000d6c80000, 0x0000000100000000)
  eden space 15360K, 100% used [0x00000000d5580000,0x00000000d6480000,0x00000000d6480000)
  from space 3584K, 94% used [0x00000000d6900000,0x00000000d6c4c210,0x00000000d6c80000)
  to   space 3584K, 0% used [0x00000000d6580000,0x00000000d6580000,0x00000000d6900000)
 ParOldGen       total 68608K, used 62755K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 91% used [0x0000000080000000,0x0000000083d48e80,0x0000000084300000)
 Metaspace       used 47402K, committed 48704K, reserved 1114112K
  class space    used 4901K, committed 5504K, reserved 1048576K
}
Event: 3.705 GC heap after
{Heap after GC invocations=35 (full 2):
 PSYoungGen      total 18944K, used 3554K [0x00000000d5580000, 0x00000000d7280000, 0x0000000100000000)
  eden space 15360K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6480000)
  from space 3584K, 99% used [0x00000000d6580000,0x00000000d68f8838,0x00000000d6900000)
  to   space 7168K, 0% used [0x00000000d6b80000,0x00000000d6b80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 65769K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 95% used [0x0000000080000000,0x000000008403a798,0x0000000084300000)
 Metaspace       used 47402K, committed 48704K, reserved 1114112K
  class space    used 4901K, committed 5504K, reserved 1048576K
}
Event: 3.748 GC heap before
{Heap before GC invocations=36 (full 2):
 PSYoungGen      total 18944K, used 18914K [0x00000000d5580000, 0x00000000d7280000, 0x0000000100000000)
  eden space 15360K, 100% used [0x00000000d5580000,0x00000000d6480000,0x00000000d6480000)
  from space 3584K, 99% used [0x00000000d6580000,0x00000000d68f8838,0x00000000d6900000)
  to   space 7168K, 0% used [0x00000000d6b80000,0x00000000d6b80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 65769K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 95% used [0x0000000080000000,0x000000008403a798,0x0000000084300000)
 Metaspace       used 47652K, committed 48896K, reserved 1114112K
  class space    used 4936K, committed 5504K, reserved 1048576K
}
Event: 3.752 GC heap after
{Heap after GC invocations=36 (full 2):
 PSYoungGen      total 19456K, used 4089K [0x00000000d5580000, 0x00000000d6f80000, 0x0000000100000000)
  eden space 15360K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6480000)
  from space 4096K, 99% used [0x00000000d6b80000,0x00000000d6f7e590,0x00000000d6f80000)
  to   space 5120K, 0% used [0x00000000d6580000,0x00000000d6580000,0x00000000d6a80000)
 ParOldGen       total 69120K, used 69030K [0x0000000080000000, 0x0000000084380000, 0x00000000d5580000)
  object space 69120K, 99% used [0x0000000080000000,0x0000000084369b38,0x0000000084380000)
 Metaspace       used 47652K, committed 48896K, reserved 1114112K
  class space    used 4936K, committed 5504K, reserved 1048576K
}
Event: 3.789 GC heap before
{Heap before GC invocations=37 (full 2):
 PSYoungGen      total 19456K, used 19449K [0x00000000d5580000, 0x00000000d6f80000, 0x0000000100000000)
  eden space 15360K, 100% used [0x00000000d5580000,0x00000000d6480000,0x00000000d6480000)
  from space 4096K, 99% used [0x00000000d6b80000,0x00000000d6f7e590,0x00000000d6f80000)
  to   space 5120K, 0% used [0x00000000d6580000,0x00000000d6580000,0x00000000d6a80000)
 ParOldGen       total 69120K, used 69030K [0x0000000080000000, 0x0000000084380000, 0x00000000d5580000)
  object space 69120K, 99% used [0x0000000080000000,0x0000000084369b38,0x0000000084380000)
 Metaspace       used 47817K, committed 49024K, reserved 1114112K
  class space    used 4961K, committed 5504K, reserved 1048576K
}
Event: 3.792 GC heap after
{Heap after GC invocations=37 (full 2):
 PSYoungGen      total 18944K, used 3483K [0x00000000d5580000, 0x00000000d6c80000, 0x0000000100000000)
  eden space 15360K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6480000)
  from space 3584K, 97% used [0x00000000d6580000,0x00000000d68e6cb8,0x00000000d6900000)
  to   space 3584K, 0% used [0x00000000d6900000,0x00000000d6900000,0x00000000d6c80000)
 ParOldGen       total 73216K, used 72730K [0x0000000080000000, 0x0000000084780000, 0x00000000d5580000)
  object space 73216K, 99% used [0x0000000080000000,0x00000000847069f0,0x0000000084780000)
 Metaspace       used 47817K, committed 49024K, reserved 1114112K
  class space    used 4961K, committed 5504K, reserved 1048576K
}
Event: 3.828 GC heap before
{Heap before GC invocations=38 (full 2):
 PSYoungGen      total 18944K, used 18843K [0x00000000d5580000, 0x00000000d6c80000, 0x0000000100000000)
  eden space 15360K, 100% used [0x00000000d5580000,0x00000000d6480000,0x00000000d6480000)
  from space 3584K, 97% used [0x00000000d6580000,0x00000000d68e6cb8,0x00000000d6900000)
  to   space 3584K, 0% used [0x00000000d6900000,0x00000000d6900000,0x00000000d6c80000)
 ParOldGen       total 73216K, used 72730K [0x0000000080000000, 0x0000000084780000, 0x00000000d5580000)
  object space 73216K, 99% used [0x0000000080000000,0x00000000847069f0,0x0000000084780000)
 Metaspace       used 47967K, committed 49216K, reserved 1114112K
  class space    used 4982K, committed 5568K, reserved 1048576K
}
Event: 3.831 GC heap after
{Heap after GC invocations=38 (full 2):
 PSYoungGen      total 18944K, used 3489K [0x00000000d5580000, 0x00000000d6c80000, 0x0000000100000000)
  eden space 15360K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6480000)
  from space 3584K, 97% used [0x00000000d6900000,0x00000000d6c687d8,0x00000000d6c80000)
  to   space 3584K, 0% used [0x00000000d6580000,0x00000000d6580000,0x00000000d6900000)
 ParOldGen       total 76288K, used 75906K [0x0000000080000000, 0x0000000084a80000, 0x00000000d5580000)
  object space 76288K, 99% used [0x0000000080000000,0x0000000084a20980,0x0000000084a80000)
 Metaspace       used 47967K, committed 49216K, reserved 1114112K
  class space    used 4982K, committed 5568K, reserved 1048576K
}
Event: 3.869 GC heap before
{Heap before GC invocations=39 (full 2):
 PSYoungGen      total 18944K, used 18849K [0x00000000d5580000, 0x00000000d6c80000, 0x0000000100000000)
  eden space 15360K, 100% used [0x00000000d5580000,0x00000000d6480000,0x00000000d6480000)
  from space 3584K, 97% used [0x00000000d6900000,0x00000000d6c687d8,0x00000000d6c80000)
  to   space 3584K, 0% used [0x00000000d6580000,0x00000000d6580000,0x00000000d6900000)
 ParOldGen       total 76288K, used 75906K [0x0000000080000000, 0x0000000084a80000, 0x00000000d5580000)
  object space 76288K, 99% used [0x0000000080000000,0x0000000084a20980,0x0000000084a80000)
 Metaspace       used 48150K, committed 49408K, reserved 1114112K
  class space    used 5003K, committed 5568K, reserved 1048576K
}
Event: 3.872 GC heap after
{Heap after GC invocations=39 (full 2):
 PSYoungGen      total 18944K, used 3559K [0x00000000d5580000, 0x00000000d7380000, 0x0000000100000000)
  eden space 15360K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6480000)
  from space 3584K, 99% used [0x00000000d6580000,0x00000000d68f9cd0,0x00000000d6900000)
  to   space 7680K, 0% used [0x00000000d6c00000,0x00000000d6c00000,0x00000000d7380000)
 ParOldGen       total 79360K, used 79176K [0x0000000080000000, 0x0000000084d80000, 0x00000000d5580000)
  object space 79360K, 99% used [0x0000000080000000,0x0000000084d523b8,0x0000000084d80000)
 Metaspace       used 48150K, committed 49408K, reserved 1114112K
  class space    used 5003K, committed 5568K, reserved 1048576K
}
Event: 3.920 GC heap before
{Heap before GC invocations=40 (full 2):
 PSYoungGen      total 18944K, used 18919K [0x00000000d5580000, 0x00000000d7380000, 0x0000000100000000)
  eden space 15360K, 100% used [0x00000000d5580000,0x00000000d6480000,0x00000000d6480000)
  from space 3584K, 99% used [0x00000000d6580000,0x00000000d68f9cd0,0x00000000d6900000)
  to   space 7680K, 0% used [0x00000000d6c00000,0x00000000d6c00000,0x00000000d7380000)
 ParOldGen       total 79360K, used 79176K [0x0000000080000000, 0x0000000084d80000, 0x00000000d5580000)
  object space 79360K, 99% used [0x0000000080000000,0x0000000084d523b8,0x0000000084d80000)
 Metaspace       used 48338K, committed 49600K, reserved 1114112K
  class space    used 5037K, committed 5632K, reserved 1048576K
}
Event: 3.923 GC heap after
{Heap after GC invocations=40 (full 2):
 PSYoungGen      total 18944K, used 3454K [0x00000000d5580000, 0x00000000d6f80000, 0x0000000100000000)
  eden space 15360K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6480000)
  from space 3584K, 96% used [0x00000000d6c00000,0x00000000d6f5fbd8,0x00000000d6f80000)
  to   space 5120K, 0% used [0x00000000d6580000,0x00000000d6580000,0x00000000d6a80000)
 ParOldGen       total 82944K, used 82489K [0x0000000080000000, 0x0000000085100000, 0x00000000d5580000)
  object space 82944K, 99% used [0x0000000080000000,0x000000008508e720,0x0000000085100000)
 Metaspace       used 48338K, committed 49600K, reserved 1114112K
  class space    used 5037K, committed 5632K, reserved 1048576K
}
Event: 3.960 GC heap before
{Heap before GC invocations=41 (full 2):
 PSYoungGen      total 18944K, used 18814K [0x00000000d5580000, 0x00000000d6f80000, 0x0000000100000000)
  eden space 15360K, 100% used [0x00000000d5580000,0x00000000d6480000,0x00000000d6480000)
  from space 3584K, 96% used [0x00000000d6c00000,0x00000000d6f5fbd8,0x00000000d6f80000)
  to   space 5120K, 0% used [0x00000000d6580000,0x00000000d6580000,0x00000000d6a80000)
 ParOldGen       total 82944K, used 82489K [0x0000000080000000, 0x0000000085100000, 0x00000000d5580000)
  object space 82944K, 99% used [0x0000000080000000,0x000000008508e720,0x0000000085100000)
 Metaspace       used 48546K, committed 49792K, reserved 1114112K
  class space    used 5069K, committed 5632K, reserved 1048576K
}
Event: 3.964 GC heap after
{Heap after GC invocations=41 (full 2):
 PSYoungGen      total 19456K, used 3993K [0x00000000d5580000, 0x00000000d6d80000, 0x0000000100000000)
  eden space 15360K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6480000)
  from space 4096K, 97% used [0x00000000d6580000,0x00000000d6966478,0x00000000d6980000)
  to   space 4096K, 0% used [0x00000000d6980000,0x00000000d6980000,0x00000000d6d80000)
 ParOldGen       total 86016K, used 85564K [0x0000000080000000, 0x0000000085400000, 0x00000000d5580000)
  object space 86016K, 99% used [0x0000000080000000,0x000000008538f0c0,0x0000000085400000)
 Metaspace       used 48546K, committed 49792K, reserved 1114112K
  class space    used 5069K, committed 5632K, reserved 1048576K
}

Dll operation events (10 events):
Event: 0.005 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.020 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.050 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.053 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.053 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.055 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.063 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.098 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 0.626 Loaded shared library C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 1.411 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-54154080\jna1566400863141209007.dll

Deoptimization events (20 events):
Event: 3.969 Thread 0x0000025a54c14340 DEOPT PACKING pc=0x0000025a00d68b03 sp=0x000000fa498fe7e0
Event: 3.970 Thread 0x0000025a54c14340 DEOPT UNPACKING pc=0x0000025a074a4242 sp=0x000000fa498fdd48 mode 0
Event: 3.974 Thread 0x0000025a54c14340 DEOPT PACKING pc=0x0000025a00d72f1a sp=0x000000fa498fe7a0
Event: 3.974 Thread 0x0000025a54c14340 DEOPT UNPACKING pc=0x0000025a074a4242 sp=0x000000fa498fdd28 mode 0
Event: 3.976 Thread 0x0000025a54c14340 DEOPT PACKING pc=0x0000025a00d68b03 sp=0x000000fa498fe7e0
Event: 3.976 Thread 0x0000025a54c14340 DEOPT UNPACKING pc=0x0000025a074a4242 sp=0x000000fa498fdd48 mode 0
Event: 3.979 Thread 0x0000025a54c14340 DEOPT PACKING pc=0x0000025a00d68b03 sp=0x000000fa498fe7e0
Event: 3.979 Thread 0x0000025a54c14340 DEOPT UNPACKING pc=0x0000025a074a4242 sp=0x000000fa498fdd48 mode 0
Event: 3.982 Thread 0x0000025a54c14340 DEOPT PACKING pc=0x0000025a00d68b03 sp=0x000000fa498fe7e0
Event: 3.982 Thread 0x0000025a54c14340 DEOPT UNPACKING pc=0x0000025a074a4242 sp=0x000000fa498fdd48 mode 0
Event: 3.986 Thread 0x0000025a54c14340 DEOPT PACKING pc=0x0000025a00d72f1a sp=0x000000fa498fe7a0
Event: 3.986 Thread 0x0000025a54c14340 DEOPT UNPACKING pc=0x0000025a074a4242 sp=0x000000fa498fdd28 mode 0
Event: 4.026 Thread 0x0000025a54c14340 DEOPT PACKING pc=0x0000025a00d72f1a sp=0x000000fa498fe7a0
Event: 4.026 Thread 0x0000025a54c14340 DEOPT UNPACKING pc=0x0000025a074a4242 sp=0x000000fa498fdd28 mode 0
Event: 4.029 Thread 0x0000025a54c14340 DEOPT PACKING pc=0x0000025a00d68b03 sp=0x000000fa498fe7e0
Event: 4.029 Thread 0x0000025a54c14340 DEOPT UNPACKING pc=0x0000025a074a4242 sp=0x000000fa498fdd48 mode 0
Event: 4.031 Thread 0x0000025a54c14340 DEOPT PACKING pc=0x0000025a00d72f1a sp=0x000000fa498fe7a0
Event: 4.031 Thread 0x0000025a54c14340 DEOPT UNPACKING pc=0x0000025a074a4242 sp=0x000000fa498fdd28 mode 0
Event: 4.032 Thread 0x0000025a54c14340 DEOPT PACKING pc=0x0000025a00d68b03 sp=0x000000fa498fe7e0
Event: 4.032 Thread 0x0000025a54c14340 DEOPT UNPACKING pc=0x0000025a074a4242 sp=0x000000fa498fdd48 mode 0

Classes loaded (20 events):
Event: 3.201 Loading class sun/nio/cs/ThreadLocalCoders
Event: 3.201 Loading class sun/nio/cs/ThreadLocalCoders done
Event: 3.201 Loading class sun/nio/cs/ThreadLocalCoders$1
Event: 3.201 Loading class sun/nio/cs/ThreadLocalCoders$Cache
Event: 3.201 Loading class sun/nio/cs/ThreadLocalCoders$Cache done
Event: 3.201 Loading class sun/nio/cs/ThreadLocalCoders$1 done
Event: 3.201 Loading class sun/nio/cs/ThreadLocalCoders$2
Event: 3.201 Loading class sun/nio/cs/ThreadLocalCoders$2 done
Event: 3.214 Loading class java/util/WeakHashMap$KeyIterator
Event: 3.214 Loading class java/util/WeakHashMap$KeyIterator done
Event: 3.306 Loading class sun/reflect/generics/scope/ConstructorScope
Event: 3.306 Loading class sun/reflect/generics/scope/ConstructorScope done
Event: 3.306 Loading class sun/reflect/generics/tree/VoidDescriptor
Event: 3.306 Loading class sun/reflect/generics/tree/VoidDescriptor done
Event: 3.460 Loading class java/util/concurrent/CompletionService
Event: 3.460 Loading class java/util/concurrent/CompletionService done
Event: 3.469 Loading class java/nio/channels/ServerSocketChannel
Event: 3.469 Loading class java/nio/channels/ServerSocketChannel done
Event: 3.677 Loading class jdk/internal/org/objectweb/asm/ClassReader
Event: 3.678 Loading class jdk/internal/org/objectweb/asm/ClassReader done

Classes unloaded (7 events):
Event: 1.684 Thread 0x0000025a7cd89680 Unloading class 0x0000025a101a4400 'java/lang/invoke/LambdaForm$MH+0x0000025a101a4400'
Event: 1.684 Thread 0x0000025a7cd89680 Unloading class 0x0000025a101a4000 'java/lang/invoke/LambdaForm$MH+0x0000025a101a4000'
Event: 1.684 Thread 0x0000025a7cd89680 Unloading class 0x0000025a101a3c00 'java/lang/invoke/LambdaForm$MH+0x0000025a101a3c00'
Event: 1.684 Thread 0x0000025a7cd89680 Unloading class 0x0000025a101a3800 'java/lang/invoke/LambdaForm$MH+0x0000025a101a3800'
Event: 1.684 Thread 0x0000025a7cd89680 Unloading class 0x0000025a101a3400 'java/lang/invoke/LambdaForm$BMH+0x0000025a101a3400'
Event: 1.684 Thread 0x0000025a7cd89680 Unloading class 0x0000025a101a3000 'java/lang/invoke/LambdaForm$DMH+0x0000025a101a3000'
Event: 1.684 Thread 0x0000025a7cd89680 Unloading class 0x0000025a101a2000 'java/lang/invoke/LambdaForm$DMH+0x0000025a101a2000'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 2.775 Thread 0x0000025a567a3540 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d56a9198}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000d56a9198) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.778 Thread 0x0000025a567a3540 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5716ab0}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d5716ab0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.853 Thread 0x0000025a567a2eb0 Exception <a 'java/lang/NoClassDefFoundError'{0x00000000d684b6b8}: javax/enterprise/inject/Typed> (0x00000000d684b6b8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 301]
Event: 3.171 Thread 0x0000025a54c11be0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d57cac48}> (0x00000000d57cac48) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.171 Thread 0x0000025a54c11be0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d57cb278}> (0x00000000d57cb278) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.179 Thread 0x0000025a54c11be0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d58395c0}> (0x00000000d58395c0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.179 Thread 0x0000025a54c11be0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5839ea0}> (0x00000000d5839ea0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.202 Thread 0x0000025a54c11be0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5bc71e0}> (0x00000000d5bc71e0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.203 Thread 0x0000025a54c11be0 Exception <a 'java/io/FileNotFoundException'{0x00000000d5bc93a8}> (0x00000000d5bc93a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.212 Thread 0x0000025a54c11be0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5cfb4a8}> (0x00000000d5cfb4a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.212 Thread 0x0000025a54c11be0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5cfbd88}> (0x00000000d5cfbd88) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.215 Thread 0x0000025a54c11be0 Exception <a 'java/io/FileNotFoundException'{0x00000000d5d1f5d8}> (0x00000000d5d1f5d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.215 Thread 0x0000025a54c11be0 Exception <a 'java/io/FileNotFoundException'{0x00000000d5f83000}> (0x00000000d5f83000) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.215 Thread 0x0000025a54c11be0 Exception <a 'java/io/FileNotFoundException'{0x00000000d5f83f50}> (0x00000000d5f83f50) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.385 Thread 0x0000025a54c14340 Implicit null exception at 0x0000025a07f3485a to 0x0000025a07f34958
Event: 3.644 Thread 0x0000025a567a2eb0 Exception <a 'java/lang/ExceptionInInitializerError'{0x00000000d5c421d8}> (0x00000000d5c421d8) 
thrown [s\src\hotspot\share\oops\instanceKlass.cpp, line 1220]
Event: 3.679 Thread 0x0000025a567a2eb0 Implicit null exception at 0x0000025a07ce63dd to 0x0000025a07ce6628
Event: 3.679 Thread 0x0000025a567a2eb0 Implicit null exception at 0x0000025a07c5109c to 0x0000025a07c51fb8
Event: 3.683 Thread 0x0000025a567a2eb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5b67258}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, int, java.lang.Object)'> (0x00000000d5b67258) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.029 Thread 0x0000025a567a2eb0 Exception <a 'java/lang/NoClassDefFoundError'{0x00000000d616b6b0}: com/google/inject/servlet/ServletModuleTargetVisitor> (0x00000000d616b6b0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 301]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 3.628 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 3.663 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 3.666 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 3.702 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 3.705 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 3.730 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 3.730 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 3.748 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 3.752 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 3.789 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 3.792 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 3.828 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 3.831 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 3.869 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 3.872 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 3.920 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 3.923 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 3.960 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 3.964 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 4.981 Executing VM operation: Cleanup

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 2.487 Thread 0x0000025a7cd89680 flushing  nmethod 0x0000025a00745490
Event: 2.487 Thread 0x0000025a7cd89680 flushing  nmethod 0x0000025a00756390
Event: 2.487 Thread 0x0000025a7cd89680 flushing  nmethod 0x0000025a00756910
Event: 2.487 Thread 0x0000025a7cd89680 flushing  nmethod 0x0000025a0076fb90
Event: 2.487 Thread 0x0000025a7cd89680 flushing  nmethod 0x0000025a0076ff90
Event: 2.487 Thread 0x0000025a7cd89680 flushing  nmethod 0x0000025a00770890
Event: 2.487 Thread 0x0000025a7cd89680 flushing  nmethod 0x0000025a00772690
Event: 2.487 Thread 0x0000025a7cd89680 flushing  nmethod 0x0000025a00774390
Event: 2.487 Thread 0x0000025a7cd89680 flushing  nmethod 0x0000025a00774890
Event: 2.487 Thread 0x0000025a7cd89680 flushing  nmethod 0x0000025a00775810
Event: 2.487 Thread 0x0000025a7cd89680 flushing  nmethod 0x0000025a00775c90
Event: 2.487 Thread 0x0000025a7cd89680 flushing  nmethod 0x0000025a00776990
Event: 2.487 Thread 0x0000025a7cd89680 flushing  nmethod 0x0000025a00777290
Event: 2.487 Thread 0x0000025a7cd89680 flushing  nmethod 0x0000025a00779190
Event: 2.487 Thread 0x0000025a7cd89680 flushing  nmethod 0x0000025a00779690
Event: 2.487 Thread 0x0000025a7cd89680 flushing  nmethod 0x0000025a00780d90
Event: 2.487 Thread 0x0000025a7cd89680 flushing  nmethod 0x0000025a007c0090
Event: 2.487 Thread 0x0000025a7cd89680 flushing  nmethod 0x0000025a007c0b90
Event: 2.487 Thread 0x0000025a7cd89680 flushing  nmethod 0x0000025a007c6910
Event: 2.487 Thread 0x0000025a7cd89680 flushing  nmethod 0x0000025a007e9e10

Events (20 events):
Event: 2.336 Thread 0x0000025a7371e940 Thread added: 0x0000025a567a9e40
Event: 2.336 Thread 0x0000025a7371e940 Thread added: 0x0000025a567aa4d0
Event: 2.336 Thread 0x0000025a7371e940 Thread added: 0x0000025a58c4bf10
Event: 2.336 Thread 0x0000025a7371e940 Thread added: 0x0000025a58c4ab60
Event: 2.336 Thread 0x0000025a7371e940 Thread added: 0x0000025a58c4c5a0
Event: 2.336 Thread 0x0000025a7371e940 Thread added: 0x0000025a58c49e40
Event: 2.336 Thread 0x0000025a7371e940 Thread added: 0x0000025a58c4cc30
Event: 2.337 Thread 0x0000025a7371e940 Thread added: 0x0000025a58c4b1f0
Event: 2.337 Thread 0x0000025a7371e940 Thread added: 0x0000025a58c497b0
Event: 2.337 Thread 0x0000025a7371e940 Thread added: 0x0000025a58c4d2c0
Event: 2.337 Thread 0x0000025a7371e940 Thread added: 0x0000025a58c4d950
Event: 2.360 Thread 0x0000025a7371e940 Thread added: 0x0000025a58c4a4d0
Event: 2.387 Thread 0x0000025a7ce7b460 Thread added: 0x0000025a564a4c50
Event: 2.390 Thread 0x0000025a737db710 Thread added: 0x0000025a564a59f0
Event: 2.390 Thread 0x0000025a7cf493d0 Thread added: 0x0000025a564a60c0
Event: 2.649 Thread 0x0000025a7371e940 Thread added: 0x0000025a58c4dfe0
Event: 2.649 Thread 0x0000025a7371e940 Thread added: 0x0000025a58c4e670
Event: 3.203 Thread 0x0000025a54c13620 Thread added: 0x0000025a58c4fa20
Event: 3.203 Thread 0x0000025a58c4fa20 Thread added: 0x0000025a58c500b0
Event: 4.546 Thread 0x0000025a7f5d24c0 Thread exited: 0x0000025a7f5d24c0


Dynamic libraries:
0x00007ff7daa60000 - 0x00007ff7daa6e000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff854d20000 - 0x00007ff854f88000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff853e10000 - 0x00007ff853ed9000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff8526d0000 - 0x00007ff852abd000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff8522a0000 - 0x00007ff8523eb000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff82fb20000 - 0x00007ff82fb38000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff8331a0000 - 0x00007ff8331be000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff853430000 - 0x00007ff8535fc000 	C:\WINDOWS\System32\USER32.dll
0x00007ff852040000 - 0x00007ff852067000 	C:\WINDOWS\System32\win32u.dll
0x00007ff831500000 - 0x00007ff83179a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3\COMCTL32.dll
0x00007ff854440000 - 0x00007ff85446b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff854290000 - 0x00007ff854339000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff851f00000 - 0x00007ff852037000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff852070000 - 0x00007ff852113000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff854340000 - 0x00007ff85436f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff84c870000 - 0x00007ff84c87c000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff823400000 - 0x00007ff82348d000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007fffbe5a0000 - 0x00007fffbf330000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff8541d0000 - 0x00007ff854284000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff854380000 - 0x00007ff854426000 	C:\WINDOWS\System32\sechost.dll
0x00007ff854470000 - 0x00007ff854588000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff853140000 - 0x00007ff8531b4000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff850a90000 - 0x00007ff850aee000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff842b20000 - 0x00007ff842b55000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff84b030000 - 0x00007ff84b03b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff850a70000 - 0x00007ff850a84000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff850d30000 - 0x00007ff850d4b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff84c860000 - 0x00007ff84c86a000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff84f070000 - 0x00007ff84f2b1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff853910000 - 0x00007ff853c96000 	C:\WINDOWS\System32\combase.dll
0x00007ff8537b0000 - 0x00007ff853890000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff823260000 - 0x00007ff8232a3000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff8523f0000 - 0x00007ff852489000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff84c810000 - 0x00007ff84c81f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff823980000 - 0x00007ff82399f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff854590000 - 0x00007ff854cda000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff852550000 - 0x00007ff8526c4000 	C:\WINDOWS\System32\wintypes.dll
0x00007ff84faf0000 - 0x00007ff85034b000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff8540a0000 - 0x00007ff854195000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff8538a0000 - 0x00007ff85390a000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff851d80000 - 0x00007ff851daf000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff8238b0000 - 0x00007ff8238c8000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff84c850000 - 0x00007ff84c860000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ff84bc80000 - 0x00007ff84bd9e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff8512c0000 - 0x00007ff85132a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff823890000 - 0x00007ff8238a6000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff839c90000 - 0x00007ff839ca0000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ff81e1c0000 - 0x00007ff81e205000 	C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ff853600000 - 0x00007ff85379e000 	C:\WINDOWS\System32\ole32.dll
0x00007ff851570000 - 0x00007ff85158b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff850c90000 - 0x00007ff850ccb000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff851360000 - 0x00007ff85138b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff851d50000 - 0x00007ff851d76000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ff851590000 - 0x00007ff85159c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff8506d0000 - 0x00007ff850703000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff853f10000 - 0x00007ff853f1a000 	C:\WINDOWS\System32\NSI.dll
0x00007ffffd640000 - 0x00007ffffd689000 	C:\Users\<USER>\AppData\Local\Temp\jna-54154080\jna1566400863141209007.dll
0x00007ff853ee0000 - 0x00007ff853ee8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff84c3c0000 - 0x00007ff84c3df000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ff84c390000 - 0x00007ff84c3b5000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3;c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-54154080

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-1c3b119d2dcd847b1270c78e824c1bcf-sock
java_class_path (initial): c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-23
PATH=D:\Ӱ��;C;\Program Files\Java\jdk-23\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\ProgramData\chocolatey\bin;C:\Program Files\nodejs\;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;D:\΢�ſ�;�߹���\΢��web�����߹���\dll;;C:\Program Files\Git\cmd;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;d:\Trae��̹���\Trae CN\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\VSCOD\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Java\jdk-23\bin;C:\Users\<USER>\.android\platform-tools-latest-windows\platform-tools;
USERNAME=91668
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4484)
OS uptime: 0 days 14:32 hours

CPU: total 24 (initial active 24) (12 cores per cpu, 2 threads per core) family 6 model 151 stepping 2 microcode 0x2c, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, serialize, rdtscp, rdpid, fsrm, f16c, pku, cet_ibt, cet_ss
Processor Information for processor 0
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 1
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 2
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 3
  Max Mhz: 2300, Current Mhz: 1506, Mhz Limit: 2300
Processor Information for processor 4
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 5
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 6
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 7
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 8
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 9
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 10
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 11
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 12
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 13
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 14
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 15
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 16
  Max Mhz: 2300, Current Mhz: 1488, Mhz Limit: 2300
Processor Information for processor 17
  Max Mhz: 2300, Current Mhz: 1488, Mhz Limit: 2300
Processor Information for processor 18
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 19
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 20
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 21
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 22
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 23
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300

Memory: 4k page, system-wide physical 16121M (1527M free)
TotalPageFile size 28921M (AvailPageFile size 5M)
current process WorkingSet (physical memory assigned to process): 390M, peak: 392M
current process commit charge ("private bytes"): 476M, peak: 477M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
