#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 2854736 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=29644, tid=32140
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-e2487a37ffc33e95e3346a9155da3121-sock

Host: 12th Gen Intel(R) Core(TM) i9-12900HX, 24 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4484)
Time: Mon Jul 21 22:27:50 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4484) elapsed time: 15.882513 seconds (0d 0h 0m 15s)

---------------  T H R E A D  ---------------

Current thread (0x0000020342e7e380):  JavaThread "C2 CompilerThread4" daemon [_thread_in_native, id=32140, stack(0x000000158ae00000,0x000000158af00000) (1024K)]


Current CompileTask:
C2:15882 8932   !   4       org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer::indexDocument (1168 bytes)

Stack: [0x000000158ae00000,0x000000158af00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0x3b692c]
V  [jvm.dll+0x1e0029]
V  [jvm.dll+0x247c42]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002034353a1d0, length=64, elements={
0x000002035e96ef00, 0x0000020376a93ad0, 0x0000020376a94980, 0x0000020376a9db30,
0x0000020376a9e580, 0x0000020376a9fba0, 0x0000020376ac2770, 0x0000020376acde60,
0x0000020376acff70, 0x0000020376c21a90, 0x000002037918ce70, 0x000002037e3542d0,
0x000002037e328e80, 0x0000020379668c90, 0x000002037965d980, 0x000002037e6ffc40,
0x000002037e80c2d0, 0x000002037e9a6820, 0x000002037e5410f0, 0x00000203794f53c0,
0x00000203794f60e0, 0x00000203794f7490, 0x00000203794f7b20, 0x00000203794f46a0,
0x00000203794f4d30, 0x000002037e76f860, 0x000002037e771930, 0x000002037e76eb40,
0x000002037e771fc0, 0x000002037e76fef0, 0x000002037e7712a0, 0x000002037e772650,
0x000002037e76f1d0, 0x000002037e770c10, 0x000002037e773370, 0x000002037e772ce0,
0x000002037e773a00, 0x000002037e770580, 0x000002037e774db0, 0x000002037e774090,
0x000002037e775440, 0x000002037e775ad0, 0x000002037e774720, 0x000002037e776160,
0x0000020341e4a470, 0x0000020341e483a0, 0x0000020341e47d10, 0x0000020341e490c0,
0x0000020341e49750, 0x0000020341e48a30, 0x0000020341e4d260, 0x0000020341e4c540,
0x0000020341e4b820, 0x0000020341e4beb0, 0x0000020341e49de0, 0x0000020341e4cbd0,
0x0000020341e4d8f0, 0x0000020342e7ea50, 0x0000020341e4df80, 0x0000020342e7f120,
0x0000020342e7dcb0, 0x0000020342e7e380, 0x00000203794f6e00, 0x0000020341e4ab00
}

Java Threads: ( => current thread )
  0x000002035e96ef00 JavaThread "main"                              [_thread_blocked, id=27800, stack(0x0000001585800000,0x0000001585900000) (1024K)]
  0x0000020376a93ad0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=25132, stack(0x0000001585c00000,0x0000001585d00000) (1024K)]
  0x0000020376a94980 JavaThread "Finalizer"                  daemon [_thread_blocked, id=16004, stack(0x0000001585d00000,0x0000001585e00000) (1024K)]
  0x0000020376a9db30 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=21812, stack(0x0000001585e00000,0x0000001585f00000) (1024K)]
  0x0000020376a9e580 JavaThread "Attach Listener"            daemon [_thread_blocked, id=32516, stack(0x0000001585f00000,0x0000001586000000) (1024K)]
  0x0000020376a9fba0 JavaThread "Service Thread"             daemon [_thread_blocked, id=2584, stack(0x0000001586000000,0x0000001586100000) (1024K)]
  0x0000020376ac2770 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=24196, stack(0x0000001586100000,0x0000001586200000) (1024K)]
  0x0000020376acde60 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=17516, stack(0x0000001586200000,0x0000001586300000) (1024K)]
  0x0000020376acff70 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=21080, stack(0x0000001586300000,0x0000001586400000) (1024K)]
  0x0000020376c21a90 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=3820, stack(0x0000001586500000,0x0000001586600000) (1024K)]
  0x000002037918ce70 JavaThread "Notification Thread"        daemon [_thread_blocked, id=8452, stack(0x0000001586900000,0x0000001586a00000) (1024K)]
  0x000002037e3542d0 JavaThread "Active Thread: Equinox Container: c688271f-35ab-4291-ad69-5619cf60881f"        [_thread_blocked, id=27716, stack(0x0000001587000000,0x0000001587100000) (1024K)]
  0x000002037e328e80 JavaThread "Refresh Thread: Equinox Container: c688271f-35ab-4291-ad69-5619cf60881f" daemon [_thread_blocked, id=30516, stack(0x0000001586800000,0x0000001586900000) (1024K)]
  0x0000020379668c90 JavaThread "Framework Event Dispatcher: Equinox Container: c688271f-35ab-4291-ad69-5619cf60881f" daemon [_thread_blocked, id=17580, stack(0x0000001587200000,0x0000001587300000) (1024K)]
  0x000002037965d980 JavaThread "Start Level: Equinox Container: c688271f-35ab-4291-ad69-5619cf60881f" daemon [_thread_blocked, id=32180, stack(0x0000001587300000,0x0000001587400000) (1024K)]
  0x000002037e6ffc40 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=28320, stack(0x0000001587800000,0x0000001587900000) (1024K)]
  0x000002037e80c2d0 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=25644, stack(0x0000001586400000,0x0000001586500000) (1024K)]
  0x000002037e9a6820 JavaThread "C1 CompilerThread1"         daemon [_thread_blocked, id=28448, stack(0x0000001586600000,0x0000001586700000) (1024K)]
  0x000002037e5410f0 JavaThread "C1 CompilerThread2"         daemon [_thread_blocked, id=14628, stack(0x0000001587900000,0x0000001587a00000) (1024K)]
  0x00000203794f53c0 JavaThread "Worker-JM"                         [_thread_blocked, id=28380, stack(0x0000001587c00000,0x0000001587d00000) (1024K)]
  0x00000203794f60e0 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=30732, stack(0x0000001588000000,0x0000001588100000) (1024K)]
  0x00000203794f7490 JavaThread "Worker-0: Update project My Application5-MyApplication5"        [_thread_blocked, id=12720, stack(0x0000001588200000,0x0000001588300000) (1024K)]
  0x00000203794f7b20 JavaThread "Worker-1"                          [_thread_blocked, id=15400, stack(0x0000001588300000,0x0000001588400000) (1024K)]
  0x00000203794f46a0 JavaThread "Worker-2"                          [_thread_blocked, id=28572, stack(0x0000001588500000,0x0000001588600000) (1024K)]
  0x00000203794f4d30 JavaThread "Java indexing"              daemon [_thread_in_Java, id=13664, stack(0x0000001588800000,0x0000001588900000) (1024K)]
  0x000002037e76f860 JavaThread "Worker-3: Update project app"        [_thread_blocked, id=21948, stack(0x0000001588b00000,0x0000001588c00000) (1024K)]
  0x000002037e771930 JavaThread "Worker-4"                          [_thread_blocked, id=15968, stack(0x0000001588c00000,0x0000001588d00000) (1024K)]
  0x000002037e76eb40 JavaThread "Thread-2"                   daemon [_thread_in_native, id=24528, stack(0x0000001588d00000,0x0000001588e00000) (1024K)]
  0x000002037e771fc0 JavaThread "Thread-3"                   daemon [_thread_in_native, id=20980, stack(0x0000001588e00000,0x0000001588f00000) (1024K)]
  0x000002037e76fef0 JavaThread "Thread-4"                   daemon [_thread_in_native, id=2616, stack(0x0000001588f00000,0x0000001589000000) (1024K)]
  0x000002037e7712a0 JavaThread "Thread-5"                   daemon [_thread_in_native, id=27232, stack(0x0000001589000000,0x0000001589100000) (1024K)]
  0x000002037e772650 JavaThread "Thread-6"                   daemon [_thread_in_native, id=29236, stack(0x0000001589100000,0x0000001589200000) (1024K)]
  0x000002037e76f1d0 JavaThread "Thread-7"                   daemon [_thread_in_native, id=28560, stack(0x0000001589200000,0x0000001589300000) (1024K)]
  0x000002037e770c10 JavaThread "Thread-8"                   daemon [_thread_in_native, id=20992, stack(0x0000001589300000,0x0000001589400000) (1024K)]
  0x000002037e773370 JavaThread "Thread-9"                   daemon [_thread_in_native, id=23048, stack(0x0000001589400000,0x0000001589500000) (1024K)]
  0x000002037e772ce0 JavaThread "Thread-10"                  daemon [_thread_in_native, id=27736, stack(0x0000001589500000,0x0000001589600000) (1024K)]
  0x000002037e773a00 JavaThread "Thread-11"                  daemon [_thread_in_native, id=27712, stack(0x0000001589600000,0x0000001589700000) (1024K)]
  0x000002037e770580 JavaThread "Thread-12"                  daemon [_thread_in_native, id=27708, stack(0x0000001589700000,0x0000001589800000) (1024K)]
  0x000002037e774db0 JavaThread "Thread-13"                  daemon [_thread_in_native, id=8416, stack(0x0000001589800000,0x0000001589900000) (1024K)]
  0x000002037e774090 JavaThread "Thread-14"                  daemon [_thread_in_native, id=15720, stack(0x0000001589900000,0x0000001589a00000) (1024K)]
  0x000002037e775440 JavaThread "Thread-15"                  daemon [_thread_in_native, id=11232, stack(0x0000001589a00000,0x0000001589b00000) (1024K)]
  0x000002037e775ad0 JavaThread "Thread-16"                  daemon [_thread_in_native, id=30200, stack(0x0000001589b00000,0x0000001589c00000) (1024K)]
  0x000002037e774720 JavaThread "Thread-17"                  daemon [_thread_in_native, id=22608, stack(0x0000001589c00000,0x0000001589d00000) (1024K)]
  0x000002037e776160 JavaThread "Thread-18"                  daemon [_thread_in_native, id=22624, stack(0x0000001589d00000,0x0000001589e00000) (1024K)]
  0x0000020341e4a470 JavaThread "Thread-19"                  daemon [_thread_in_native, id=23504, stack(0x0000001589e00000,0x0000001589f00000) (1024K)]
  0x0000020341e483a0 JavaThread "Thread-20"                  daemon [_thread_in_native, id=1692, stack(0x0000001589f00000,0x000000158a000000) (1024K)]
  0x0000020341e47d10 JavaThread "Thread-21"                  daemon [_thread_in_native, id=21204, stack(0x000000158a000000,0x000000158a100000) (1024K)]
  0x0000020341e490c0 JavaThread "Thread-22"                  daemon [_thread_in_native, id=21608, stack(0x000000158a100000,0x000000158a200000) (1024K)]
  0x0000020341e49750 JavaThread "Thread-23"                  daemon [_thread_in_native, id=7608, stack(0x000000158a200000,0x000000158a300000) (1024K)]
  0x0000020341e48a30 JavaThread "Thread-24"                  daemon [_thread_in_native, id=32612, stack(0x000000158a300000,0x000000158a400000) (1024K)]
  0x0000020341e4d260 JavaThread "Thread-25"                  daemon [_thread_in_native, id=26912, stack(0x000000158a400000,0x000000158a500000) (1024K)]
  0x0000020341e4c540 JavaThread "Thread-26"                  daemon [_thread_in_native, id=9128, stack(0x000000158a500000,0x000000158a600000) (1024K)]
  0x0000020341e4b820 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=22648, stack(0x000000158a600000,0x000000158a700000) (1024K)]
  0x0000020341e4beb0 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=18416, stack(0x000000158a700000,0x000000158a800000) (1024K)]
  0x0000020341e49de0 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=18364, stack(0x000000158a800000,0x000000158a900000) (1024K)]
  0x0000020341e4cbd0 JavaThread "Worker-5: Java indexing... "        [_thread_blocked, id=26528, stack(0x000000158a900000,0x000000158aa00000) (1024K)]
  0x0000020341e4d8f0 JavaThread "Worker-6: Initialize workspace"        [_thread_blocked, id=23984, stack(0x000000158aa00000,0x000000158ab00000) (1024K)]
  0x0000020342e7ea50 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=25160, stack(0x0000001587b00000,0x0000001587c00000) (1024K)]
  0x0000020341e4df80 JavaThread "Timer-0"                           [_thread_blocked, id=30308, stack(0x0000001588100000,0x0000001588200000) (1024K)]
  0x0000020342e7f120 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=28096, stack(0x000000158ac00000,0x000000158ad00000) (1024K)]
  0x0000020342e7dcb0 JavaThread "C2 CompilerThread3"         daemon [_thread_blocked, id=7860, stack(0x000000158ad00000,0x000000158ae00000) (1024K)]
=>0x0000020342e7e380 JavaThread "C2 CompilerThread4"         daemon [_thread_in_native, id=32140, stack(0x000000158ae00000,0x000000158af00000) (1024K)]
  0x00000203794f6e00 JavaThread "Timer-1"                           [_thread_blocked, id=21200, stack(0x000000158ab00000,0x000000158ac00000) (1024K)]
  0x0000020341e4ab00 JavaThread "Connection worker"                 [_thread_in_native, id=30944, stack(0x000000158af00000,0x000000158b000000) (1024K)]
Total: 64

Other Threads:
  0x000002035ea2ec00 VMThread "VM Thread"                           [id=28036, stack(0x0000001585b00000,0x0000001585c00000) (1024K)]
  0x00000203752eca00 WatcherThread "VM Periodic Task Thread"        [id=32568, stack(0x0000001585a00000,0x0000001585b00000) (1024K)]
  0x000002035e98c200 WorkerThread "GC Thread#0"                     [id=25336, stack(0x0000001585900000,0x0000001585a00000) (1024K)]
  0x0000020379489bd0 WorkerThread "GC Thread#1"                     [id=31508, stack(0x0000001586a00000,0x0000001586b00000) (1024K)]
  0x0000020379486020 WorkerThread "GC Thread#2"                     [id=19628, stack(0x0000001586b00000,0x0000001586c00000) (1024K)]
  0x0000020379502690 WorkerThread "GC Thread#3"                     [id=12300, stack(0x0000001586c00000,0x0000001586d00000) (1024K)]
  0x0000020379502a30 WorkerThread "GC Thread#4"                     [id=32376, stack(0x0000001586d00000,0x0000001586e00000) (1024K)]
  0x0000020379502dd0 WorkerThread "GC Thread#5"                     [id=23736, stack(0x0000001586e00000,0x0000001586f00000) (1024K)]
  0x0000020379503570 WorkerThread "GC Thread#6"                     [id=1756, stack(0x0000001586f00000,0x0000001587000000) (1024K)]
  0x0000020379757090 WorkerThread "GC Thread#7"                     [id=23820, stack(0x0000001587100000,0x0000001587200000) (1024K)]
  0x000002037e3146a0 WorkerThread "GC Thread#8"                     [id=6584, stack(0x0000001587500000,0x0000001587600000) (1024K)]
  0x000002037e2f6f10 WorkerThread "GC Thread#9"                     [id=29936, stack(0x0000001587600000,0x0000001587700000) (1024K)]
  0x000002037e2f5950 WorkerThread "GC Thread#10"                    [id=11636, stack(0x0000001587700000,0x0000001587800000) (1024K)]
  0x000002037e2f5cf0 WorkerThread "GC Thread#11"                    [id=19384, stack(0x0000001587400000,0x0000001587500000) (1024K)]
  0x000002037e2f67d0 WorkerThread "GC Thread#12"                    [id=22304, stack(0x0000001587d00000,0x0000001587e00000) (1024K)]
  0x000002037e2f6b70 WorkerThread "GC Thread#13"                    [id=2316, stack(0x0000001587e00000,0x0000001587f00000) (1024K)]
  0x000002037e2b5940 WorkerThread "GC Thread#14"                    [id=22960, stack(0x0000001587f00000,0x0000001588000000) (1024K)]
  0x000002037e2b5ce0 WorkerThread "GC Thread#15"                    [id=2720, stack(0x0000001588400000,0x0000001588500000) (1024K)]
  0x000002037e2b7d80 WorkerThread "GC Thread#16"                    [id=28772, stack(0x0000001588600000,0x0000001588700000) (1024K)]
  0x000002037e2b6080 WorkerThread "GC Thread#17"                    [id=31580, stack(0x0000001588700000,0x0000001588800000) (1024K)]
Total: 20

Threads with active compile tasks:
C2 CompilerThread4  15927 8932   !   4       org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer::indexDocument (1168 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000020300000000-0x0000020300ba0000-0x0000020300ba0000), size 12189696, SharedBaseAddress: 0x0000020300000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000020301000000-0x0000020341000000, reserved size: 1073741824
Narrow klass base: 0x0000020300000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 24 total, 24 available
 Memory: 16121M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 18

Heap:
 PSYoungGen      total 5120K, used 4703K [0x00000000d5580000, 0x00000000d5c00000, 0x0000000100000000)
  eden space 4096K, 94% used [0x00000000d5580000,0x00000000d5943ab0,0x00000000d5980000)
  from space 1024K, 82% used [0x00000000d5b00000,0x00000000d5bd4160,0x00000000d5c00000)
  to   space 1024K, 0% used [0x00000000d5a00000,0x00000000d5a00000,0x00000000d5b00000)
 ParOldGen       total 159232K, used 158864K [0x0000000080000000, 0x0000000089b80000, 0x00000000d5580000)
  object space 159232K, 99% used [0x0000000080000000,0x0000000089b24078,0x0000000089b80000)
 Metaspace       used 61530K, committed 62912K, reserved 1114112K
  class space    used 7077K, committed 7680K, reserved 1048576K

Card table byte_map: [0x000002035e330000,0x000002035e740000] _byte_map_base: 0x000002035df30000

Marking Bits: (ParMarkBitMap*) 0x00007fffbf2531f0
 Begin Bits: [0x0000020370e10000, 0x0000020372e10000)
 End Bits:   [0x0000020372e10000, 0x0000020374e10000)

Polling page: 0x000002035e0a0000

Metaspace:

Usage:
  Non-class:     53.18 MB used.
      Class:      6.91 MB used.
       Both:     60.09 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      53.94 MB ( 84%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       7.50 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      61.44 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  9.47 MB
       Class:  8.44 MB
        Both:  17.91 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 97.31 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 1020.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 983.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 23.
num_chunks_taken_from_freelist: 3803.
num_chunk_merges: 15.
num_chunk_splits: 2403.
num_chunks_enlarged: 1434.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=6339Kb max_used=6339Kb free=112828Kb
 bounds [0x0000020369700000, 0x0000020369d40000, 0x0000020370b60000]
CodeHeap 'profiled nmethods': size=119104Kb used=17109Kb max_used=17395Kb free=101994Kb
 bounds [0x0000020361b60000, 0x0000020362c70000, 0x0000020368fb0000]
CodeHeap 'non-nmethods': size=7488Kb used=2535Kb max_used=3199Kb free=4952Kb
 bounds [0x0000020368fb0000, 0x00000203692e0000, 0x0000020369700000]
 total_blobs=9052 nmethods=8334 adapters=622
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 14.639 Thread 0x0000020376acff70 nmethod 9180 0x00000203626f7510 code [0x00000203626f7740, 0x00000203626f8038]
Event: 14.674 Thread 0x0000020376acde60 9181       4       org.eclipse.jdt.internal.compiler.util.Util::scanClassTypeSignature (109 bytes)
Event: 14.678 Thread 0x0000020376acde60 nmethod 9181 0x0000020369d21410 code [0x0000020369d215e0, 0x0000020369d219c0]
Event: 14.679 Thread 0x0000020376acde60 9182       4       org.objectweb.asm.ClassReader::readInt (52 bytes)
Event: 14.679 Thread 0x0000020376acde60 nmethod 9182 0x0000020369d21c90 code [0x0000020369d21e20, 0x0000020369d21f40]
Event: 14.681 Thread 0x0000020376acde60 9183       4       org.eclipse.jdt.core.Signature::appendTypeSignature (513 bytes)
Event: 14.682 Thread 0x0000020376acde60 nmethod 9183 0x0000020369d22010 code [0x0000020369d221c0, 0x0000020369d22328]
Event: 14.685 Thread 0x0000020376acde60 9184       4       java.lang.ref.ReferenceQueue::enqueue0 (98 bytes)
Event: 14.688 Thread 0x0000020376acde60 nmethod 9184 0x0000020369d22490 code [0x0000020369d22680, 0x0000020369d22988]
Event: 14.708 Thread 0x0000020376acde60 9185       4       org.eclipse.jdt.core.Signature::checkArrayDimension (92 bytes)
Event: 14.709 Thread 0x0000020342e7f120 9186       4       org.eclipse.jdt.core.Signature::encodeQualifiedName (207 bytes)
Event: 14.712 Thread 0x0000020376acde60 nmethod 9185 0x0000020369d22c10 code [0x0000020369d22da0, 0x0000020369d23008]
Event: 14.712 Thread 0x0000020342e7dcb0 9187 %     4       org.objectweb.asm.ClassReader::<init> @ 95 (359 bytes)
Event: 14.718 Thread 0x0000020342e80590 nmethod 9160 0x0000020369d23110 code [0x0000020369d238e0, 0x0000020369d29210]
Event: 14.719 Thread 0x0000020342e7dcb0 nmethod 9187% 0x0000020369d2cc90 code [0x0000020369d2ce60, 0x0000020369d2d380]
Event: 14.719 Thread 0x0000020342e7f120 nmethod 9186 0x0000020369d2d690 code [0x0000020369d2d8e0, 0x0000020369d2e078]
Event: 14.737 Thread 0x0000020342e7f120 9188       4       jdk.internal.jrtfs.JrtFileSystem::checkNode (40 bytes)
Event: 14.755 Thread 0x0000020342e7f120 nmethod 9188 0x0000020369d2e710 code [0x0000020369d2e9a0, 0x0000020369d2f758]
Event: 14.763 Thread 0x0000020342e7f120 9189       4       org.objectweb.asm.ClassReader::<init> (359 bytes)
Event: 14.771 Thread 0x0000020342e7f120 nmethod 9189 0x0000020369d30210 code [0x0000020369d303e0, 0x0000020369d30b08]

GC Heap History (20 events):
Event: 14.685 GC heap after
{Heap after GC invocations=168 (full 3):
 PSYoungGen      total 4096K, used 982K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5880000)
  from space 1024K, 95% used [0x00000000d5880000,0x00000000d59758b0,0x00000000d5980000)
  to   space 1024K, 0% used [0x00000000d5980000,0x00000000d5980000,0x00000000d5a80000)
 ParOldGen       total 151040K, used 150824K [0x0000000080000000, 0x0000000089380000, 0x00000000d5580000)
  object space 151040K, 99% used [0x0000000080000000,0x000000008934a178,0x0000000089380000)
 Metaspace       used 60713K, committed 62144K, reserved 1114112K
  class space    used 6914K, committed 7552K, reserved 1048576K
}
Event: 14.695 GC heap before
{Heap before GC invocations=169 (full 3):
 PSYoungGen      total 4096K, used 4054K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000d5580000,0x00000000d5880000,0x00000000d5880000)
  from space 1024K, 95% used [0x00000000d5880000,0x00000000d59758b0,0x00000000d5980000)
  to   space 1024K, 0% used [0x00000000d5980000,0x00000000d5980000,0x00000000d5a80000)
 ParOldGen       total 151040K, used 150824K [0x0000000080000000, 0x0000000089380000, 0x00000000d5580000)
  object space 151040K, 99% used [0x0000000080000000,0x000000008934a178,0x0000000089380000)
 Metaspace       used 60768K, committed 62144K, reserved 1114112K
  class space    used 6928K, committed 7552K, reserved 1048576K
}
Event: 14.696 GC heap after
{Heap after GC invocations=169 (full 3):
 PSYoungGen      total 4096K, used 800K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5880000)
  from space 1024K, 78% used [0x00000000d5980000,0x00000000d5a48000,0x00000000d5a80000)
  to   space 1024K, 0% used [0x00000000d5880000,0x00000000d5880000,0x00000000d5980000)
 ParOldGen       total 151552K, used 151533K [0x0000000080000000, 0x0000000089400000, 0x00000000d5580000)
  object space 151552K, 99% used [0x0000000080000000,0x00000000893fb648,0x0000000089400000)
 Metaspace       used 60768K, committed 62144K, reserved 1114112K
  class space    used 6928K, committed 7552K, reserved 1048576K
}
Event: 14.703 GC heap before
{Heap before GC invocations=170 (full 3):
 PSYoungGen      total 4096K, used 3872K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000d5580000,0x00000000d5880000,0x00000000d5880000)
  from space 1024K, 78% used [0x00000000d5980000,0x00000000d5a48000,0x00000000d5a80000)
  to   space 1024K, 0% used [0x00000000d5880000,0x00000000d5880000,0x00000000d5980000)
 ParOldGen       total 151552K, used 151533K [0x0000000080000000, 0x0000000089400000, 0x00000000d5580000)
  object space 151552K, 99% used [0x0000000080000000,0x00000000893fb648,0x0000000089400000)
 Metaspace       used 60831K, committed 62208K, reserved 1114112K
  class space    used 6939K, committed 7552K, reserved 1048576K
}
Event: 14.704 GC heap after
{Heap after GC invocations=170 (full 3):
 PSYoungGen      total 4096K, used 880K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5880000)
  from space 1024K, 86% used [0x00000000d5880000,0x00000000d595c370,0x00000000d5980000)
  to   space 1024K, 0% used [0x00000000d5980000,0x00000000d5980000,0x00000000d5a80000)
 ParOldGen       total 152576K, used 152087K [0x0000000080000000, 0x0000000089500000, 0x00000000d5580000)
  object space 152576K, 99% used [0x0000000080000000,0x0000000089485ef8,0x0000000089500000)
 Metaspace       used 60831K, committed 62208K, reserved 1114112K
  class space    used 6939K, committed 7552K, reserved 1048576K
}
Event: 14.715 GC heap before
{Heap before GC invocations=171 (full 3):
 PSYoungGen      total 4096K, used 3952K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000d5580000,0x00000000d5880000,0x00000000d5880000)
  from space 1024K, 86% used [0x00000000d5880000,0x00000000d595c370,0x00000000d5980000)
  to   space 1024K, 0% used [0x00000000d5980000,0x00000000d5980000,0x00000000d5a80000)
 ParOldGen       total 152576K, used 152087K [0x0000000080000000, 0x0000000089500000, 0x00000000d5580000)
  object space 152576K, 99% used [0x0000000080000000,0x0000000089485ef8,0x0000000089500000)
 Metaspace       used 60871K, committed 62272K, reserved 1114112K
  class space    used 6947K, committed 7552K, reserved 1048576K
}
Event: 14.716 GC heap after
{Heap after GC invocations=171 (full 3):
 PSYoungGen      total 4096K, used 806K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5880000)
  from space 1024K, 78% used [0x00000000d5980000,0x00000000d5a49968,0x00000000d5a80000)
  to   space 1024K, 0% used [0x00000000d5880000,0x00000000d5880000,0x00000000d5980000)
 ParOldGen       total 153088K, used 152708K [0x0000000080000000, 0x0000000089580000, 0x00000000d5580000)
  object space 153088K, 99% used [0x0000000080000000,0x0000000089521148,0x0000000089580000)
 Metaspace       used 60871K, committed 62272K, reserved 1114112K
  class space    used 6947K, committed 7552K, reserved 1048576K
}
Event: 14.726 GC heap before
{Heap before GC invocations=172 (full 3):
 PSYoungGen      total 4096K, used 3878K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000d5580000,0x00000000d5880000,0x00000000d5880000)
  from space 1024K, 78% used [0x00000000d5980000,0x00000000d5a49968,0x00000000d5a80000)
  to   space 1024K, 0% used [0x00000000d5880000,0x00000000d5880000,0x00000000d5980000)
 ParOldGen       total 153088K, used 152708K [0x0000000080000000, 0x0000000089580000, 0x00000000d5580000)
  object space 153088K, 99% used [0x0000000080000000,0x0000000089521148,0x0000000089580000)
 Metaspace       used 60913K, committed 62272K, reserved 1114112K
  class space    used 6955K, committed 7552K, reserved 1048576K
}
Event: 14.727 GC heap after
{Heap after GC invocations=172 (full 3):
 PSYoungGen      total 4096K, used 925K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5880000)
  from space 1024K, 90% used [0x00000000d5880000,0x00000000d5967700,0x00000000d5980000)
  to   space 1024K, 0% used [0x00000000d5980000,0x00000000d5980000,0x00000000d5a80000)
 ParOldGen       total 153600K, used 153336K [0x0000000080000000, 0x0000000089600000, 0x00000000d5580000)
  object space 153600K, 99% used [0x0000000080000000,0x00000000895be248,0x0000000089600000)
 Metaspace       used 60913K, committed 62272K, reserved 1114112K
  class space    used 6955K, committed 7552K, reserved 1048576K
}
Event: 14.733 GC heap before
{Heap before GC invocations=173 (full 3):
 PSYoungGen      total 4096K, used 3997K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000d5580000,0x00000000d5880000,0x00000000d5880000)
  from space 1024K, 90% used [0x00000000d5880000,0x00000000d5967700,0x00000000d5980000)
  to   space 1024K, 0% used [0x00000000d5980000,0x00000000d5980000,0x00000000d5a80000)
 ParOldGen       total 153600K, used 153336K [0x0000000080000000, 0x0000000089600000, 0x00000000d5580000)
  object space 153600K, 99% used [0x0000000080000000,0x00000000895be248,0x0000000089600000)
 Metaspace       used 60982K, committed 62400K, reserved 1114112K
  class space    used 6966K, committed 7616K, reserved 1048576K
}
Event: 14.734 GC heap after
{Heap after GC invocations=173 (full 3):
 PSYoungGen      total 4096K, used 930K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5880000)
  from space 1024K, 90% used [0x00000000d5980000,0x00000000d5a68908,0x00000000d5a80000)
  to   space 1024K, 0% used [0x00000000d5880000,0x00000000d5880000,0x00000000d5980000)
 ParOldGen       total 154112K, used 153988K [0x0000000080000000, 0x0000000089680000, 0x00000000d5580000)
  object space 154112K, 99% used [0x0000000080000000,0x0000000089661320,0x0000000089680000)
 Metaspace       used 60982K, committed 62400K, reserved 1114112K
  class space    used 6966K, committed 7616K, reserved 1048576K
}
Event: 14.741 GC heap before
{Heap before GC invocations=174 (full 3):
 PSYoungGen      total 4096K, used 4002K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000d5580000,0x00000000d5880000,0x00000000d5880000)
  from space 1024K, 90% used [0x00000000d5980000,0x00000000d5a68908,0x00000000d5a80000)
  to   space 1024K, 0% used [0x00000000d5880000,0x00000000d5880000,0x00000000d5980000)
 ParOldGen       total 154112K, used 153988K [0x0000000080000000, 0x0000000089680000, 0x00000000d5580000)
  object space 154112K, 99% used [0x0000000080000000,0x0000000089661320,0x0000000089680000)
 Metaspace       used 61054K, committed 62464K, reserved 1114112K
  class space    used 6981K, committed 7616K, reserved 1048576K
}
Event: 14.742 GC heap after
{Heap after GC invocations=174 (full 3):
 PSYoungGen      total 4096K, used 945K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5880000)
  from space 1024K, 92% used [0x00000000d5880000,0x00000000d596c408,0x00000000d5980000)
  to   space 1024K, 0% used [0x00000000d5980000,0x00000000d5980000,0x00000000d5a80000)
 ParOldGen       total 154624K, used 154575K [0x0000000080000000, 0x0000000089700000, 0x00000000d5580000)
  object space 154624K, 99% used [0x0000000080000000,0x00000000896f3c28,0x0000000089700000)
 Metaspace       used 61054K, committed 62464K, reserved 1114112K
  class space    used 6981K, committed 7616K, reserved 1048576K
}
Event: 14.750 GC heap before
{Heap before GC invocations=175 (full 3):
 PSYoungGen      total 4096K, used 4017K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000d5580000,0x00000000d5880000,0x00000000d5880000)
  from space 1024K, 92% used [0x00000000d5880000,0x00000000d596c408,0x00000000d5980000)
  to   space 1024K, 0% used [0x00000000d5980000,0x00000000d5980000,0x00000000d5a80000)
 ParOldGen       total 154624K, used 154575K [0x0000000080000000, 0x0000000089700000, 0x00000000d5580000)
  object space 154624K, 99% used [0x0000000080000000,0x00000000896f3c28,0x0000000089700000)
 Metaspace       used 61127K, committed 62528K, reserved 1114112K
  class space    used 7000K, committed 7616K, reserved 1048576K
}
Event: 14.751 GC heap after
{Heap after GC invocations=175 (full 3):
 PSYoungGen      total 4096K, used 787K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5880000)
  from space 1024K, 76% used [0x00000000d5980000,0x00000000d5a44fc0,0x00000000d5a80000)
  to   space 1024K, 0% used [0x00000000d5880000,0x00000000d5880000,0x00000000d5980000)
 ParOldGen       total 155648K, used 155149K [0x0000000080000000, 0x0000000089800000, 0x00000000d5580000)
  object space 155648K, 99% used [0x0000000080000000,0x0000000089783480,0x0000000089800000)
 Metaspace       used 61127K, committed 62528K, reserved 1114112K
  class space    used 7000K, committed 7616K, reserved 1048576K
}
Event: 14.759 GC heap before
{Heap before GC invocations=176 (full 3):
 PSYoungGen      total 4096K, used 3859K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000d5580000,0x00000000d5880000,0x00000000d5880000)
  from space 1024K, 76% used [0x00000000d5980000,0x00000000d5a44fc0,0x00000000d5a80000)
  to   space 1024K, 0% used [0x00000000d5880000,0x00000000d5880000,0x00000000d5980000)
 ParOldGen       total 155648K, used 155149K [0x0000000080000000, 0x0000000089800000, 0x00000000d5580000)
  object space 155648K, 99% used [0x0000000080000000,0x0000000089783480,0x0000000089800000)
 Metaspace       used 61192K, committed 62592K, reserved 1114112K
  class space    used 7008K, committed 7616K, reserved 1048576K
}
Event: 14.760 GC heap after
{Heap after GC invocations=176 (full 3):
 PSYoungGen      total 4096K, used 833K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5880000)
  from space 1024K, 81% used [0x00000000d5880000,0x00000000d5950430,0x00000000d5980000)
  to   space 1024K, 0% used [0x00000000d5980000,0x00000000d5980000,0x00000000d5a80000)
 ParOldGen       total 156160K, used 155743K [0x0000000080000000, 0x0000000089880000, 0x00000000d5580000)
  object space 156160K, 99% used [0x0000000080000000,0x0000000089817e68,0x0000000089880000)
 Metaspace       used 61192K, committed 62592K, reserved 1114112K
  class space    used 7008K, committed 7616K, reserved 1048576K
}
Event: 14.768 GC heap before
{Heap before GC invocations=177 (full 3):
 PSYoungGen      total 4096K, used 3905K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000d5580000,0x00000000d5880000,0x00000000d5880000)
  from space 1024K, 81% used [0x00000000d5880000,0x00000000d5950430,0x00000000d5980000)
  to   space 1024K, 0% used [0x00000000d5980000,0x00000000d5980000,0x00000000d5a80000)
 ParOldGen       total 156160K, used 155743K [0x0000000080000000, 0x0000000089880000, 0x00000000d5580000)
  object space 156160K, 99% used [0x0000000080000000,0x0000000089817e68,0x0000000089880000)
 Metaspace       used 61280K, committed 62656K, reserved 1114112K
  class space    used 7027K, committed 7616K, reserved 1048576K
}
Event: 14.769 GC heap after
{Heap after GC invocations=177 (full 3):
 PSYoungGen      total 4096K, used 720K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5880000)
  from space 1024K, 70% used [0x00000000d5980000,0x00000000d5a341c0,0x00000000d5a80000)
  to   space 1024K, 0% used [0x00000000d5880000,0x00000000d5880000,0x00000000d5980000)
 ParOldGen       total 156672K, used 156314K [0x0000000080000000, 0x0000000089900000, 0x00000000d5580000)
  object space 156672K, 99% used [0x0000000080000000,0x00000000898a6828,0x0000000089900000)
 Metaspace       used 61280K, committed 62656K, reserved 1114112K
  class space    used 7027K, committed 7616K, reserved 1048576K
}
Event: 14.776 GC heap before
{Heap before GC invocations=178 (full 3):
 PSYoungGen      total 4096K, used 3792K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000d5580000,0x00000000d5880000,0x00000000d5880000)
  from space 1024K, 70% used [0x00000000d5980000,0x00000000d5a341c0,0x00000000d5a80000)
  to   space 1024K, 0% used [0x00000000d5880000,0x00000000d5880000,0x00000000d5980000)
 ParOldGen       total 156672K, used 156314K [0x0000000080000000, 0x0000000089900000, 0x00000000d5580000)
  object space 156672K, 99% used [0x0000000080000000,0x00000000898a6828,0x0000000089900000)
 Metaspace       used 61356K, committed 62784K, reserved 1114112K
  class space    used 7041K, committed 7680K, reserved 1048576K
}

Dll operation events (13 events):
Event: 0.012 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.035 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.091 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.095 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.097 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.099 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.112 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.154 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 1.652 Loaded shared library C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 6.111 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-54154080\jna15900159811661328287.dll
Event: 10.413 Loaded shared library D:\gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
Event: 12.062 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
Event: 12.070 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll

Deoptimization events (20 events):
Event: 13.307 Thread 0x0000020341e4d8f0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000020369cbb438 relative=0x0000000000002c58
Event: 13.307 Thread 0x0000020341e4d8f0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000020369cbb438 method=org.eclipse.jdt.internal.compiler.util.SoftClassCache$ClassBytes.getBytes()[B @ 41 c2
Event: 13.308 Thread 0x0000020341e4d8f0 DEOPT PACKING pc=0x0000020369cbb438 sp=0x000000158aafe150
Event: 13.308 Thread 0x0000020341e4d8f0 DEOPT UNPACKING pc=0x0000020369003aa2 sp=0x000000158aafe030 mode 2
Event: 13.308 Thread 0x0000020341e4d8f0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000020369cc2310 relative=0x00000000000033f0
Event: 13.308 Thread 0x0000020341e4d8f0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000020369cc2310 method=org.eclipse.jdt.internal.compiler.util.SoftClassCache$ClassBytes.getBytes()[B @ 41 c2
Event: 13.308 Thread 0x0000020341e4d8f0 DEOPT PACKING pc=0x0000020369cc2310 sp=0x000000158aafe040
Event: 13.308 Thread 0x0000020341e4d8f0 DEOPT UNPACKING pc=0x0000020369003aa2 sp=0x000000158aafe068 mode 2
Event: 13.347 Thread 0x00000203794f4d30 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000020369ca7d70 relative=0x00000000000004f0
Event: 13.347 Thread 0x00000203794f4d30 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000020369ca7d70 method=org.eclipse.jdt.internal.compiler.parser.Scanner.internalScanIdentifierOrKeyword(II[C)Lorg/eclipse/jdt/internal/compiler/parser/TerminalToken; @ 3502 c2
Event: 13.347 Thread 0x00000203794f4d30 DEOPT PACKING pc=0x0000020369ca7d70 sp=0x00000015888fea20
Event: 13.347 Thread 0x00000203794f4d30 DEOPT UNPACKING pc=0x0000020369003aa2 sp=0x00000015888fe9b8 mode 2
Event: 13.355 Thread 0x00000203794f4d30 DEOPT PACKING pc=0x0000020361fc7cc0 sp=0x00000015888fe7e0
Event: 13.355 Thread 0x00000203794f4d30 DEOPT UNPACKING pc=0x0000020369004242 sp=0x00000015888fdde8 mode 0
Event: 13.438 Thread 0x00000203794f4d30 DEOPT PACKING pc=0x0000020361fc7cc0 sp=0x00000015888fe7e0
Event: 13.438 Thread 0x00000203794f4d30 DEOPT UNPACKING pc=0x0000020369004242 sp=0x00000015888fdde8 mode 0
Event: 13.532 Thread 0x00000203794f4d30 DEOPT PACKING pc=0x0000020361fc7cc0 sp=0x00000015888fe7e0
Event: 13.532 Thread 0x00000203794f4d30 DEOPT UNPACKING pc=0x0000020369004242 sp=0x00000015888fdde8 mode 0
Event: 14.763 Thread 0x0000020341e4ab00 DEOPT PACKING pc=0x00000203626447b3 sp=0x000000158aff8eb0
Event: 14.763 Thread 0x0000020341e4ab00 DEOPT UNPACKING pc=0x0000020369004242 sp=0x000000158aff83c0 mode 0

Classes loaded (20 events):
Event: 13.261 Loading class java/util/stream/DistinctOps$1$2
Event: 13.261 Loading class java/util/stream/DistinctOps$1$2 done
Event: 13.288 Loading class java/nio/charset/CharacterCodingException
Event: 13.288 Loading class java/nio/charset/CharacterCodingException done
Event: 13.291 Loading class java/io/ObjectOutputStream$BlockDataOutputStream
Event: 13.292 Loading class java/io/ObjectOutputStream$BlockDataOutputStream done
Event: 13.292 Loading class java/io/ObjectOutputStream$HandleTable
Event: 13.292 Loading class java/io/ObjectOutputStream$HandleTable done
Event: 13.292 Loading class java/io/ObjectOutputStream$ReplaceTable
Event: 13.292 Loading class java/io/ObjectOutputStream$ReplaceTable done
Event: 13.423 Loading class java/util/PriorityQueue
Event: 13.423 Loading class java/util/PriorityQueue done
Event: 13.500 Loading class java/util/OptionalInt
Event: 13.501 Loading class java/util/OptionalInt done
Event: 13.501 Loading class java/util/OptionalLong
Event: 13.501 Loading class java/util/OptionalLong done
Event: 13.501 Loading class java/util/OptionalDouble
Event: 13.501 Loading class java/util/OptionalDouble done
Event: 14.703 Loading class java/util/function/ObjIntConsumer
Event: 14.704 Loading class java/util/function/ObjIntConsumer done

Classes unloaded (7 events):
Event: 6.391 Thread 0x000002035ea2ec00 Unloading class 0x00000203011a2c00 'java/lang/invoke/LambdaForm$MH+0x00000203011a2c00'
Event: 6.391 Thread 0x000002035ea2ec00 Unloading class 0x00000203011a2800 'java/lang/invoke/LambdaForm$MH+0x00000203011a2800'
Event: 6.391 Thread 0x000002035ea2ec00 Unloading class 0x00000203011a2400 'java/lang/invoke/LambdaForm$MH+0x00000203011a2400'
Event: 6.391 Thread 0x000002035ea2ec00 Unloading class 0x00000203011a2000 'java/lang/invoke/LambdaForm$MH+0x00000203011a2000'
Event: 6.391 Thread 0x000002035ea2ec00 Unloading class 0x00000203011a1c00 'java/lang/invoke/LambdaForm$BMH+0x00000203011a1c00'
Event: 6.391 Thread 0x000002035ea2ec00 Unloading class 0x00000203011a1800 'java/lang/invoke/LambdaForm$DMH+0x00000203011a1800'
Event: 6.391 Thread 0x000002035ea2ec00 Unloading class 0x00000203011a0800 'java/lang/invoke/LambdaForm$DMH+0x00000203011a0800'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 13.219 Thread 0x0000020341e4d8f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5780278}> (0x00000000d5780278) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.229 Thread 0x0000020341e4d8f0 Exception <a 'java/io/FileNotFoundException'{0x00000000d55fa188}> (0x00000000d55fa188) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.230 Thread 0x0000020341e4d8f0 Exception <a 'java/io/FileNotFoundException'{0x00000000d55fb110}> (0x00000000d55fb110) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.248 Thread 0x0000020341e4d8f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d56e1cf8}> (0x00000000d56e1cf8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.249 Thread 0x0000020341e4d8f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d56e2328}> (0x00000000d56e2328) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.262 Thread 0x0000020341e4d8f0 Implicit null exception at 0x0000020369c68ad7 to 0x0000020369c6a8ac
Event: 13.268 Thread 0x0000020341e4d8f0 Implicit null exception at 0x0000020369c6574c to 0x0000020369c6706c
Event: 13.305 Thread 0x0000020341e4d8f0 Implicit null exception at 0x0000020369c62d06 to 0x0000020369c644dc
Event: 13.314 Thread 0x0000020341e4d8f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d55e6530}> (0x00000000d55e6530) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.314 Thread 0x0000020341e4d8f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d56127c8}> (0x00000000d56127c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.315 Thread 0x0000020341e4d8f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5613260}> (0x00000000d5613260) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.315 Thread 0x0000020341e4d8f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d565f560}> (0x00000000d565f560) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.316 Thread 0x0000020341e4d8f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d56b3c28}> (0x00000000d56b3c28) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.316 Thread 0x0000020341e4d8f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d56b4258}> (0x00000000d56b4258) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.325 Thread 0x0000020341e4d8f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d55e4d30}> (0x00000000d55e4d30) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.325 Thread 0x0000020341e4d8f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d560cbe0}> (0x00000000d560cbe0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.325 Thread 0x0000020341e4d8f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d560d678}> (0x00000000d560d678) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.325 Thread 0x0000020341e4d8f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d560dca8}> (0x00000000d560dca8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.327 Thread 0x0000020341e4d8f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5630ce8}> (0x00000000d5630ce8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.328 Thread 0x0000020341e4d8f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5696208}> (0x00000000d5696208) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 14.685 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 14.695 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 14.696 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 14.703 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 14.704 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 14.715 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 14.716 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 14.726 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 14.727 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 14.733 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 14.734 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 14.740 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 14.742 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 14.750 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 14.751 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 14.759 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 14.760 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 14.768 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 14.769 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 14.776 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 12.409 Thread 0x000002035ea2ec00 flushing  nmethod 0x0000020362605110
Event: 12.409 Thread 0x000002035ea2ec00 flushing  nmethod 0x000002036260bf90
Event: 12.409 Thread 0x000002035ea2ec00 flushing  nmethod 0x000002036260ca10
Event: 12.409 Thread 0x000002035ea2ec00 flushing osr nmethod 0x0000020362613710
Event: 12.409 Thread 0x000002035ea2ec00 flushing  nmethod 0x000002036261cd10
Event: 12.409 Thread 0x000002035ea2ec00 flushing  nmethod 0x000002036262db90
Event: 12.409 Thread 0x000002035ea2ec00 flushing  nmethod 0x000002036263f010
Event: 12.409 Thread 0x000002035ea2ec00 flushing  nmethod 0x0000020362642810
Event: 12.409 Thread 0x000002035ea2ec00 flushing  nmethod 0x0000020362642f10
Event: 12.409 Thread 0x000002035ea2ec00 flushing  nmethod 0x0000020362643e10
Event: 12.409 Thread 0x000002035ea2ec00 flushing  nmethod 0x0000020362644410
Event: 12.409 Thread 0x000002035ea2ec00 flushing  nmethod 0x0000020362644c90
Event: 12.409 Thread 0x000002035ea2ec00 flushing  nmethod 0x0000020362646790
Event: 12.409 Thread 0x000002035ea2ec00 flushing  nmethod 0x000002036267de90
Event: 12.409 Thread 0x000002035ea2ec00 flushing  nmethod 0x0000020362682f10
Event: 12.409 Thread 0x000002035ea2ec00 flushing  nmethod 0x0000020362694790
Event: 12.409 Thread 0x000002035ea2ec00 flushing  nmethod 0x00000203626e1590
Event: 12.409 Thread 0x000002035ea2ec00 flushing  nmethod 0x00000203626e1a10
Event: 12.409 Thread 0x000002035ea2ec00 flushing  nmethod 0x00000203626e3510
Event: 12.409 Thread 0x000002035ea2ec00 flushing  nmethod 0x00000203626f6d90

Events (20 events):
Event: 6.964 Thread 0x000002035e96ef00 Thread added: 0x0000020341e4c540
Event: 6.984 Thread 0x000002035e96ef00 Thread added: 0x0000020341e4b820
Event: 7.153 Thread 0x000002035e96ef00 Thread added: 0x0000020341e4beb0
Event: 7.154 Thread 0x000002035e96ef00 Thread added: 0x0000020341e49de0
Event: 7.717 Thread 0x000002037e76f860 Thread added: 0x0000020341e4cbd0
Event: 7.717 Thread 0x0000020341e4cbd0 Thread added: 0x0000020341e4d8f0
Event: 8.689 Thread 0x000002037e511280 Thread exited: 0x000002037e511280
Event: 8.786 Thread 0x00000203794fb740 Thread exited: 0x00000203794fb740
Event: 8.793 Thread 0x000002037e5410f0 Thread added: 0x0000020342e7ea50
Event: 9.469 Thread 0x000002037e76f860 Thread added: 0x0000020341e4df80
Event: 9.620 Thread 0x000002037e76f860 Thread added: 0x0000020341e4ab00
Event: 10.033 Thread 0x0000020376acff70 Thread added: 0x0000020342e7f120
Event: 10.543 Thread 0x0000020342e7ea50 Thread added: 0x0000020342e7dcb0
Event: 10.857 Thread 0x0000020342e7dcb0 Thread added: 0x0000020342e7e380
Event: 11.392 Thread 0x00000203794f6e00 Thread exited: 0x00000203794f6e00
Event: 12.250 Thread 0x0000020341e4ab00 Thread exited: 0x0000020341e4ab00
Event: 12.477 Thread 0x000002037e9a6820 Thread added: 0x0000020342e80590
Event: 12.838 Thread 0x00000203794f7490 Thread added: 0x00000203794f6e00
Event: 13.210 Thread 0x00000203794f7490 Thread added: 0x0000020341e4ab00
Event: 14.573 Thread 0x000002037e5427a0 Thread exited: 0x000002037e5427a0


Dynamic libraries:
0x00007ff7daa60000 - 0x00007ff7daa6e000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff854d20000 - 0x00007ff854f88000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff853e10000 - 0x00007ff853ed9000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff8526d0000 - 0x00007ff852abd000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff8522a0000 - 0x00007ff8523eb000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff82fb20000 - 0x00007ff82fb38000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff8331a0000 - 0x00007ff8331be000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff853430000 - 0x00007ff8535fc000 	C:\WINDOWS\System32\USER32.dll
0x00007ff852040000 - 0x00007ff852067000 	C:\WINDOWS\System32\win32u.dll
0x00007ff854440000 - 0x00007ff85446b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff831500000 - 0x00007ff83179a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3\COMCTL32.dll
0x00007ff851f00000 - 0x00007ff852037000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff852070000 - 0x00007ff852113000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff854290000 - 0x00007ff854339000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff854340000 - 0x00007ff85436f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff84c870000 - 0x00007ff84c87c000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff823400000 - 0x00007ff82348d000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007fffbe5a0000 - 0x00007fffbf330000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff8541d0000 - 0x00007ff854284000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff854380000 - 0x00007ff854426000 	C:\WINDOWS\System32\sechost.dll
0x00007ff854470000 - 0x00007ff854588000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff853140000 - 0x00007ff8531b4000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff850a90000 - 0x00007ff850aee000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff84b030000 - 0x00007ff84b03b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff842b20000 - 0x00007ff842b55000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff850a70000 - 0x00007ff850a84000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff850d30000 - 0x00007ff850d4b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff84c860000 - 0x00007ff84c86a000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff84f070000 - 0x00007ff84f2b1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff853910000 - 0x00007ff853c96000 	C:\WINDOWS\System32\combase.dll
0x00007ff8537b0000 - 0x00007ff853890000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff823260000 - 0x00007ff8232a3000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff8523f0000 - 0x00007ff852489000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff84c810000 - 0x00007ff84c81f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff823980000 - 0x00007ff82399f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff854590000 - 0x00007ff854cda000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff852550000 - 0x00007ff8526c4000 	C:\WINDOWS\System32\wintypes.dll
0x00007ff84faf0000 - 0x00007ff85034b000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff8540a0000 - 0x00007ff854195000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff8538a0000 - 0x00007ff85390a000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff851d80000 - 0x00007ff851daf000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff8238b0000 - 0x00007ff8238c8000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff84c850000 - 0x00007ff84c860000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ff84bc80000 - 0x00007ff84bd9e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff8512c0000 - 0x00007ff85132a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff823890000 - 0x00007ff8238a6000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff839c90000 - 0x00007ff839ca0000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ff81e1c0000 - 0x00007ff81e205000 	C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ff853600000 - 0x00007ff85379e000 	C:\WINDOWS\System32\ole32.dll
0x00007ff851570000 - 0x00007ff85158b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff850c90000 - 0x00007ff850ccb000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff851360000 - 0x00007ff85138b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff851d50000 - 0x00007ff851d76000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ff851590000 - 0x00007ff85159c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff8506d0000 - 0x00007ff850703000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff853f10000 - 0x00007ff853f1a000 	C:\WINDOWS\System32\NSI.dll
0x00007ffffd640000 - 0x00007ffffd689000 	C:\Users\<USER>\AppData\Local\Temp\jna-54154080\jna15900159811661328287.dll
0x00007ff853ee0000 - 0x00007ff853ee8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff84c3c0000 - 0x00007ff84c3df000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ff84c390000 - 0x00007ff84c3b5000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007fffc9620000 - 0x00007fffc9647000 	D:\gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00007ff82ffc0000 - 0x00007ff82ffca000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
0x00007ff82fe40000 - 0x00007ff82fe4b000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3;c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-54154080;D:\gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-e2487a37ffc33e95e3346a9155da3121-sock
java_class_path (initial): c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-23
PATH=D:\Ӱ��;C;\Program Files\Java\jdk-23\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\ProgramData\chocolatey\bin;C:\Program Files\nodejs\;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;D:\΢�ſ�;�߹���\΢��web�����߹���\dll;;C:\Program Files\Git\cmd;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;d:\Trae��̹���\Trae CN\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\VSCOD\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Java\jdk-23\bin;C:\Users\<USER>\.android\platform-tools-latest-windows\platform-tools;
USERNAME=91668
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4484)
OS uptime: 0 days 14:32 hours

CPU: total 24 (initial active 24) (12 cores per cpu, 2 threads per core) family 6 model 151 stepping 2 microcode 0x2c, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, serialize, rdtscp, rdpid, fsrm, f16c, pku, cet_ibt, cet_ss
Processor Information for processor 0
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 1
  Max Mhz: 2300, Current Mhz: 1506, Mhz Limit: 2300
Processor Information for processor 2
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 3
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 4
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 5
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 6
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 7
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 8
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 9
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 10
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 11
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 12
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 13
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 14
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 15
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 16
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 17
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 18
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 19
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 20
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 21
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 22
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 23
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300

Memory: 4k page, system-wide physical 16121M (1452M free)
TotalPageFile size 28921M (AvailPageFile size 26M)
current process WorkingSet (physical memory assigned to process): 417M, peak: 455M
current process commit charge ("private bytes"): 492M, peak: 519M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
