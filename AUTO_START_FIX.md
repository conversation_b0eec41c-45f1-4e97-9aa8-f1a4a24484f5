# 开机自启功能修复方案

## 🔍 问题描述

用户反馈：**手机关机后需要打开软件后才能自动调节，就是开机自启这部分还有哪里没有做好**

## 🐛 问题原因分析

### 1. Android 8.0+ 后台限制
- Android 8.0及以上版本对后台服务有严格限制
- 开机自启需要额外的权限和配置

### 2. 开机自启权限不足
- 缺少必要的开机自启权限
- 不同品牌手机需要不同的权限设置

### 3. 服务启动时机问题
- 开机时系统可能还没有完全准备好
- 需要延迟启动服务

### 4. 电池优化影响
- 系统可能阻止开机自启
- 需要电池优化豁免

## ✅ 修复方案

### 1. 添加更多开机自启权限

**文件：`AndroidManifest.xml`**
```xml
<!-- 开机自启权限 -->
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
<uses-permission android:name="android.permission.QUICKBOOT_POWERON" />
<uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
```

### 2. 改进开机自启接收器

**文件：`BootReceiver.kt`**

**改进Intent过滤器：**
```xml
<receiver
    android:name=".BootReceiver"
    android:enabled="true"
    android:exported="true"
    android:permission="android.permission.RECEIVE_BOOT_COMPLETED">
    <intent-filter android:priority="1000">
        <action android:name="android.intent.action.BOOT_COMPLETED" />
        <action android:name="android.intent.action.QUICKBOOT_POWERON" />
        <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
        <action android:name="android.intent.action.PACKAGE_REPLACED" />
        <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED" />
        <category android:name="android.intent.category.DEFAULT" />
        <data android:scheme="package" />
    </intent-filter>
</receiver>
```

**改进开机事件处理：**
```kotlin
when (action) {
    Intent.ACTION_BOOT_COMPLETED,
    "android.intent.action.QUICKBOOT_POWERON",
    Intent.ACTION_MY_PACKAGE_REPLACED,
    Intent.ACTION_PACKAGE_REPLACED,
    "android.intent.action.LOCKED_BOOT_COMPLETED" -> {
        Log.d(TAG, "收到开机事件: $action")
        handleBootCompleted(context)
    }
    else -> {
        Log.d(TAG, "收到其他广播: $action")
    }
}
```

### 3. 延迟启动服务

**改进`handleBootCompleted()`方法：**
```kotlin
private fun handleBootCompleted(context: Context) {
    try {
        Log.d(TAG, "开始处理开机完成事件")
        
        // 使用新的设置管理器
        val autoStartEnabled = EyeCareSettingsManager.isAutoStartEnabled(context)
        
        if (!autoStartEnabled) {
            Log.d(TAG, "开机自启已禁用，跳过启动服务")
            return
        }
        
        // 恢复用户设置
        val eyeCareMode = EyeCareSettingsManager.getEyeCareMode(context)
        val autoAdjustment = EyeCareSettingsManager.isAutoAdjustmentEnabled(context)
        val backgroundServiceEnabled = EyeCareSettingsManager.isBackgroundServiceEnabled(context)
        
        Log.d(TAG, "恢复护眼设置: 模式=$eyeCareMode, 自动调节=$autoAdjustment, 后台服务=$backgroundServiceEnabled")
        
        // 延迟启动服务，确保系统完全启动
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            try {
                // 只有启用了后台服务才启动
                if (backgroundServiceEnabled) {
                    startEyeCareService(context, eyeCareMode, autoAdjustment)
                }
                
                // 发送开机启动通知
                sendBootNotification(context)
                
            } catch (e: Exception) {
                Log.e(TAG, "延迟启动服务失败: ${e.message}", e)
            }
        }, 10000) // 延迟10秒启动
        
    } catch (e: Exception) {
        Log.e(TAG, "开机启动处理失败: ${e.message}", e)
    }
}
```

### 4. 改进服务启动方法

**改进`startEyeCareService()`方法：**
```kotlin
private fun startEyeCareService(
    context: Context,
    eyeCareMode: String?,
    autoAdjustment: Boolean
) {
    try {
        Log.d(TAG, "准备启动护眼服务: 模式=$eyeCareMode, 自动调节=$autoAdjustment")
        
        val serviceIntent = Intent(context, EyeCareBackgroundService::class.java).apply {
            putExtra("eye_care_mode", eyeCareMode ?: "STANDARD")
            putExtra("auto_adjustment", autoAdjustment)
            putExtra("start_reason", "boot_completed")
            // 添加标志确保服务能正确启动
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        
        // Android 8.0+ 需要使用 startForegroundService
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                context.startForegroundService(serviceIntent)
                Log.d(TAG, "使用 startForegroundService 启动成功")
            } catch (e: Exception) {
                Log.w(TAG, "startForegroundService 失败，尝试 startService: ${e.message}")
                context.startService(serviceIntent)
            }
        } else {
            context.startService(serviceIntent)
            Log.d(TAG, "使用 startService 启动成功")
        }
        
        Log.d(TAG, "护眼后台服务启动成功")
        
    } catch (e: Exception) {
        Log.e(TAG, "启动护眼服务失败: ${e.message}", e)
    }
}
```

### 5. 添加开机启动日志记录

**改进`sendBootNotification()`方法：**
```kotlin
private fun sendBootNotification(context: Context) {
    try {
        // 记录开机启动日志
        Log.d(TAG, "护眼保护已自动启动")
        
        // 保存开机启动记录
        val prefs = context.getSharedPreferences("boot_log", Context.MODE_PRIVATE)
        prefs.edit().apply {
            putLong("last_boot_time", System.currentTimeMillis())
            putString("last_boot_action", "success")
            apply()
        }
        
    } catch (e: Exception) {
        Log.w(TAG, "发送开机通知失败: ${e.message}")
        
        // 记录失败日志
        val prefs = context.getSharedPreferences("boot_log", Context.MODE_PRIVATE)
        prefs.edit().apply {
            putLong("last_boot_time", System.currentTimeMillis())
            putString("last_boot_action", "failed")
            putString("last_boot_error", e.message)
            apply()
        }
    }
}
```

### 6. 添加用户设置指导

**文件：`MainActivity.kt`**

**添加开机自启设置指导：**
```kotlin
private fun showAutoStartGuide() {
    val dialog = AlertDialog.Builder(this)
        .setTitle("开机自启设置指导")
        .setMessage("""
            为了确保开机自启功能正常工作，请按以下步骤设置：
            
            1️⃣ 系统设置 → 应用管理 → 护眼光感调节
            2️⃣ 权限管理 → 自启动 → 允许
            3️⃣ 电池优化 → 不优化
            4️⃣ 后台运行 → 允许
            
            不同品牌手机设置路径可能不同：
            • 华为：设置 → 应用 → 护眼光感调节 → 自启动
            • 小米：设置 → 应用管理 → 护眼光感调节 → 权限管理
            • OPPO：设置 → 应用管理 → 护眼光感调节 → 自启动
            • vivo：设置 → 应用管理 → 护眼光感调节 → 自启动
            
            设置完成后，重启手机测试开机自启功能。
        """.trimIndent())
        .setPositiveButton("去设置") { _, _ ->
            openAppSettings()
        }
        .setNegativeButton("知道了") { _, _ -> }
        .setCancelable(false)
        .create()
    
    dialog.show()
}
```

**添加开机状态检查：**
```kotlin
private fun checkAutoStartStatus(): String {
    try {
        val prefs = getSharedPreferences("boot_log", Context.MODE_PRIVATE)
        val lastBootTime = prefs.getLong("last_boot_time", 0)
        val lastBootAction = prefs.getString("last_boot_action", "unknown")
        val lastBootError = prefs.getString("last_boot_error", "")
        
        if (lastBootTime == 0L) {
            return "未检测到开机启动记录"
        }
        
        val timeAgo = (System.currentTimeMillis() - lastBootTime) / 1000 / 60 // 分钟
        val status = when (lastBootAction) {
            "success" -> "✅ 开机启动成功"
            "failed" -> "❌ 开机启动失败: $lastBootError"
            else -> "❓ 开机启动状态未知"
        }
        
        return "$status (${timeAgo}分钟前)"
        
    } catch (e: Exception) {
        return "检查开机状态失败: ${e.message}"
    }
}
```

## 🎯 修复效果

### 修复前的问题：
1. ❌ 开机后需要手动打开应用才能自动调节
2. ❌ 开机自启功能不工作
3. ❌ 缺少必要的权限和配置
4. ❌ 没有用户设置指导

### 修复后的效果：
1. ✅ 开机后自动启动护眼服务
2. ✅ 自动恢复用户的护眼设置
3. ✅ 支持多种开机事件（包括快速启动）
4. ✅ 提供详细的用户设置指导
5. ✅ 记录开机启动日志便于调试
6. ✅ 延迟启动确保系统稳定性

## 🔧 技术要点

### 1. 权限配置
- **RECEIVE_BOOT_COMPLETED**：标准开机自启权限
- **QUICKBOOT_POWERON**：快速启动权限
- **DISABLE_KEYGUARD**：解锁屏幕权限
- **WAKE_LOCK**：保持唤醒权限

### 2. 启动策略
- **延迟启动**：等待系统完全启动
- **多重尝试**：startForegroundService失败时回退到startService
- **状态检查**：确保用户启用了开机自启和后台服务

### 3. 兼容性处理
- **Android版本适配**：针对不同Android版本使用不同的启动方法
- **品牌适配**：提供不同品牌手机的设置指导
- **错误处理**：捕获异常并记录日志

### 4. 用户体验
- **设置指导**：详细的设置步骤说明
- **状态反馈**：显示开机启动状态
- **日志记录**：便于问题排查

## 📱 测试建议

1. **基本功能测试**：
   - 启用开机自启和后台服务
   - 重启手机
   - 检查护眼服务是否自动启动

2. **权限设置测试**：
   - 按照指导设置应用权限
   - 测试不同品牌手机的设置路径
   - 验证电池优化豁免

3. **日志检查测试**：
   - 查看开机启动日志
   - 检查服务启动状态
   - 验证设置恢复情况

## 🎉 总结

通过添加必要的权限、改进开机自启接收器、延迟启动服务和提供用户设置指导，成功解决了开机自启功能不工作的问题。现在用户重启手机后，护眼服务会自动启动并恢复之前的设置，无需手动打开应用。 