# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. For more details, visit
# https://developer.android.com/r/tools/gradle-multi-project-decoupled-projects
# org.gradle.parallel=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true

# ===============================================
# ç½ç»è¿æ¥è¶æ¶è§£å³æ¹æ¡éç½®
# Network timeout and proxy configuration
# ===============================================

# å¢å ç½ç»è¶æ¶æ¶é´ï¼æ¯«ç§ï¼
# Increase network timeout (milliseconds)
systemProp.org.gradle.internal.http.connectionTimeout=300000
systemProp.org.gradle.internal.http.socketTimeout=300000

# å¦æä½¿ç¨ä»£çï¼è¯·åæ¶æ³¨éå¹¶éç½®ä»¥ä¸è®¾ç½®
# If using proxy, uncomment and configure the following settings
# systemProp.http.proxyHost=your.proxy.host
# systemProp.http.proxyPort=8080
# systemProp.https.proxyHost=your.proxy.host
# systemProp.https.proxyPort=8080
# systemProp.http.proxyUser=username
# systemProp.http.proxyPassword=password

# ===============================================
# JDKå·¥å·é¾æ ¹æ¬è§£å³æ¹æ¡éç½®
# JDK toolchain fundamental solution configuration
# ===============================================

# æç¡®æå®JDKè·¯å¾ï¼ç¡®ä¿ä½¿ç¨åå«jlinkå·¥å·çå®æ´JDK
# ä½¿ç¨æ­£ææ é¿åWindowsè·¯å¾è½¬ä¹é®é¢
# Explicitly specify JDK path to ensure using complete JDK with jlink tool
# Use forward slashes to avoid Windows path escaping issues
org.gradle.java.home=C:/Program Files/Java/jdk-23

# ææ¶ç¦ç¨éç½®ç¼å­ä»¥é¿åJDKè·¯å¾ç¼å­é®é¢
# Temporarily disable configuration cache to avoid JDK path caching issues
# æ³¨æï¼éç½®ç¼å­å¯è½ä¼ç¼å­éè¯¯çJDKè·¯å¾ï¼å¯¼è´jlinkæ¾ä¸å°
# Note: Configuration cache may cache incorrect JDK paths, causing jlink not found
org.gradle.configuration-cache=false

# ä½¿ç¨å¹¶è¡æå»ºï¼å¦æé¡¹ç®æ¯æï¼
# Enable parallel builds (if project supports it)
org.gradle.parallel=true

# å¯ç¨æå»ºç¼å­
# Enable build cache
org.gradle.caching=true