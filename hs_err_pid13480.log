#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 567216 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=13480, tid=22044
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-4440e0ccc089c3adca2403b8fc107d65-sock

Host: 12th Gen Intel(R) Core(TM) i9-12900HX, 24 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4484)
Time: Mon Jul 21 22:28:05 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4484) elapsed time: 7.068685 seconds (0d 0h 0m 7s)

---------------  T H R E A D  ---------------

Current thread (0x0000019ea82de210):  JavaThread "C2 CompilerThread5" daemon [_thread_in_native, id=22044, stack(0x0000007ea4200000,0x0000007ea4300000) (1024K)]


Current CompileTask:
C2:7068 6208       4       jdk.internal.jrtfs.JrtFileSystem::lambda$iteratorOf$2 (20 bytes)

Stack: [0x0000007ea4200000,0x0000007ea4300000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0x3b68f2]
V  [jvm.dll+0x382aa5]
V  [jvm.dll+0x381f0a]
V  [jvm.dll+0x247af0]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000019e9c5510d0, length=67, elements={
0x0000019e4355dff0, 0x0000019e9c483e30, 0x0000019e9c485c50, 0x0000019e43600f90,
0x0000019e43600270, 0x0000019e43600900, 0x0000019e43601620, 0x0000019e4360b910,
0x0000019e4360d080, 0x0000019e4361dfe0, 0x0000019e435c87f0, 0x0000019e435ffbe0,
0x0000019e9c5e95a0, 0x0000019e9c72f1f0, 0x0000019e43601cb0, 0x0000019ea32ca050,
0x0000019ea3842c80, 0x0000019e43602340, 0x0000019e436029d0, 0x0000019ea3c87280,
0x0000019ea3c87910, 0x0000019ea3c86bf0, 0x0000019ea3c86560, 0x0000019ea3c85ed0,
0x0000019ea3c87fa0, 0x0000019ea3c88630, 0x0000019ea3c851b0, 0x0000019ea3c88cc0,
0x0000019ea3c85840, 0x0000019ea450bb80, 0x0000019ea3c8ad90, 0x0000019ea3c8a070,
0x0000019ea3c899e0, 0x0000019ea3c8a700, 0x0000019ea3c8bab0, 0x0000019ea3c8c140,
0x0000019ea78d1870, 0x0000019ea78d3fd0, 0x0000019ea78d32b0, 0x0000019ea78d2590,
0x0000019ea78d2c20, 0x0000019ea78d3940, 0x0000019ea78d4660, 0x0000019ea78d11e0,
0x0000019ea78d4cf0, 0x0000019ea78d5380, 0x0000019ea78d1f00, 0x0000019ea78d6730,
0x0000019ea78d8170, 0x0000019ea78d5a10, 0x0000019ea78d7ae0, 0x0000019ea78d7450,
0x0000019ea78d8800, 0x0000019ea78d60a0, 0x0000019ea78d6dc0, 0x0000019ea7b6de60,
0x0000019ea7b6e4f0, 0x0000019ea7b6eb80, 0x0000019ea7b6ff30, 0x0000019ea82de210,
0x0000019ea7b6f210, 0x0000019ea7b6f8a0, 0x0000019ea3e9e060, 0x0000019ea362e220,
0x0000019ea7b705c0, 0x0000019ea7b6d140, 0x0000019ea7b70c50
}

Java Threads: ( => current thread )
  0x0000019e4355dff0 JavaThread "main"                              [_thread_blocked, id=20624, stack(0x0000007e9f100000,0x0000007e9f200000) (1024K)]
  0x0000019e9c483e30 JavaThread "Reference Handler"          daemon [_thread_blocked, id=22848, stack(0x0000007e9f500000,0x0000007e9f600000) (1024K)]
  0x0000019e9c485c50 JavaThread "Finalizer"                  daemon [_thread_blocked, id=25220, stack(0x0000007e9f600000,0x0000007e9f700000) (1024K)]
  0x0000019e43600f90 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=23896, stack(0x0000007e9f700000,0x0000007e9f800000) (1024K)]
  0x0000019e43600270 JavaThread "Attach Listener"            daemon [_thread_blocked, id=17364, stack(0x0000007e9f800000,0x0000007e9f900000) (1024K)]
  0x0000019e43600900 JavaThread "Service Thread"             daemon [_thread_blocked, id=2812, stack(0x0000007e9f900000,0x0000007e9fa00000) (1024K)]
  0x0000019e43601620 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=20284, stack(0x0000007e9fa00000,0x0000007e9fb00000) (1024K)]
  0x0000019e4360b910 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=12192, stack(0x0000007e9fb00000,0x0000007e9fc00000) (1024K)]
  0x0000019e4360d080 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=26484, stack(0x0000007e9fc00000,0x0000007e9fd00000) (1024K)]
  0x0000019e4361dfe0 JavaThread "C1 CompilerThread1"         daemon [_thread_blocked, id=31116, stack(0x0000007e9fd00000,0x0000007e9fe00000) (1024K)]
  0x0000019e435c87f0 JavaThread "C1 CompilerThread2"         daemon [_thread_blocked, id=8892, stack(0x0000007e9fe00000,0x0000007e9ff00000) (1024K)]
  0x0000019e435ffbe0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=30580, stack(0x0000007e9ff00000,0x0000007ea0000000) (1024K)]
  0x0000019e9c5e95a0 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=20156, stack(0x0000007ea0000000,0x0000007ea0100000) (1024K)]
  0x0000019e9c72f1f0 JavaThread "C1 CompilerThread3"         daemon [_thread_blocked, id=32088, stack(0x0000007ea0100000,0x0000007ea0200000) (1024K)]
  0x0000019e43601cb0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=2104, stack(0x0000007ea0200000,0x0000007ea0300000) (1024K)]
  0x0000019ea32ca050 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=4388, stack(0x0000007ea0300000,0x0000007ea0400000) (1024K)]
  0x0000019ea3842c80 JavaThread "C2 CompilerThread3"         daemon [_thread_blocked, id=6220, stack(0x0000007ea0400000,0x0000007ea0500000) (1024K)]
  0x0000019e43602340 JavaThread "Active Thread: Equinox Container: a9872c13-8df7-4854-8019-cd2b92b81817"        [_thread_blocked, id=14116, stack(0x0000007ea0b00000,0x0000007ea0c00000) (1024K)]
  0x0000019e436029d0 JavaThread "Refresh Thread: Equinox Container: a9872c13-8df7-4854-8019-cd2b92b81817" daemon [_thread_blocked, id=8912, stack(0x0000007ea0d00000,0x0000007ea0e00000) (1024K)]
  0x0000019ea3c87280 JavaThread "Framework Event Dispatcher: Equinox Container: a9872c13-8df7-4854-8019-cd2b92b81817" daemon [_thread_blocked, id=32460, stack(0x0000007ea0e00000,0x0000007ea0f00000) (1024K)]
  0x0000019ea3c87910 JavaThread "Start Level: Equinox Container: a9872c13-8df7-4854-8019-cd2b92b81817" daemon [_thread_blocked, id=5764, stack(0x0000007ea0f00000,0x0000007ea1000000) (1024K)]
  0x0000019ea3c86bf0 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=15352, stack(0x0000007ea1300000,0x0000007ea1400000) (1024K)]
  0x0000019ea3c86560 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=5216, stack(0x0000007ea1400000,0x0000007ea1500000) (1024K)]
  0x0000019ea3c85ed0 JavaThread "SCR Component Registry"     daemon [_thread_blocked, id=25272, stack(0x0000007ea1500000,0x0000007ea1600000) (1024K)]
  0x0000019ea3c87fa0 JavaThread "Worker-JM"                         [_thread_blocked, id=29092, stack(0x0000007ea1700000,0x0000007ea1800000) (1024K)]
  0x0000019ea3c88630 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=29768, stack(0x0000007ea1b00000,0x0000007ea1c00000) (1024K)]
  0x0000019ea3c851b0 JavaThread "Worker-0: Initialize After Load"        [_thread_blocked, id=12356, stack(0x0000007ea1c00000,0x0000007ea1d00000) (1024K)]
  0x0000019ea3c88cc0 JavaThread "Worker-1: Repository registry initialization"        [_thread_blocked, id=27740, stack(0x0000007ea1d00000,0x0000007ea1e00000) (1024K)]
  0x0000019ea3c85840 JavaThread "Worker-2: Building"                [_thread_blocked, id=29468, stack(0x0000007ea1e00000,0x0000007ea1f00000) (1024K)]
  0x0000019ea450bb80 JavaThread "C2 CompilerThread4"         daemon [_thread_blocked, id=7848, stack(0x0000007ea2200000,0x0000007ea2300000) (1024K)]
  0x0000019ea3c8ad90 JavaThread "Java indexing"              daemon [_thread_blocked, id=20356, stack(0x0000007ea2300000,0x0000007ea2400000) (1024K)]
  0x0000019ea3c8a070 JavaThread "Worker-3: Updating workspace"        [_thread_blocked, id=28208, stack(0x0000007ea2600000,0x0000007ea2700000) (1024K)]
  0x0000019ea3c899e0 JavaThread "Worker-4: Updating Maven Dependencies"        [_thread_blocked, id=2300, stack(0x0000007ea2700000,0x0000007ea2800000) (1024K)]
  0x0000019ea3c8a700 JavaThread "Thread-2"                   daemon [_thread_in_native, id=25704, stack(0x0000007ea2800000,0x0000007ea2900000) (1024K)]
  0x0000019ea3c8bab0 JavaThread "Thread-3"                   daemon [_thread_in_native, id=19512, stack(0x0000007ea2900000,0x0000007ea2a00000) (1024K)]
  0x0000019ea3c8c140 JavaThread "Thread-4"                   daemon [_thread_in_native, id=25604, stack(0x0000007ea2a00000,0x0000007ea2b00000) (1024K)]
  0x0000019ea78d1870 JavaThread "Thread-5"                   daemon [_thread_in_native, id=31500, stack(0x0000007ea2b00000,0x0000007ea2c00000) (1024K)]
  0x0000019ea78d3fd0 JavaThread "Thread-6"                   daemon [_thread_in_native, id=29452, stack(0x0000007ea2c00000,0x0000007ea2d00000) (1024K)]
  0x0000019ea78d32b0 JavaThread "Thread-7"                   daemon [_thread_in_native, id=30476, stack(0x0000007ea2d00000,0x0000007ea2e00000) (1024K)]
  0x0000019ea78d2590 JavaThread "Thread-8"                   daemon [_thread_in_native, id=13204, stack(0x0000007ea2e00000,0x0000007ea2f00000) (1024K)]
  0x0000019ea78d2c20 JavaThread "Thread-9"                   daemon [_thread_in_native, id=8788, stack(0x0000007ea2f00000,0x0000007ea3000000) (1024K)]
  0x0000019ea78d3940 JavaThread "Thread-10"                  daemon [_thread_in_native, id=28252, stack(0x0000007ea3000000,0x0000007ea3100000) (1024K)]
  0x0000019ea78d4660 JavaThread "Thread-11"                  daemon [_thread_in_native, id=31088, stack(0x0000007ea3100000,0x0000007ea3200000) (1024K)]
  0x0000019ea78d11e0 JavaThread "Thread-12"                  daemon [_thread_in_native, id=9108, stack(0x0000007ea3200000,0x0000007ea3300000) (1024K)]
  0x0000019ea78d4cf0 JavaThread "Thread-13"                  daemon [_thread_in_native, id=8044, stack(0x0000007ea3300000,0x0000007ea3400000) (1024K)]
  0x0000019ea78d5380 JavaThread "Thread-14"                  daemon [_thread_in_native, id=20908, stack(0x0000007ea3400000,0x0000007ea3500000) (1024K)]
  0x0000019ea78d1f00 JavaThread "Thread-15"                  daemon [_thread_in_native, id=31900, stack(0x0000007ea3500000,0x0000007ea3600000) (1024K)]
  0x0000019ea78d6730 JavaThread "Thread-16"                  daemon [_thread_in_native, id=30988, stack(0x0000007ea3600000,0x0000007ea3700000) (1024K)]
  0x0000019ea78d8170 JavaThread "Thread-17"                  daemon [_thread_in_native, id=32692, stack(0x0000007ea3700000,0x0000007ea3800000) (1024K)]
  0x0000019ea78d5a10 JavaThread "Thread-18"                  daemon [_thread_in_native, id=17608, stack(0x0000007ea3800000,0x0000007ea3900000) (1024K)]
  0x0000019ea78d7ae0 JavaThread "Thread-19"                  daemon [_thread_in_native, id=26512, stack(0x0000007ea3900000,0x0000007ea3a00000) (1024K)]
  0x0000019ea78d7450 JavaThread "Thread-20"                  daemon [_thread_in_native, id=28372, stack(0x0000007ea3a00000,0x0000007ea3b00000) (1024K)]
  0x0000019ea78d8800 JavaThread "Thread-21"                  daemon [_thread_in_native, id=9488, stack(0x0000007ea3b00000,0x0000007ea3c00000) (1024K)]
  0x0000019ea78d60a0 JavaThread "Thread-22"                  daemon [_thread_in_native, id=23092, stack(0x0000007ea3c00000,0x0000007ea3d00000) (1024K)]
  0x0000019ea78d6dc0 JavaThread "Thread-23"                  daemon [_thread_in_native, id=25804, stack(0x0000007ea3d00000,0x0000007ea3e00000) (1024K)]
  0x0000019ea7b6de60 JavaThread "Thread-24"                  daemon [_thread_in_native, id=22056, stack(0x0000007ea3e00000,0x0000007ea3f00000) (1024K)]
  0x0000019ea7b6e4f0 JavaThread "Thread-25"                  daemon [_thread_in_native, id=16976, stack(0x0000007ea3f00000,0x0000007ea4000000) (1024K)]
  0x0000019ea7b6eb80 JavaThread "Thread-26"                  daemon [_thread_in_native, id=13520, stack(0x0000007ea4000000,0x0000007ea4100000) (1024K)]
  0x0000019ea7b6ff30 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=12404, stack(0x0000007ea4100000,0x0000007ea4200000) (1024K)]
=>0x0000019ea82de210 JavaThread "C2 CompilerThread5"         daemon [_thread_in_native, id=22044, stack(0x0000007ea4200000,0x0000007ea4300000) (1024K)]
  0x0000019ea7b6f210 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=32164, stack(0x0000007ea4300000,0x0000007ea4400000) (1024K)]
  0x0000019ea7b6f8a0 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=4824, stack(0x0000007ea4400000,0x0000007ea4500000) (1024K)]
  0x0000019ea3e9e060 JavaThread "C2 CompilerThread6"         daemon [_thread_blocked, id=30820, stack(0x0000007ea4500000,0x0000007ea4600000) (1024K)]
  0x0000019ea362e220 JavaThread "C2 CompilerThread7"         daemon [_thread_blocked, id=2544, stack(0x0000007ea4600000,0x0000007ea4700000) (1024K)]
  0x0000019ea7b705c0 JavaThread "Worker-5"                          [_thread_blocked, id=32192, stack(0x0000007ea4700000,0x0000007ea4800000) (1024K)]
  0x0000019ea7b6d140 JavaThread "Worker-6: Java indexing... "        [_thread_blocked, id=14508, stack(0x0000007ea4800000,0x0000007ea4900000) (1024K)]
  0x0000019ea7b70c50 JavaThread "Worker-7"                          [_thread_blocked, id=4948, stack(0x0000007ea4900000,0x0000007ea4a00000) (1024K)]
Total: 67

Other Threads:
  0x0000019e9c479680 VMThread "VM Thread"                           [id=29512, stack(0x0000007e9f400000,0x0000007e9f500000) (1024K)] _threads_hazard_ptr=0x0000019e9c5510d0
  0x0000019e433eca00 WatcherThread "VM Periodic Task Thread"        [id=25048, stack(0x0000007e9f300000,0x0000007e9f400000) (1024K)]
  0x0000019e4357b3f0 WorkerThread "GC Thread#0"                     [id=30700, stack(0x0000007e9f200000,0x0000007e9f300000) (1024K)]
  0x0000019ea3a9fc90 WorkerThread "GC Thread#1"                     [id=30100, stack(0x0000007ea0500000,0x0000007ea0600000) (1024K)]
  0x0000019ea367f600 WorkerThread "GC Thread#2"                     [id=21736, stack(0x0000007ea0600000,0x0000007ea0700000) (1024K)]
  0x0000019ea367f9a0 WorkerThread "GC Thread#3"                     [id=22680, stack(0x0000007ea0700000,0x0000007ea0800000) (1024K)]
  0x0000019ea31fbce0 WorkerThread "GC Thread#4"                     [id=12536, stack(0x0000007ea0800000,0x0000007ea0900000) (1024K)]
  0x0000019ea31fcca0 WorkerThread "GC Thread#5"                     [id=9544, stack(0x0000007ea0900000,0x0000007ea0a00000) (1024K)]
  0x0000019ea31fd040 WorkerThread "GC Thread#6"                     [id=4400, stack(0x0000007ea0a00000,0x0000007ea0b00000) (1024K)]
  0x0000019ea391c430 WorkerThread "GC Thread#7"                     [id=21324, stack(0x0000007ea0c00000,0x0000007ea0d00000) (1024K)]
  0x0000019ea39d1f20 WorkerThread "GC Thread#8"                     [id=22540, stack(0x0000007ea1000000,0x0000007ea1100000) (1024K)]
  0x0000019ea4136da0 WorkerThread "GC Thread#9"                     [id=8392, stack(0x0000007ea1100000,0x0000007ea1200000) (1024K)]
  0x0000019ea4136a00 WorkerThread "GC Thread#10"                    [id=21448, stack(0x0000007ea1200000,0x0000007ea1300000) (1024K)]
  0x0000019ea4137140 WorkerThread "GC Thread#11"                    [id=16996, stack(0x0000007ea1600000,0x0000007ea1700000) (1024K)]
  0x0000019ea41362c0 WorkerThread "GC Thread#12"                    [id=31136, stack(0x0000007ea1800000,0x0000007ea1900000) (1024K)]
  0x0000019ea4136660 WorkerThread "GC Thread#13"                    [id=22936, stack(0x0000007ea1900000,0x0000007ea1a00000) (1024K)]
  0x0000019ea3fd9610 WorkerThread "GC Thread#14"                    [id=31396, stack(0x0000007ea1a00000,0x0000007ea1b00000) (1024K)]
  0x0000019ea3fd7910 WorkerThread "GC Thread#15"                    [id=2472, stack(0x0000007ea1f00000,0x0000007ea2000000) (1024K)]
  0x0000019ea3fd83f0 WorkerThread "GC Thread#16"                    [id=24536, stack(0x0000007ea2000000,0x0000007ea2100000) (1024K)]
  0x0000019ea3fd8ed0 WorkerThread "GC Thread#17"                    [id=29208, stack(0x0000007ea2100000,0x0000007ea2200000) (1024K)]
Total: 20

Threads with active compile tasks:
C2 CompilerThread0  7079 6267       4       jdk.internal.jrtfs.JrtPath::getName (116 bytes)
C1 CompilerThread0  7079 6645 % !   3       org.eclipse.jdt.internal.compiler.classfmt.ClassFileReader::<init> @ 904 (2135 bytes)
C1 CompilerThread2  7079 6667 %     3       org.eclipse.sisu.space.asm.ClassReader::readUtf @ 18 (161 bytes)
C2 CompilerThread1  7079 6272       4       java.util.ArrayList$SubList::equals (47 bytes)
C2 CompilerThread2  7079 6207       4       jdk.internal.jrtfs.JrtFileSystem$$Lambda/0x0000019e5b409d80::apply (16 bytes)
C2 CompilerThread3  7079 6248       4       jdk.internal.jrtfs.JrtPath::getName (6 bytes)
C2 CompilerThread4  7079 5516   !   4       org.eclipse.osgi.internal.loader.classpath.ClasspathManager::findClassImpl (343 bytes)
C2 CompilerThread5  7079 6208       4       jdk.internal.jrtfs.JrtFileSystem::lambda$iteratorOf$2 (20 bytes)
C2 CompilerThread6  7079 6209       4       jdk.internal.jrtfs.JrtPath::resolve (125 bytes)
C2 CompilerThread7  7079 6194       4       jdk.internal.jimage.ImageLocation::readFrom (26 bytes)
Total: 10

VM state: synchronizing (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007fffbf24e308] Threads_lock - owner thread: 0x0000019e9c479680

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000019e5a000000-0x0000019e5aba0000-0x0000019e5aba0000), size 12189696, SharedBaseAddress: 0x0000019e5a000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000019e5b000000-0x0000019e9b000000, reserved size: 1073741824
Narrow klass base: 0x0000019e5a000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 24 total, 24 available
 Memory: 16121M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 18

Heap:
 PSYoungGen      total 24576K, used 19329K [0x00000000d5580000, 0x00000000d7000000, 0x0000000100000000)
  eden space 22016K, 77% used [0x00000000d5580000,0x00000000d6628750,0x00000000d6b00000)
  from space 2560K, 88% used [0x00000000d6d80000,0x00000000d6fb8020,0x00000000d7000000)
  to   space 2560K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6d80000)
 ParOldGen       total 68608K, used 31624K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 46% used [0x0000000080000000,0x0000000081ee2188,0x0000000084300000)
 Metaspace       used 40261K, committed 41472K, reserved 1114112K
  class space    used 4092K, committed 4672K, reserved 1048576K

Card table byte_map: [0x0000019e42f00000,0x0000019e43310000] _byte_map_base: 0x0000019e42b00000

Marking Bits: (ParMarkBitMap*) 0x00007fffbf2531f0
 Begin Bits: [0x0000019e55a00000, 0x0000019e57a00000)
 End Bits:   [0x0000019e57a00000, 0x0000019e59a00000)

Polling page: 0x0000019e413e0000

Metaspace:

Usage:
  Non-class:     35.32 MB used.
      Class:      4.00 MB used.
       Both:     39.32 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      35.94 MB ( 56%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       4.56 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      40.50 MB (  4%) committed. 

Chunk freelists:
   Non-Class:  11.86 MB
       Class:  11.34 MB
        Both:  23.20 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 58.38 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 792.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 648.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 20.
num_chunks_taken_from_freelist: 2610.
num_chunk_merges: 12.
num_chunk_splits: 1593.
num_chunks_enlarged: 879.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=3028Kb max_used=3028Kb free=116139Kb
 bounds [0x0000019e4e2f0000, 0x0000019e4e5f0000, 0x0000019e55750000]
CodeHeap 'profiled nmethods': size=119104Kb used=12570Kb max_used=12570Kb free=106533Kb
 bounds [0x0000019e46750000, 0x0000019e473a0000, 0x0000019e4dba0000]
CodeHeap 'non-nmethods': size=7488Kb used=3083Kb max_used=3178Kb free=4404Kb
 bounds [0x0000019e4dba0000, 0x0000019e4dec0000, 0x0000019e4e2f0000]
 total_blobs=6662 nmethods=5988 adapters=574
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 5.039 Thread 0x0000019e4361dfe0 nmethod 6658 0x0000019e47384610 code [0x0000019e47384800, 0x0000019e47384b48]
Event: 5.039 Thread 0x0000019e9c72f1f0 6660       2       org.eclipse.core.internal.jobs.JobQueue::peek (26 bytes)
Event: 5.039 Thread 0x0000019e4361dfe0 6659       1       sun.nio.fs.WindowsException::lastError (5 bytes)
Event: 5.040 Thread 0x0000019e435c87f0 nmethod 6657 0x0000019e47384d10 code [0x0000019e47384f20, 0x0000019e473854c0]
Event: 5.040 Thread 0x0000019ea362e220 6192   !   4       jdk.internal.jimage.ImageReader$SharedImageReader::imageFileAttributes (46 bytes)
Event: 5.040 Thread 0x0000019e9c72f1f0 nmethod 6660 0x0000019e47385910 code [0x0000019e47385aa0, 0x0000019e47385bc8]
Event: 5.040 Thread 0x0000019e4361dfe0 nmethod 6659 0x0000019e4e5e4790 code [0x0000019e4e5e4920, 0x0000019e4e5e49e8]
Event: 5.040 Thread 0x0000019e435c87f0 6662       2       org.eclipse.jdt.internal.compiler.env.IModule$IPackageExport::isQualified (20 bytes)
Event: 5.040 Thread 0x0000019e9c72f1f0 6663       1       org.eclipse.jdt.internal.core.builder.ClasspathJrt::getKey (5 bytes)
Event: 5.040 Thread 0x0000019e4361dfe0 6664       1       org.eclipse.jdt.internal.core.builder.ClasspathJrt::hasModule (2 bytes)
Event: 5.040 Thread 0x0000019e435c87f0 nmethod 6662 0x0000019e47385c90 code [0x0000019e47385e40, 0x0000019e47385fd0]
Event: 5.040 Thread 0x0000019e9c72f1f0 nmethod 6663 0x0000019e4e5e4a90 code [0x0000019e4e5e4c20, 0x0000019e4e5e4ce8]
Event: 5.040 Thread 0x0000019e4361dfe0 nmethod 6664 0x0000019e4e5e4d90 code [0x0000019e4e5e4f20, 0x0000019e4e5e4fe8]
Event: 5.040 Thread 0x0000019e435c87f0 6665       2       org.eclipse.jdt.internal.core.builder.ClasspathJrt::getModule (42 bytes)
Event: 5.041 Thread 0x0000019e435c87f0 nmethod 6665 0x0000019e47386110 code [0x0000019e473862e0, 0x0000019e47386538]
Event: 5.041 Thread 0x0000019ea362e220 nmethod 6192 0x0000019e4e5e5090 code [0x0000019e4e5e5220, 0x0000019e4e5e52d0]
Event: 5.041 Thread 0x0000019ea362e220 6194       4       jdk.internal.jimage.ImageLocation::readFrom (26 bytes)
Event: 5.041 Thread 0x0000019e435c87f0 6666       2       sun.reflect.annotation.AnnotationInvocationHandler::<init> (71 bytes)
Event: 5.042 Thread 0x0000019e435c87f0 nmethod 6666 0x0000019e47386690 code [0x0000019e473868a0, 0x0000019e47386cf0]
Event: 5.042 Thread 0x0000019e435c87f0 6667 %     3       org.eclipse.sisu.space.asm.ClassReader::readUtf @ 18 (161 bytes)

GC Heap History (20 events):
Event: 2.500 GC heap before
{Heap before GC invocations=15 (full 1):
 PSYoungGen      total 25088K, used 20195K [0x00000000d5580000, 0x00000000d7080000, 0x0000000100000000)
  eden space 22016K, 83% used [0x00000000d5580000,0x00000000d677aaa8,0x00000000d6b00000)
  from space 3072K, 58% used [0x00000000d6b00000,0x00000000d6cbe280,0x00000000d6e00000)
  to   space 2048K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7080000)
 ParOldGen       total 68608K, used 16884K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 24% used [0x0000000080000000,0x000000008107d3f0,0x0000000084300000)
 Metaspace       used 34789K, committed 35840K, reserved 1114112K
  class space    used 3404K, committed 3904K, reserved 1048576K
}
Event: 2.502 GC heap after
{Heap after GC invocations=15 (full 1):
 PSYoungGen      total 24064K, used 1945K [0x00000000d5580000, 0x00000000d7080000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 2048K, 94% used [0x00000000d6e80000,0x00000000d7066470,0x00000000d7080000)
  to   space 2048K, 0% used [0x00000000d6c80000,0x00000000d6c80000,0x00000000d6e80000)
 ParOldGen       total 68608K, used 18036K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 26% used [0x0000000080000000,0x000000008119d2b8,0x0000000084300000)
 Metaspace       used 34789K, committed 35840K, reserved 1114112K
  class space    used 3404K, committed 3904K, reserved 1048576K
}
Event: 2.502 GC heap before
{Heap before GC invocations=16 (full 2):
 PSYoungGen      total 24064K, used 1945K [0x00000000d5580000, 0x00000000d7080000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 2048K, 94% used [0x00000000d6e80000,0x00000000d7066470,0x00000000d7080000)
  to   space 2048K, 0% used [0x00000000d6c80000,0x00000000d6c80000,0x00000000d6e80000)
 ParOldGen       total 68608K, used 18036K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 26% used [0x0000000080000000,0x000000008119d2b8,0x0000000084300000)
 Metaspace       used 34789K, committed 35840K, reserved 1114112K
  class space    used 3404K, committed 3904K, reserved 1048576K
}
Event: 2.528 GC heap after
{Heap after GC invocations=16 (full 2):
 PSYoungGen      total 24064K, used 0K [0x00000000d5580000, 0x00000000d7080000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 2048K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7080000)
  to   space 2048K, 0% used [0x00000000d6c80000,0x00000000d6c80000,0x00000000d6e80000)
 ParOldGen       total 68608K, used 18953K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 27% used [0x0000000080000000,0x0000000081282458,0x0000000084300000)
 Metaspace       used 34789K, committed 35840K, reserved 1114112K
  class space    used 3404K, committed 3904K, reserved 1048576K
}
Event: 2.655 GC heap before
{Heap before GC invocations=17 (full 2):
 PSYoungGen      total 24064K, used 22016K [0x00000000d5580000, 0x00000000d7080000, 0x0000000100000000)
  eden space 22016K, 100% used [0x00000000d5580000,0x00000000d6b00000,0x00000000d6b00000)
  from space 2048K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7080000)
  to   space 2048K, 0% used [0x00000000d6c80000,0x00000000d6c80000,0x00000000d6e80000)
 ParOldGen       total 68608K, used 18953K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 27% used [0x0000000080000000,0x0000000081282458,0x0000000084300000)
 Metaspace       used 36995K, committed 38144K, reserved 1114112K
  class space    used 3674K, committed 4160K, reserved 1048576K
}
Event: 2.657 GC heap after
{Heap after GC invocations=17 (full 2):
 PSYoungGen      total 24064K, used 1776K [0x00000000d5580000, 0x00000000d6f00000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 2048K, 86% used [0x00000000d6c80000,0x00000000d6e3c030,0x00000000d6e80000)
  to   space 512K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d6f00000)
 ParOldGen       total 68608K, used 18961K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 27% used [0x0000000080000000,0x0000000081284458,0x0000000084300000)
 Metaspace       used 36995K, committed 38144K, reserved 1114112K
  class space    used 3674K, committed 4160K, reserved 1048576K
}
Event: 2.687 GC heap before
{Heap before GC invocations=18 (full 2):
 PSYoungGen      total 24064K, used 23792K [0x00000000d5580000, 0x00000000d6f00000, 0x0000000100000000)
  eden space 22016K, 100% used [0x00000000d5580000,0x00000000d6b00000,0x00000000d6b00000)
  from space 2048K, 86% used [0x00000000d6c80000,0x00000000d6e3c030,0x00000000d6e80000)
  to   space 512K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d6f00000)
 ParOldGen       total 68608K, used 18961K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 27% used [0x0000000080000000,0x0000000081284458,0x0000000084300000)
 Metaspace       used 37430K, committed 38528K, reserved 1114112K
  class space    used 3720K, committed 4224K, reserved 1048576K
}
Event: 2.689 GC heap after
{Heap after GC invocations=18 (full 2):
 PSYoungGen      total 22528K, used 480K [0x00000000d5580000, 0x00000000d7200000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 512K, 93% used [0x00000000d6e80000,0x00000000d6ef8010,0x00000000d6f00000)
  to   space 3584K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6e80000)
 ParOldGen       total 68608K, used 21621K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 31% used [0x0000000080000000,0x000000008151d7f0,0x0000000084300000)
 Metaspace       used 37430K, committed 38528K, reserved 1114112K
  class space    used 3720K, committed 4224K, reserved 1048576K
}
Event: 2.757 GC heap before
{Heap before GC invocations=19 (full 2):
 PSYoungGen      total 22528K, used 22495K [0x00000000d5580000, 0x00000000d7200000, 0x0000000100000000)
  eden space 22016K, 99% used [0x00000000d5580000,0x00000000d6affdc0,0x00000000d6b00000)
  from space 512K, 93% used [0x00000000d6e80000,0x00000000d6ef8010,0x00000000d6f00000)
  to   space 3584K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6e80000)
 ParOldGen       total 68608K, used 21621K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 31% used [0x0000000080000000,0x000000008151d7f0,0x0000000084300000)
 Metaspace       used 38610K, committed 39744K, reserved 1114112K
  class space    used 3900K, committed 4416K, reserved 1048576K
}
Event: 2.758 GC heap after
{Heap after GC invocations=19 (full 2):
 PSYoungGen      total 24576K, used 2114K [0x00000000d5580000, 0x00000000d7000000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 2560K, 82% used [0x00000000d6b00000,0x00000000d6d10800,0x00000000d6d80000)
  to   space 2560K, 0% used [0x00000000d6d80000,0x00000000d6d80000,0x00000000d7000000)
 ParOldGen       total 68608K, used 22158K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 32% used [0x0000000080000000,0x00000000815a3800,0x0000000084300000)
 Metaspace       used 38610K, committed 39744K, reserved 1114112K
  class space    used 3900K, committed 4416K, reserved 1048576K
}
Event: 2.797 GC heap before
{Heap before GC invocations=20 (full 2):
 PSYoungGen      total 24576K, used 24130K [0x00000000d5580000, 0x00000000d7000000, 0x0000000100000000)
  eden space 22016K, 100% used [0x00000000d5580000,0x00000000d6b00000,0x00000000d6b00000)
  from space 2560K, 82% used [0x00000000d6b00000,0x00000000d6d10800,0x00000000d6d80000)
  to   space 2560K, 0% used [0x00000000d6d80000,0x00000000d6d80000,0x00000000d7000000)
 ParOldGen       total 68608K, used 22158K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 32% used [0x0000000080000000,0x00000000815a3800,0x0000000084300000)
 Metaspace       used 39075K, committed 40192K, reserved 1114112K
  class space    used 3963K, committed 4480K, reserved 1048576K
}
Event: 2.800 GC heap after
{Heap after GC invocations=20 (full 2):
 PSYoungGen      total 24576K, used 2336K [0x00000000d5580000, 0x00000000d7000000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 2560K, 91% used [0x00000000d6d80000,0x00000000d6fc8020,0x00000000d7000000)
  to   space 2560K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6d80000)
 ParOldGen       total 68608K, used 24080K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 35% used [0x0000000080000000,0x0000000081784000,0x0000000084300000)
 Metaspace       used 39075K, committed 40192K, reserved 1114112K
  class space    used 3963K, committed 4480K, reserved 1048576K
}
Event: 2.836 GC heap before
{Heap before GC invocations=21 (full 2):
 PSYoungGen      total 24576K, used 24352K [0x00000000d5580000, 0x00000000d7000000, 0x0000000100000000)
  eden space 22016K, 100% used [0x00000000d5580000,0x00000000d6b00000,0x00000000d6b00000)
  from space 2560K, 91% used [0x00000000d6d80000,0x00000000d6fc8020,0x00000000d7000000)
  to   space 2560K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6d80000)
 ParOldGen       total 68608K, used 24080K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 35% used [0x0000000080000000,0x0000000081784000,0x0000000084300000)
 Metaspace       used 39275K, committed 40384K, reserved 1114112K
  class space    used 3987K, committed 4480K, reserved 1048576K
}
Event: 2.839 GC heap after
{Heap after GC invocations=21 (full 2):
 PSYoungGen      total 24576K, used 2224K [0x00000000d5580000, 0x00000000d7000000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 2560K, 86% used [0x00000000d6b00000,0x00000000d6d2c010,0x00000000d6d80000)
  to   space 2560K, 0% used [0x00000000d6d80000,0x00000000d6d80000,0x00000000d7000000)
 ParOldGen       total 68608K, used 26156K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 38% used [0x0000000080000000,0x000000008198b150,0x0000000084300000)
 Metaspace       used 39275K, committed 40384K, reserved 1114112K
  class space    used 3987K, committed 4480K, reserved 1048576K
}
Event: 2.874 GC heap before
{Heap before GC invocations=22 (full 2):
 PSYoungGen      total 24576K, used 24240K [0x00000000d5580000, 0x00000000d7000000, 0x0000000100000000)
  eden space 22016K, 100% used [0x00000000d5580000,0x00000000d6b00000,0x00000000d6b00000)
  from space 2560K, 86% used [0x00000000d6b00000,0x00000000d6d2c010,0x00000000d6d80000)
  to   space 2560K, 0% used [0x00000000d6d80000,0x00000000d6d80000,0x00000000d7000000)
 ParOldGen       total 68608K, used 26156K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 38% used [0x0000000080000000,0x000000008198b150,0x0000000084300000)
 Metaspace       used 39424K, committed 40576K, reserved 1114112K
  class space    used 4014K, committed 4544K, reserved 1048576K
}
Event: 2.877 GC heap after
{Heap after GC invocations=22 (full 2):
 PSYoungGen      total 24576K, used 2173K [0x00000000d5580000, 0x00000000d7000000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 2560K, 84% used [0x00000000d6d80000,0x00000000d6f9f548,0x00000000d7000000)
  to   space 2560K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6d80000)
 ParOldGen       total 68608K, used 28018K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 40% used [0x0000000080000000,0x0000000081b5cb00,0x0000000084300000)
 Metaspace       used 39424K, committed 40576K, reserved 1114112K
  class space    used 4014K, committed 4544K, reserved 1048576K
}
Event: 4.976 GC heap before
{Heap before GC invocations=23 (full 2):
 PSYoungGen      total 24576K, used 24189K [0x00000000d5580000, 0x00000000d7000000, 0x0000000100000000)
  eden space 22016K, 100% used [0x00000000d5580000,0x00000000d6b00000,0x00000000d6b00000)
  from space 2560K, 84% used [0x00000000d6d80000,0x00000000d6f9f548,0x00000000d7000000)
  to   space 2560K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6d80000)
 ParOldGen       total 68608K, used 28018K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 40% used [0x0000000080000000,0x0000000081b5cb00,0x0000000084300000)
 Metaspace       used 39652K, committed 40832K, reserved 1114112K
  class space    used 4042K, committed 4544K, reserved 1048576K
}
Event: 4.978 GC heap after
{Heap after GC invocations=23 (full 2):
 PSYoungGen      total 24576K, used 2000K [0x00000000d5580000, 0x00000000d7000000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 2560K, 78% used [0x00000000d6b00000,0x00000000d6cf41f0,0x00000000d6d80000)
  to   space 2560K, 0% used [0x00000000d6d80000,0x00000000d6d80000,0x00000000d7000000)
 ParOldGen       total 68608K, used 29792K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 43% used [0x0000000080000000,0x0000000081d18178,0x0000000084300000)
 Metaspace       used 39652K, committed 40832K, reserved 1114112K
  class space    used 4042K, committed 4544K, reserved 1048576K
}
Event: 5.003 GC heap before
{Heap before GC invocations=24 (full 2):
 PSYoungGen      total 24576K, used 24016K [0x00000000d5580000, 0x00000000d7000000, 0x0000000100000000)
  eden space 22016K, 100% used [0x00000000d5580000,0x00000000d6b00000,0x00000000d6b00000)
  from space 2560K, 78% used [0x00000000d6b00000,0x00000000d6cf41f0,0x00000000d6d80000)
  to   space 2560K, 0% used [0x00000000d6d80000,0x00000000d6d80000,0x00000000d7000000)
 ParOldGen       total 68608K, used 29792K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 43% used [0x0000000080000000,0x0000000081d18178,0x0000000084300000)
 Metaspace       used 39832K, committed 40960K, reserved 1114112K
  class space    used 4054K, committed 4544K, reserved 1048576K
}
Event: 5.005 GC heap after
{Heap after GC invocations=24 (full 2):
 PSYoungGen      total 24576K, used 2272K [0x00000000d5580000, 0x00000000d7000000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 2560K, 88% used [0x00000000d6d80000,0x00000000d6fb8020,0x00000000d7000000)
  to   space 2560K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6d80000)
 ParOldGen       total 68608K, used 31624K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 46% used [0x0000000080000000,0x0000000081ee2188,0x0000000084300000)
 Metaspace       used 39832K, committed 40960K, reserved 1114112K
  class space    used 4054K, committed 4544K, reserved 1048576K
}

Dll operation events (10 events):
Event: 0.005 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.029 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.060 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.063 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.064 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.065 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.073 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.103 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 0.626 Loaded shared library C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 1.452 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-54154080\jna2595244243525305653.dll

Deoptimization events (20 events):
Event: 4.989 Thread 0x0000019ea3c899e0 DEOPT PACKING pc=0x0000019e4e5aa678 sp=0x0000007ea27fdb80
Event: 4.989 Thread 0x0000019ea3c899e0 DEOPT UNPACKING pc=0x0000019e4dbf3aa2 sp=0x0000007ea27fda38 mode 2
Event: 4.989 Thread 0x0000019ea3c8a070 DEOPT PACKING pc=0x0000019e472bb91f sp=0x0000007ea26fdb70
Event: 4.989 Thread 0x0000019ea3c8a070 DEOPT UNPACKING pc=0x0000019e4dbf4242 sp=0x0000007ea26fd048 mode 0
Event: 4.990 Thread 0x0000019ea3c899e0 DEOPT PACKING pc=0x0000019e4698179b sp=0x0000007ea27fdd10
Event: 4.990 Thread 0x0000019ea3c899e0 DEOPT UNPACKING pc=0x0000019e4dbf4242 sp=0x0000007ea27fd218 mode 0
Event: 4.991 Thread 0x0000019ea3c85840 DEOPT PACKING pc=0x0000019e472e4f46 sp=0x0000007ea1efd340
Event: 4.991 Thread 0x0000019ea3c85840 DEOPT UNPACKING pc=0x0000019e4dbf4242 sp=0x0000007ea1efc8c0 mode 0
Event: 5.018 Thread 0x0000019ea3c85840 DEOPT PACKING pc=0x0000019e46b8d0a6 sp=0x0000007ea1efd640
Event: 5.018 Thread 0x0000019ea3c85840 DEOPT UNPACKING pc=0x0000019e4dbf4242 sp=0x0000007ea1efcb30 mode 0
Event: 5.025 Thread 0x0000019ea3c85840 DEOPT PACKING pc=0x0000019e472bb91f sp=0x0000007ea1efd9f0
Event: 5.025 Thread 0x0000019ea3c85840 DEOPT UNPACKING pc=0x0000019e4dbf4242 sp=0x0000007ea1efcec8 mode 0
Event: 5.028 Thread 0x0000019ea3c85840 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000019e4e328af0 relative=0x00000000000001b0
Event: 5.028 Thread 0x0000019ea3c8a070 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000019e4e328af0 relative=0x00000000000001b0
Event: 5.028 Thread 0x0000019ea3c85840 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000019e4e328af0 method=jdk.internal.util.ArraysSupport.vectorizedHashCode(Ljava/lang/Object;IIII)I @ 2 c2
Event: 5.028 Thread 0x0000019ea3c8a070 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000019e4e328af0 method=jdk.internal.util.ArraysSupport.vectorizedHashCode(Ljava/lang/Object;IIII)I @ 2 c2
Event: 5.028 Thread 0x0000019ea3c85840 DEOPT PACKING pc=0x0000019e4e328af0 sp=0x0000007ea1efe000
Event: 5.028 Thread 0x0000019ea3c8a070 DEOPT PACKING pc=0x0000019e4e328af0 sp=0x0000007ea26fe0a0
Event: 5.028 Thread 0x0000019ea3c85840 DEOPT UNPACKING pc=0x0000019e4dbf3aa2 sp=0x0000007ea1efdfb0 mode 2
Event: 5.028 Thread 0x0000019ea3c8a070 DEOPT UNPACKING pc=0x0000019e4dbf3aa2 sp=0x0000007ea26fe050 mode 2

Classes loaded (20 events):
Event: 2.739 Loading class java/lang/invoke/MethodHandleImpl$CountingWrapper
Event: 2.740 Loading class java/lang/invoke/MethodHandleImpl$CountingWrapper done
Event: 2.756 Loading class java/lang/annotation/ElementType
Event: 2.756 Loading class java/lang/annotation/ElementType done
Event: 2.763 Loading class java/nio/channels/Channels$ReadableByteChannelImpl
Event: 2.763 Loading class java/nio/channels/Channels$ReadableByteChannelImpl done
Event: 2.764 Loading class java/nio/channels/NonWritableChannelException
Event: 2.764 Loading class java/nio/channels/NonWritableChannelException done
Event: 2.829 Loading class java/lang/annotation/IncompleteAnnotationException
Event: 2.829 Loading class java/lang/annotation/IncompleteAnnotationException done
Event: 4.988 Loading class java/lang/invoke/MethodHandleImpl$CountingWrapper$1
Event: 4.988 Loading class java/lang/invoke/MethodHandleImpl$CountingWrapper$1 done
Event: 5.035 Loading class sun/nio/cs/ThreadLocalCoders
Event: 5.035 Loading class sun/nio/cs/ThreadLocalCoders done
Event: 5.036 Loading class sun/nio/cs/ThreadLocalCoders$1
Event: 5.036 Loading class sun/nio/cs/ThreadLocalCoders$Cache
Event: 5.036 Loading class sun/nio/cs/ThreadLocalCoders$Cache done
Event: 5.036 Loading class sun/nio/cs/ThreadLocalCoders$1 done
Event: 5.036 Loading class sun/nio/cs/ThreadLocalCoders$2
Event: 5.036 Loading class sun/nio/cs/ThreadLocalCoders$2 done

Classes unloaded (7 events):
Event: 1.739 Thread 0x0000019e9c479680 Unloading class 0x0000019e5b1a2c00 'java/lang/invoke/LambdaForm$MH+0x0000019e5b1a2c00'
Event: 1.739 Thread 0x0000019e9c479680 Unloading class 0x0000019e5b1a2800 'java/lang/invoke/LambdaForm$MH+0x0000019e5b1a2800'
Event: 1.739 Thread 0x0000019e9c479680 Unloading class 0x0000019e5b1a2400 'java/lang/invoke/LambdaForm$MH+0x0000019e5b1a2400'
Event: 1.739 Thread 0x0000019e9c479680 Unloading class 0x0000019e5b1a2000 'java/lang/invoke/LambdaForm$MH+0x0000019e5b1a2000'
Event: 1.739 Thread 0x0000019e9c479680 Unloading class 0x0000019e5b1a1c00 'java/lang/invoke/LambdaForm$BMH+0x0000019e5b1a1c00'
Event: 1.739 Thread 0x0000019e9c479680 Unloading class 0x0000019e5b1a1800 'java/lang/invoke/LambdaForm$DMH+0x0000019e5b1a1800'
Event: 1.739 Thread 0x0000019e9c479680 Unloading class 0x0000019e5b1a0800 'java/lang/invoke/LambdaForm$DMH+0x0000019e5b1a0800'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 2.529 Thread 0x0000019ea3c899e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d55831b0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d55831b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.556 Thread 0x0000019ea3c85840 Implicit null exception at 0x0000019e4e55be26 to 0x0000019e4e55c0f4
Event: 2.556 Thread 0x0000019ea3c85840 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d59e1358}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x00000000d59e1358) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.557 Thread 0x0000019ea3c85840 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d59ec298}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, int, long, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d59ec298) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.617 Thread 0x0000019e4355dff0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6530570}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d6530570) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.618 Thread 0x0000019e4355dff0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d6586ae0}: Found class java.lang.Object, but interface was expected> (0x00000000d6586ae0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 2.619 Thread 0x0000019e4355dff0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d65889e0}: Found class java.lang.Object, but interface was expected> (0x00000000d65889e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 2.630 Thread 0x0000019ea3c85840 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d66e26e8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invoke_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d66e26e8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.739 Thread 0x0000019ea3c88cc0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d690b8f8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000d690b8f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.739 Thread 0x0000019ea3c88cc0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d690f0f8}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000d690f0f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.740 Thread 0x0000019ea3c88cc0 Implicit null exception at 0x0000019e4e3685f6 to 0x0000019e4e36892c
Event: 2.742 Thread 0x0000019ea3c88cc0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6941840}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d6941840) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.745 Thread 0x0000019ea3c85840 Exception <a 'java/io/FileNotFoundException'{0x00000000d6989d78}> (0x00000000d6989d78) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 2.745 Thread 0x0000019ea3c85840 Exception <a 'java/io/FileNotFoundException'{0x00000000d698b5a8}> (0x00000000d698b5a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 2.748 Thread 0x0000019ea3c85840 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d69f73e0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d69f73e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.829 Thread 0x0000019ea3c899e0 Exception <a 'java/lang/NoClassDefFoundError'{0x00000000d6485c98}: javax/enterprise/inject/Typed> (0x00000000d6485c98) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 301]
Event: 4.981 Thread 0x0000019ea3c8a070 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d55efab8}> (0x00000000d55efab8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 4.981 Thread 0x0000019ea3c8a070 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d55f0398}> (0x00000000d55f0398) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 5.038 Thread 0x0000019ea3c8a070 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d64c1900}> (0x00000000d64c1900) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 5.038 Thread 0x0000019ea3c8a070 Exception <a 'java/io/FileNotFoundException'{0x00000000d64c3ac8}> (0x00000000d64c3ac8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 2.689 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 2.733 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 2.736 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 2.756 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 2.758 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 2.797 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 2.800 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 2.817 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 2.817 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 2.836 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 2.839 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 2.874 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 2.877 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 3.877 Executing VM operation: Cleanup
Event: 4.945 Executing VM operation: Cleanup done
Event: 4.957 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 4.978 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 5.002 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 5.005 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 6.012 Executing VM operation: Cleanup

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 2.516 Thread 0x0000019e9c479680 flushing  nmethod 0x0000019e46e8c510
Event: 2.516 Thread 0x0000019e9c479680 flushing  nmethod 0x0000019e46e9ba90
Event: 2.516 Thread 0x0000019e9c479680 flushing  nmethod 0x0000019e46eb7910
Event: 2.516 Thread 0x0000019e9c479680 flushing  nmethod 0x0000019e46eb8d90
Event: 2.516 Thread 0x0000019e9c479680 flushing  nmethod 0x0000019e46eb9690
Event: 2.516 Thread 0x0000019e9c479680 flushing  nmethod 0x0000019e46eba890
Event: 2.516 Thread 0x0000019e9c479680 flushing  nmethod 0x0000019e46ebb490
Event: 2.516 Thread 0x0000019e9c479680 flushing  nmethod 0x0000019e46ebd590
Event: 2.516 Thread 0x0000019e9c479680 flushing  nmethod 0x0000019e46ebda90
Event: 2.516 Thread 0x0000019e9c479680 flushing  nmethod 0x0000019e46ebe610
Event: 2.516 Thread 0x0000019e9c479680 flushing  nmethod 0x0000019e46ebea90
Event: 2.516 Thread 0x0000019e9c479680 flushing  nmethod 0x0000019e46ec0110
Event: 2.516 Thread 0x0000019e9c479680 flushing  nmethod 0x0000019e46ec0410
Event: 2.516 Thread 0x0000019e9c479680 flushing  nmethod 0x0000019e46ec1790
Event: 2.516 Thread 0x0000019e9c479680 flushing  nmethod 0x0000019e46ec2890
Event: 2.516 Thread 0x0000019e9c479680 flushing  nmethod 0x0000019e46ec7e10
Event: 2.516 Thread 0x0000019e9c479680 flushing  nmethod 0x0000019e46f09510
Event: 2.516 Thread 0x0000019e9c479680 flushing  nmethod 0x0000019e46f0a990
Event: 2.516 Thread 0x0000019e9c479680 flushing  nmethod 0x0000019e46f2a810
Event: 2.516 Thread 0x0000019e9c479680 flushing  nmethod 0x0000019e46f32d10

Events (20 events):
Event: 2.389 Thread 0x0000019e4355dff0 Thread added: 0x0000019ea78d6730
Event: 2.389 Thread 0x0000019e4355dff0 Thread added: 0x0000019ea78d8170
Event: 2.389 Thread 0x0000019e4355dff0 Thread added: 0x0000019ea78d5a10
Event: 2.390 Thread 0x0000019e4355dff0 Thread added: 0x0000019ea78d7ae0
Event: 2.390 Thread 0x0000019e4355dff0 Thread added: 0x0000019ea78d7450
Event: 2.390 Thread 0x0000019e4355dff0 Thread added: 0x0000019ea78d8800
Event: 2.393 Thread 0x0000019e4355dff0 Thread added: 0x0000019ea78d60a0
Event: 2.393 Thread 0x0000019e4355dff0 Thread added: 0x0000019ea78d6dc0
Event: 2.393 Thread 0x0000019e4355dff0 Thread added: 0x0000019ea7b6de60
Event: 2.393 Thread 0x0000019e4355dff0 Thread added: 0x0000019ea7b6e4f0
Event: 2.393 Thread 0x0000019e4355dff0 Thread added: 0x0000019ea7b6eb80
Event: 2.413 Thread 0x0000019e4355dff0 Thread added: 0x0000019ea7b6ff30
Event: 2.439 Thread 0x0000019e435c87f0 Thread added: 0x0000019ea82de210
Event: 2.647 Thread 0x0000019e4355dff0 Thread added: 0x0000019ea7b6f210
Event: 2.647 Thread 0x0000019e4355dff0 Thread added: 0x0000019ea7b6f8a0
Event: 4.945 Thread 0x0000019e435c87f0 Thread added: 0x0000019ea3e9e060
Event: 4.945 Thread 0x0000019e435c87f0 Thread added: 0x0000019ea362e220
Event: 4.945 Thread 0x0000019ea3c8a070 Thread added: 0x0000019ea7b705c0
Event: 5.038 Thread 0x0000019ea7b705c0 Thread added: 0x0000019ea7b6d140
Event: 5.039 Thread 0x0000019ea7b6d140 Thread added: 0x0000019ea7b70c50


Dynamic libraries:
0x00007ff7daa60000 - 0x00007ff7daa6e000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff854d20000 - 0x00007ff854f88000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff853e10000 - 0x00007ff853ed9000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff8526d0000 - 0x00007ff852abd000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff8522a0000 - 0x00007ff8523eb000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff82fb20000 - 0x00007ff82fb38000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff8331a0000 - 0x00007ff8331be000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff853430000 - 0x00007ff8535fc000 	C:\WINDOWS\System32\USER32.dll
0x00007ff852040000 - 0x00007ff852067000 	C:\WINDOWS\System32\win32u.dll
0x00007ff831500000 - 0x00007ff83179a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3\COMCTL32.dll
0x00007ff854440000 - 0x00007ff85446b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff854290000 - 0x00007ff854339000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff851f00000 - 0x00007ff852037000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff852070000 - 0x00007ff852113000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff854340000 - 0x00007ff85436f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff84c870000 - 0x00007ff84c87c000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff823400000 - 0x00007ff82348d000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007fffbe5a0000 - 0x00007fffbf330000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff8541d0000 - 0x00007ff854284000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff854380000 - 0x00007ff854426000 	C:\WINDOWS\System32\sechost.dll
0x00007ff854470000 - 0x00007ff854588000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff853140000 - 0x00007ff8531b4000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff850a90000 - 0x00007ff850aee000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff842b20000 - 0x00007ff842b55000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff84b030000 - 0x00007ff84b03b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff850a70000 - 0x00007ff850a84000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff850d30000 - 0x00007ff850d4b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff84c860000 - 0x00007ff84c86a000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff84f070000 - 0x00007ff84f2b1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff853910000 - 0x00007ff853c96000 	C:\WINDOWS\System32\combase.dll
0x00007ff8537b0000 - 0x00007ff853890000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff823260000 - 0x00007ff8232a3000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff8523f0000 - 0x00007ff852489000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff84c810000 - 0x00007ff84c81f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff823980000 - 0x00007ff82399f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff854590000 - 0x00007ff854cda000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff852550000 - 0x00007ff8526c4000 	C:\WINDOWS\System32\wintypes.dll
0x00007ff84faf0000 - 0x00007ff85034b000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff8540a0000 - 0x00007ff854195000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff8538a0000 - 0x00007ff85390a000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff851d80000 - 0x00007ff851daf000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff8238b0000 - 0x00007ff8238c8000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff84c850000 - 0x00007ff84c860000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ff84bc80000 - 0x00007ff84bd9e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff8512c0000 - 0x00007ff85132a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff823890000 - 0x00007ff8238a6000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff839c90000 - 0x00007ff839ca0000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ff81e1c0000 - 0x00007ff81e205000 	C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ff853600000 - 0x00007ff85379e000 	C:\WINDOWS\System32\ole32.dll
0x00007ff851570000 - 0x00007ff85158b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff850c90000 - 0x00007ff850ccb000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff851360000 - 0x00007ff85138b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff851d50000 - 0x00007ff851d76000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ff851590000 - 0x00007ff85159c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff8506d0000 - 0x00007ff850703000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff853f10000 - 0x00007ff853f1a000 	C:\WINDOWS\System32\NSI.dll
0x00007ffffd640000 - 0x00007ffffd689000 	C:\Users\<USER>\AppData\Local\Temp\jna-54154080\jna2595244243525305653.dll
0x00007ff853ee0000 - 0x00007ff853ee8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff84c3c0000 - 0x00007ff84c3df000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ff84c390000 - 0x00007ff84c3b5000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3;c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-54154080

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-4440e0ccc089c3adca2403b8fc107d65-sock
java_class_path (initial): c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-23
PATH=D:\Ӱ��;C;\Program Files\Java\jdk-23\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\ProgramData\chocolatey\bin;C:\Program Files\nodejs\;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;D:\΢�ſ�;�߹���\΢��web�����߹���\dll;;C:\Program Files\Git\cmd;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;d:\Trae��̹���\Trae CN\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\VSCOD\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Java\jdk-23\bin;C:\Users\<USER>\.android\platform-tools-latest-windows\platform-tools;
USERNAME=91668
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4484)
OS uptime: 0 days 14:32 hours

CPU: total 24 (initial active 24) (12 cores per cpu, 2 threads per core) family 6 model 151 stepping 2 microcode 0x2c, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, serialize, rdtscp, rdpid, fsrm, f16c, pku, cet_ibt, cet_ss
Processor Information for processor 0
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 1
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 2
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 3
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 4
  Max Mhz: 2300, Current Mhz: 1506, Mhz Limit: 2300
Processor Information for processor 5
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 6
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 7
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 8
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 9
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 10
  Max Mhz: 2300, Current Mhz: 1506, Mhz Limit: 2300
Processor Information for processor 11
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 12
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 13
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 14
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 15
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 16
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 17
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 18
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 19
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 20
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 21
  Max Mhz: 2300, Current Mhz: 1488, Mhz Limit: 2300
Processor Information for processor 22
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 23
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300

Memory: 4k page, system-wide physical 16121M (1999M free)
TotalPageFile size 28921M (AvailPageFile size 20M)
current process WorkingSet (physical memory assigned to process): 229M, peak: 253M
current process commit charge ("private bytes"): 372M, peak: 397M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
