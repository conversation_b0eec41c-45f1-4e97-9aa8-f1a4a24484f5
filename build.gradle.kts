/**
 * 顶级构建文件，可以在此处添加适用于所有子项目/模块的配置选项
 * Top-level build file where you can add configuration options common to all sub-projects/modules.
 * 
 * 注意：仓库配置已移动到 settings.gradle.kts 文件中
 * Note: Repository configuration has been moved to settings.gradle.kts file
 */

// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id("com.android.application") version "8.10.1" apply false
    id("org.jetbrains.kotlin.android") version "2.0.21" apply false
    id("org.jetbrains.kotlin.plugin.compose") version "2.0.21" apply false
}