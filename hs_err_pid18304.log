#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 32744 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=18304, tid=26896
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-6700ee9bea00a418edc6e7a37df34d5b-sock

Host: 12th Gen Intel(R) Core(TM) i9-12900HX, 24 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4484)
Time: Mon Jul 21 22:27:31 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4484) elapsed time: 309.974742 seconds (0d 0h 5m 9s)

---------------  T H R E A D  ---------------

Current thread (0x000001e670253260):  JavaThread "C1 CompilerThread0" daemon [_thread_in_native, id=26896, stack(0x0000002a74700000,0x0000002a74800000) (1024K)]


Current CompileTask:
C1:309974 11110       3       org.eclipse.core.internal.dtree.DataTreeWriter::writeNode (182 bytes)

Stack: [0x0000002a74700000,0x0000002a74800000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0x198fc6]
V  [jvm.dll+0x198c36]
V  [jvm.dll+0x153873]
V  [jvm.dll+0x1530b8]
V  [jvm.dll+0x153304]
V  [jvm.dll+0x152736]
V  [jvm.dll+0x15450d]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001e67f23b0f0, length=63, elements={
0x000001e67019e480, 0x000001e6797c1610, 0x000001e6797c3530, 0x000001e6797cbf90,
0x000001e6797cb900, 0x000001e6797c91a0, 0x000001e6797c9830, 0x000001e670251130,
0x000001e670253260, 0x000001e6797c8b10, 0x000001e6797c9ec0, 0x000001e6797ca550,
0x000001e6797cabe0, 0x000001e67ce397c0, 0x000001e67ce3a4e0, 0x000001e67ce3bf20,
0x000001e67ce3d2d0, 0x000001e67ce3b200, 0x000001e67ce3cc40, 0x000001e67ce39e50,
0x000001e67ce3f3a0, 0x000001e67ce400c0, 0x000001e67ce40de0, 0x000001e67ce40750,
0x000001e67ce3e680, 0x000001e67ce3dff0, 0x000001e67ce3ed10, 0x000001e67f0cd010,
0x000001e67f0cb5d0, 0x000001e67f0cf0e0, 0x000001e67f0cd6a0, 0x000001e67f0cdd30,
0x000001e67f0cbc60, 0x000001e67f0cea50, 0x000001e67f0ce3c0, 0x000001e67f0cf770,
0x000001e67f0cc980, 0x000001e67f0cc2f0, 0x000001e67f0cfe00, 0x000001e67f0d1840,
0x000001e67f0d11b0, 0x000001e67f0d2560, 0x000001e67f0d0490, 0x000001e67f0d1ed0,
0x000001e67f0d2bf0, 0x000001e67f0d0b20, 0x000001e67e28c9a0, 0x000001e67e28af60,
0x000001e67e28e3e0, 0x000001e67e28fe20, 0x000001e656ff2fe0, 0x000001e656ff15a0,
0x000001e656ff4a20, 0x000001e656ff3670, 0x000001e656ff5dd0, 0x000001e656ff50b0,
0x000001e656ff1c30, 0x000001e65521d270, 0x000001e65521ecb0, 0x000001e655220060,
0x000001e65521d900, 0x000001e67c5daee0, 0x000001e67c5dca20
}

Java Threads: ( => current thread )
  0x000001e67019e480 JavaThread "main"                              [_thread_blocked, id=29928, stack(0x0000002a73c00000,0x0000002a73d00000) (1024K)]
  0x000001e6797c1610 JavaThread "Reference Handler"          daemon [_thread_blocked, id=20264, stack(0x0000002a74000000,0x0000002a74100000) (1024K)]
  0x000001e6797c3530 JavaThread "Finalizer"                  daemon [_thread_blocked, id=30660, stack(0x0000002a74100000,0x0000002a74200000) (1024K)]
  0x000001e6797cbf90 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=32628, stack(0x0000002a74200000,0x0000002a74300000) (1024K)]
  0x000001e6797cb900 JavaThread "Attach Listener"            daemon [_thread_blocked, id=32360, stack(0x0000002a74300000,0x0000002a74400000) (1024K)]
  0x000001e6797c91a0 JavaThread "Service Thread"             daemon [_thread_blocked, id=20864, stack(0x0000002a74400000,0x0000002a74500000) (1024K)]
  0x000001e6797c9830 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=30752, stack(0x0000002a74500000,0x0000002a74600000) (1024K)]
  0x000001e670251130 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=13688, stack(0x0000002a74600000,0x0000002a74700000) (1024K)]
=>0x000001e670253260 JavaThread "C1 CompilerThread0"         daemon [_thread_in_native, id=26896, stack(0x0000002a74700000,0x0000002a74800000) (1024K)]
  0x000001e6797c8b10 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=30508, stack(0x0000002a74900000,0x0000002a74a00000) (1024K)]
  0x000001e6797c9ec0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=15472, stack(0x0000002a74d00000,0x0000002a74e00000) (1024K)]
  0x000001e6797ca550 JavaThread "Active Thread: Equinox Container: 6afb5368-b969-4f27-9002-f8935f6e1d53"        [_thread_blocked, id=10984, stack(0x0000002a75600000,0x0000002a75700000) (1024K)]
  0x000001e6797cabe0 JavaThread "Refresh Thread: Equinox Container: 6afb5368-b969-4f27-9002-f8935f6e1d53" daemon [_thread_blocked, id=28836, stack(0x0000002a74e00000,0x0000002a74f00000) (1024K)]
  0x000001e67ce397c0 JavaThread "Framework Event Dispatcher: Equinox Container: 6afb5368-b969-4f27-9002-f8935f6e1d53" daemon [_thread_blocked, id=12232, stack(0x0000002a74f00000,0x0000002a75000000) (1024K)]
  0x000001e67ce3a4e0 JavaThread "Start Level: Equinox Container: 6afb5368-b969-4f27-9002-f8935f6e1d53" daemon [_thread_blocked, id=31400, stack(0x0000002a75800000,0x0000002a75900000) (1024K)]
  0x000001e67ce3bf20 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=9208, stack(0x0000002a75a00000,0x0000002a75b00000) (1024K)]
  0x000001e67ce3d2d0 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=7396, stack(0x0000002a75c00000,0x0000002a75d00000) (1024K)]
  0x000001e67ce3b200 JavaThread "Worker-JM"                         [_thread_blocked, id=28052, stack(0x0000002a76000000,0x0000002a76100000) (1024K)]
  0x000001e67ce3cc40 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=28844, stack(0x0000002a75b00000,0x0000002a75c00000) (1024K)]
  0x000001e67ce39e50 JavaThread "Worker-1"                          [_thread_blocked, id=16800, stack(0x0000002a76500000,0x0000002a76600000) (1024K)]
  0x000001e67ce3f3a0 JavaThread "Java indexing"              daemon [_thread_blocked, id=23216, stack(0x0000002a76b00000,0x0000002a76c00000) (1024K)]
  0x000001e67ce400c0 JavaThread "Thread-2"                   daemon [_thread_in_native, id=7944, stack(0x0000002a77000000,0x0000002a77100000) (1024K)]
  0x000001e67ce40de0 JavaThread "Thread-3"                   daemon [_thread_in_native, id=26300, stack(0x0000002a77100000,0x0000002a77200000) (1024K)]
  0x000001e67ce40750 JavaThread "Thread-4"                   daemon [_thread_in_native, id=12668, stack(0x0000002a77200000,0x0000002a77300000) (1024K)]
  0x000001e67ce3e680 JavaThread "Thread-5"                   daemon [_thread_in_native, id=29088, stack(0x0000002a77300000,0x0000002a77400000) (1024K)]
  0x000001e67ce3dff0 JavaThread "Thread-6"                   daemon [_thread_in_native, id=8668, stack(0x0000002a77400000,0x0000002a77500000) (1024K)]
  0x000001e67ce3ed10 JavaThread "Thread-7"                   daemon [_thread_in_native, id=13932, stack(0x0000002a77500000,0x0000002a77600000) (1024K)]
  0x000001e67f0cd010 JavaThread "Thread-8"                   daemon [_thread_in_native, id=8632, stack(0x0000002a77600000,0x0000002a77700000) (1024K)]
  0x000001e67f0cb5d0 JavaThread "Thread-9"                   daemon [_thread_in_native, id=6368, stack(0x0000002a77700000,0x0000002a77800000) (1024K)]
  0x000001e67f0cf0e0 JavaThread "Thread-10"                  daemon [_thread_in_native, id=5904, stack(0x0000002a77800000,0x0000002a77900000) (1024K)]
  0x000001e67f0cd6a0 JavaThread "Thread-11"                  daemon [_thread_in_native, id=21860, stack(0x0000002a77900000,0x0000002a77a00000) (1024K)]
  0x000001e67f0cdd30 JavaThread "Thread-12"                  daemon [_thread_in_native, id=26200, stack(0x0000002a77a00000,0x0000002a77b00000) (1024K)]
  0x000001e67f0cbc60 JavaThread "Thread-13"                  daemon [_thread_in_native, id=22708, stack(0x0000002a77b00000,0x0000002a77c00000) (1024K)]
  0x000001e67f0cea50 JavaThread "Thread-14"                  daemon [_thread_in_native, id=8544, stack(0x0000002a77c00000,0x0000002a77d00000) (1024K)]
  0x000001e67f0ce3c0 JavaThread "Thread-15"                  daemon [_thread_in_native, id=27296, stack(0x0000002a77d00000,0x0000002a77e00000) (1024K)]
  0x000001e67f0cf770 JavaThread "Thread-16"                  daemon [_thread_in_native, id=28504, stack(0x0000002a77e00000,0x0000002a77f00000) (1024K)]
  0x000001e67f0cc980 JavaThread "Thread-17"                  daemon [_thread_in_native, id=28544, stack(0x0000002a77f00000,0x0000002a78000000) (1024K)]
  0x000001e67f0cc2f0 JavaThread "Thread-18"                  daemon [_thread_in_native, id=32328, stack(0x0000002a78000000,0x0000002a78100000) (1024K)]
  0x000001e67f0cfe00 JavaThread "Thread-19"                  daemon [_thread_in_native, id=8468, stack(0x0000002a78100000,0x0000002a78200000) (1024K)]
  0x000001e67f0d1840 JavaThread "Thread-20"                  daemon [_thread_in_native, id=27376, stack(0x0000002a78200000,0x0000002a78300000) (1024K)]
  0x000001e67f0d11b0 JavaThread "Thread-21"                  daemon [_thread_in_native, id=31608, stack(0x0000002a78300000,0x0000002a78400000) (1024K)]
  0x000001e67f0d2560 JavaThread "Thread-22"                  daemon [_thread_in_native, id=24444, stack(0x0000002a78400000,0x0000002a78500000) (1024K)]
  0x000001e67f0d0490 JavaThread "Thread-23"                  daemon [_thread_in_native, id=28048, stack(0x0000002a78500000,0x0000002a78600000) (1024K)]
  0x000001e67f0d1ed0 JavaThread "Thread-24"                  daemon [_thread_in_native, id=21652, stack(0x0000002a78600000,0x0000002a78700000) (1024K)]
  0x000001e67f0d2bf0 JavaThread "Thread-25"                  daemon [_thread_in_native, id=29040, stack(0x0000002a78700000,0x0000002a78800000) (1024K)]
  0x000001e67f0d0b20 JavaThread "Thread-26"                  daemon [_thread_in_native, id=7316, stack(0x0000002a78800000,0x0000002a78900000) (1024K)]
  0x000001e67e28c9a0 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=27696, stack(0x0000002a78e00000,0x0000002a78f00000) (1024K)]
  0x000001e67e28af60 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=24668, stack(0x0000002a78f00000,0x0000002a79000000) (1024K)]
  0x000001e67e28e3e0 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=13452, stack(0x0000002a79000000,0x0000002a79100000) (1024K)]
  0x000001e67e28fe20 JavaThread "Timer-0"                           [_thread_blocked, id=29200, stack(0x0000002a79200000,0x0000002a79300000) (1024K)]
  0x000001e656ff2fe0 JavaThread "Timer-1"                           [_thread_blocked, id=4976, stack(0x0000002a78900000,0x0000002a78a00000) (1024K)]
  0x000001e656ff15a0 JavaThread "Worker-5: Periodic workspace save."        [_thread_in_Java, id=24740, stack(0x0000002a78a00000,0x0000002a78b00000) (1024K)]
  0x000001e656ff4a20 JavaThread "Timer-3"                           [_thread_blocked, id=8376, stack(0x0000002a76700000,0x0000002a76800000) (1024K)]
  0x000001e656ff3670 JavaThread "Timer-2"                           [_thread_blocked, id=9228, stack(0x0000002a79600000,0x0000002a79700000) (1024K)]
  0x000001e656ff5dd0 JavaThread "Timer-4"                           [_thread_blocked, id=22108, stack(0x0000002a79800000,0x0000002a79900000) (1024K)]
  0x000001e656ff50b0 JavaThread "Timer-5"                           [_thread_blocked, id=32332, stack(0x0000002a79900000,0x0000002a79a00000) (1024K)]
  0x000001e656ff1c30 JavaThread "Timer-6"                           [_thread_blocked, id=31496, stack(0x0000002a79a00000,0x0000002a79b00000) (1024K)]
  0x000001e65521d270 JavaThread "Timer-7"                           [_thread_blocked, id=31812, stack(0x0000002a79c00000,0x0000002a79d00000) (1024K)]
  0x000001e65521ecb0 JavaThread "Timer-8"                           [_thread_blocked, id=9328, stack(0x0000002a78b00000,0x0000002a78c00000) (1024K)]
  0x000001e655220060 JavaThread "Timer-9"                           [_thread_blocked, id=2060, stack(0x0000002a79500000,0x0000002a79600000) (1024K)]
  0x000001e65521d900 JavaThread "Timer-10"                          [_thread_blocked, id=20016, stack(0x0000002a79b00000,0x0000002a79c00000) (1024K)]
  0x000001e67c5daee0 JavaThread "C1 CompilerThread1"         daemon [_thread_blocked, id=29032, stack(0x0000002a73b00000,0x0000002a73c00000) (1024K)]
  0x000001e67c5dca20 JavaThread "C1 CompilerThread2"         daemon [_thread_blocked, id=8796, stack(0x0000002a75900000,0x0000002a75a00000) (1024K)]
Total: 63

Other Threads:
  0x000001e6797b9a80 VMThread "VM Thread"                           [id=8064, stack(0x0000002a73f00000,0x0000002a74000000) (1024K)]
  0x000001e67813ca00 WatcherThread "VM Periodic Task Thread"        [id=30204, stack(0x0000002a73e00000,0x0000002a73f00000) (1024K)]
  0x000001e6701bc250 WorkerThread "GC Thread#0"                     [id=4744, stack(0x0000002a73d00000,0x0000002a73e00000) (1024K)]
  0x000001e67c580120 WorkerThread "GC Thread#1"                     [id=12880, stack(0x0000002a75000000,0x0000002a75100000) (1024K)]
  0x000001e67c5808c0 WorkerThread "GC Thread#2"                     [id=24884, stack(0x0000002a75100000,0x0000002a75200000) (1024K)]
  0x000001e67cb24090 WorkerThread "GC Thread#3"                     [id=4232, stack(0x0000002a75200000,0x0000002a75300000) (1024K)]
  0x000001e67cb24430 WorkerThread "GC Thread#4"                     [id=30724, stack(0x0000002a75300000,0x0000002a75400000) (1024K)]
  0x000001e67cb247d0 WorkerThread "GC Thread#5"                     [id=30932, stack(0x0000002a75400000,0x0000002a75500000) (1024K)]
  0x000001e67cb24b70 WorkerThread "GC Thread#6"                     [id=21484, stack(0x0000002a75500000,0x0000002a75600000) (1024K)]
  0x000001e67c9c27e0 WorkerThread "GC Thread#7"                     [id=15856, stack(0x0000002a75700000,0x0000002a75800000) (1024K)]
  0x000001e67c9fbbf0 WorkerThread "GC Thread#8"                     [id=13104, stack(0x0000002a74a00000,0x0000002a74b00000) (1024K)]
  0x000001e67c9fca70 WorkerThread "GC Thread#9"                     [id=31224, stack(0x0000002a74b00000,0x0000002a74c00000) (1024K)]
  0x000001e67c9fce10 WorkerThread "GC Thread#10"                    [id=18352, stack(0x0000002a74c00000,0x0000002a74d00000) (1024K)]
  0x000001e67c9fbf90 WorkerThread "GC Thread#11"                    [id=16088, stack(0x0000002a75e00000,0x0000002a75f00000) (1024K)]
  0x000001e67c9fd1b0 WorkerThread "GC Thread#12"                    [id=21588, stack(0x0000002a76100000,0x0000002a76200000) (1024K)]
  0x000001e67c9fd550 WorkerThread "GC Thread#13"                    [id=4664, stack(0x0000002a76200000,0x0000002a76300000) (1024K)]
  0x000001e67d52fc30 WorkerThread "GC Thread#14"                    [id=13436, stack(0x0000002a76300000,0x0000002a76400000) (1024K)]
  0x000001e67d530ab0 WorkerThread "GC Thread#15"                    [id=16520, stack(0x0000002a76600000,0x0000002a76700000) (1024K)]
  0x000001e67d531590 WorkerThread "GC Thread#16"                    [id=22464, stack(0x0000002a76900000,0x0000002a76a00000) (1024K)]
  0x000001e67d52ffd0 WorkerThread "GC Thread#17"                    [id=11116, stack(0x0000002a76a00000,0x0000002a76b00000) (1024K)]
Total: 20

Threads with active compile tasks:
C2 CompilerThread0  312360 11096       4       org.eclipse.core.internal.dtree.AbstractDataTreeNode::assembleWith (502 bytes)
C1 CompilerThread0  312360 11110       3       org.eclipse.core.internal.dtree.DataTreeWriter::writeNode (182 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000001e60f000000-0x000001e60fba0000-0x000001e60fba0000), size 12189696, SharedBaseAddress: 0x000001e60f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001e610000000-0x000001e650000000, reserved size: 1073741824
Narrow klass base: 0x000001e60f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 24 total, 24 available
 Memory: 16121M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 18

Heap:
 PSYoungGen      total 4096K, used 4083K [0x00000000d5580000, 0x00000000d6000000, 0x0000000100000000)
  eden space 2560K, 99% used [0x00000000d5580000,0x00000000d57fce50,0x00000000d5800000)
  from space 1536K, 100% used [0x00000000d5800000,0x00000000d5980000,0x00000000d5980000)
  to   space 4096K, 0% used [0x00000000d5c00000,0x00000000d5c00000,0x00000000d6000000)
 ParOldGen       total 485888K, used 485626K [0x0000000080000000, 0x000000009da80000, 0x00000000d5580000)
  object space 485888K, 99% used [0x0000000080000000,0x000000009da3e8f8,0x000000009da80000)
 Metaspace       used 72390K, committed 74048K, reserved 1114112K
  class space    used 8722K, committed 9472K, reserved 1048576K

Card table byte_map: [0x000001e6735a0000,0x000001e6739b0000] _byte_map_base: 0x000001e6731a0000

Marking Bits: (ParMarkBitMap*) 0x00007fffbf2531f0
 Begin Bits: [0x000001e673c60000, 0x000001e675c60000)
 End Bits:   [0x000001e675c60000, 0x000001e677c60000)

Polling page: 0x000001e66e610000

Metaspace:

Usage:
  Non-class:     62.18 MB used.
      Class:      8.52 MB used.
       Both:     70.69 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      63.06 MB ( 99%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       9.25 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      72.31 MB (  7%) committed. 

Chunk freelists:
   Non-Class:  992.00 KB
       Class:  6.73 MB
        Both:  7.70 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 97.31 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 11.
num_arena_births: 1442.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 1157.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 25.
num_chunks_taken_from_freelist: 4677.
num_chunk_merges: 14.
num_chunk_splits: 2999.
num_chunks_enlarged: 1883.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=10477Kb max_used=10477Kb free=108690Kb
 bounds [0x000001e607ba0000, 0x000001e6085e0000, 0x000001e60f000000]
CodeHeap 'profiled nmethods': size=119104Kb used=21747Kb max_used=21747Kb free=97356Kb
 bounds [0x000001e600000000, 0x000001e601540000, 0x000001e607450000]
CodeHeap 'non-nmethods': size=7488Kb used=2558Kb max_used=3177Kb free=4929Kb
 bounds [0x000001e607450000, 0x000001e607770000, 0x000001e607ba0000]
 total_blobs=11037 nmethods=10294 adapters=647
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 307.652 Thread 0x000001e670253260 nmethod 11091 0x000001e60152c310 code [0x000001e60152c4e0, 0x000001e60152c828]
Event: 307.653 Thread 0x000001e670253260 11092       3       java.lang.invoke.LambdaForm$Name::cloneWithIndex (47 bytes)
Event: 307.655 Thread 0x000001e670253260 nmethod 11092 0x000001e60152c990 code [0x000001e60152cb80, 0x000001e60152d0d8]
Event: 307.718 Thread 0x000001e670253260 11093       3       java.lang.Math::addExact (26 bytes)
Event: 307.719 Thread 0x000001e670253260 nmethod 11093 0x000001e60152d290 code [0x000001e60152d440, 0x000001e60152d638]
Event: 307.833 Thread 0x000001e670253260 11094       3       jdk.internal.misc.Unsafe::loadFence (5 bytes)
Event: 307.833 Thread 0x000001e670253260 nmethod 11094 0x000001e60152d710 code [0x000001e60152d8a0, 0x000001e60152d9c0]
Event: 307.938 Thread 0x000001e670253260 11095       3       java.io.ObjectOutputStream$HandleTable::lookup (56 bytes)
Event: 307.939 Thread 0x000001e670253260 nmethod 11095 0x000001e60152da90 code [0x000001e60152dc80, 0x000001e60152e010]
Event: 307.955 Thread 0x000001e670251130 11096       4       org.eclipse.core.internal.dtree.AbstractDataTreeNode::assembleWith (502 bytes)
Event: 307.955 Thread 0x000001e670253260 11097       1       org.eclipse.core.internal.dtree.AbstractDataTreeNode::isEmptyDelta (2 bytes)
Event: 307.955 Thread 0x000001e670253260 nmethod 11097 0x000001e6085dab10 code [0x000001e6085daca0, 0x000001e6085dad68]
Event: 307.955 Thread 0x000001e670253260 11098       3       org.eclipse.core.internal.dtree.AbstractDataTreeNode::simplifyWithParent (120 bytes)
Event: 307.956 Thread 0x000001e670253260 nmethod 11098 0x000001e60152e210 code [0x000001e60152e440, 0x000001e60152edd8]
Event: 307.956 Thread 0x000001e670253260 11099       3       org.eclipse.core.internal.dtree.DataDeltaNode::simplifyWithParent (71 bytes)
Event: 307.956 Thread 0x000001e670253260 nmethod 11099 0x000001e60152f110 code [0x000001e60152f340, 0x000001e60152fa40]
Event: 307.956 Thread 0x000001e670253260 11104       3       java.io.DataOutputStream::writeInt (28 bytes)
Event: 307.957 Thread 0x000001e670253260 nmethod 11104 0x000001e60152fe10 code [0x000001e601530040, 0x000001e6015307a8]
Event: 307.957 Thread 0x000001e670253260 11110       3       org.eclipse.core.internal.dtree.DataTreeWriter::writeNode (182 bytes)
Event: 307.957 Thread 0x000001e67c5daee0 11100       3       org.eclipse.core.internal.dtree.NoDataDeltaNode::isEmptyDelta (11 bytes)

GC Heap History (20 events):
Event: 49.194 GC heap before
{Heap before GC invocations=749 (full 3):
 PSYoungGen      total 3584K, used 3232K [0x00000000d5580000, 0x00000000d5a00000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000d5580000,0x00000000d5880000,0x00000000d5880000)
  from space 512K, 31% used [0x00000000d5900000,0x00000000d5928000,0x00000000d5980000)
  to   space 512K, 0% used [0x00000000d5980000,0x00000000d5980000,0x00000000d5a00000)
 ParOldGen       total 483840K, used 483452K [0x0000000080000000, 0x000000009d880000, 0x00000000d5580000)
  object space 483840K, 99% used [0x0000000080000000,0x000000009d81f2c8,0x000000009d880000)
 Metaspace       used 71962K, committed 73536K, reserved 1114112K
  class space    used 8681K, committed 9408K, reserved 1048576K
}
Event: 49.195 GC heap after
{Heap after GC invocations=749 (full 3):
 PSYoungGen      total 3584K, used 192K [0x00000000d5580000, 0x00000000d5a00000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5880000)
  from space 512K, 37% used [0x00000000d5980000,0x00000000d59b0000,0x00000000d5a00000)
  to   space 512K, 0% used [0x00000000d5900000,0x00000000d5900000,0x00000000d5980000)
 ParOldGen       total 483840K, used 483452K [0x0000000080000000, 0x000000009d880000, 0x00000000d5580000)
  object space 483840K, 99% used [0x0000000080000000,0x000000009d81f2c8,0x000000009d880000)
 Metaspace       used 71962K, committed 73536K, reserved 1114112K
  class space    used 8681K, committed 9408K, reserved 1048576K
}
Event: 49.240 GC heap before
{Heap before GC invocations=750 (full 3):
 PSYoungGen      total 3584K, used 3264K [0x00000000d5580000, 0x00000000d5a00000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000d5580000,0x00000000d5880000,0x00000000d5880000)
  from space 512K, 37% used [0x00000000d5980000,0x00000000d59b0000,0x00000000d5a00000)
  to   space 512K, 0% used [0x00000000d5900000,0x00000000d5900000,0x00000000d5980000)
 ParOldGen       total 483840K, used 483452K [0x0000000080000000, 0x000000009d880000, 0x00000000d5580000)
  object space 483840K, 99% used [0x0000000080000000,0x000000009d81f2c8,0x000000009d880000)
 Metaspace       used 71962K, committed 73536K, reserved 1114112K
  class space    used 8681K, committed 9408K, reserved 1048576K
}
Event: 49.241 GC heap after
{Heap after GC invocations=750 (full 3):
 PSYoungGen      total 3584K, used 224K [0x00000000d5580000, 0x00000000d5a00000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5880000)
  from space 512K, 43% used [0x00000000d5900000,0x00000000d5938000,0x00000000d5980000)
  to   space 512K, 0% used [0x00000000d5980000,0x00000000d5980000,0x00000000d5a00000)
 ParOldGen       total 483840K, used 483452K [0x0000000080000000, 0x000000009d880000, 0x00000000d5580000)
  object space 483840K, 99% used [0x0000000080000000,0x000000009d81f2c8,0x000000009d880000)
 Metaspace       used 71962K, committed 73536K, reserved 1114112K
  class space    used 8681K, committed 9408K, reserved 1048576K
}
Event: 49.256 GC heap before
{Heap before GC invocations=751 (full 3):
 PSYoungGen      total 3584K, used 3296K [0x00000000d5580000, 0x00000000d5a00000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000d5580000,0x00000000d5880000,0x00000000d5880000)
  from space 512K, 43% used [0x00000000d5900000,0x00000000d5938000,0x00000000d5980000)
  to   space 512K, 0% used [0x00000000d5980000,0x00000000d5980000,0x00000000d5a00000)
 ParOldGen       total 483840K, used 483452K [0x0000000080000000, 0x000000009d880000, 0x00000000d5580000)
  object space 483840K, 99% used [0x0000000080000000,0x000000009d81f2c8,0x000000009d880000)
 Metaspace       used 71962K, committed 73536K, reserved 1114112K
  class space    used 8681K, committed 9408K, reserved 1048576K
}
Event: 49.257 GC heap after
{Heap after GC invocations=751 (full 3):
 PSYoungGen      total 3584K, used 160K [0x00000000d5580000, 0x00000000d5a00000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5880000)
  from space 512K, 31% used [0x00000000d5980000,0x00000000d59a8000,0x00000000d5a00000)
  to   space 512K, 0% used [0x00000000d5900000,0x00000000d5900000,0x00000000d5980000)
 ParOldGen       total 483840K, used 483468K [0x0000000080000000, 0x000000009d880000, 0x00000000d5580000)
  object space 483840K, 99% used [0x0000000080000000,0x000000009d8232c8,0x000000009d880000)
 Metaspace       used 71962K, committed 73536K, reserved 1114112K
  class space    used 8681K, committed 9408K, reserved 1048576K
}
Event: 49.271 GC heap before
{Heap before GC invocations=752 (full 3):
 PSYoungGen      total 3584K, used 3232K [0x00000000d5580000, 0x00000000d5a00000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000d5580000,0x00000000d5880000,0x00000000d5880000)
  from space 512K, 31% used [0x00000000d5980000,0x00000000d59a8000,0x00000000d5a00000)
  to   space 512K, 0% used [0x00000000d5900000,0x00000000d5900000,0x00000000d5980000)
 ParOldGen       total 483840K, used 483468K [0x0000000080000000, 0x000000009d880000, 0x00000000d5580000)
  object space 483840K, 99% used [0x0000000080000000,0x000000009d8232c8,0x000000009d880000)
 Metaspace       used 71962K, committed 73536K, reserved 1114112K
  class space    used 8681K, committed 9408K, reserved 1048576K
}
Event: 49.272 GC heap after
{Heap after GC invocations=752 (full 3):
 PSYoungGen      total 3584K, used 192K [0x00000000d5580000, 0x00000000d5a00000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5880000)
  from space 512K, 37% used [0x00000000d5900000,0x00000000d5930000,0x00000000d5980000)
  to   space 512K, 0% used [0x00000000d5980000,0x00000000d5980000,0x00000000d5a00000)
 ParOldGen       total 483840K, used 483476K [0x0000000080000000, 0x000000009d880000, 0x00000000d5580000)
  object space 483840K, 99% used [0x0000000080000000,0x000000009d8252c8,0x000000009d880000)
 Metaspace       used 71962K, committed 73536K, reserved 1114112K
  class space    used 8681K, committed 9408K, reserved 1048576K
}
Event: 49.279 GC heap before
{Heap before GC invocations=753 (full 3):
 PSYoungGen      total 3584K, used 3264K [0x00000000d5580000, 0x00000000d5a00000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000d5580000,0x00000000d5880000,0x00000000d5880000)
  from space 512K, 37% used [0x00000000d5900000,0x00000000d5930000,0x00000000d5980000)
  to   space 512K, 0% used [0x00000000d5980000,0x00000000d5980000,0x00000000d5a00000)
 ParOldGen       total 483840K, used 483476K [0x0000000080000000, 0x000000009d880000, 0x00000000d5580000)
  object space 483840K, 99% used [0x0000000080000000,0x000000009d8252c8,0x000000009d880000)
 Metaspace       used 71962K, committed 73536K, reserved 1114112K
  class space    used 8681K, committed 9408K, reserved 1048576K
}
Event: 49.280 GC heap after
{Heap after GC invocations=753 (full 3):
 PSYoungGen      total 3584K, used 160K [0x00000000d5580000, 0x00000000d5a00000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5880000)
  from space 512K, 31% used [0x00000000d5980000,0x00000000d59a8000,0x00000000d5a00000)
  to   space 512K, 0% used [0x00000000d5900000,0x00000000d5900000,0x00000000d5980000)
 ParOldGen       total 483840K, used 483476K [0x0000000080000000, 0x000000009d880000, 0x00000000d5580000)
  object space 483840K, 99% used [0x0000000080000000,0x000000009d8252c8,0x000000009d880000)
 Metaspace       used 71962K, committed 73536K, reserved 1114112K
  class space    used 8681K, committed 9408K, reserved 1048576K
}
Event: 49.291 GC heap before
{Heap before GC invocations=754 (full 3):
 PSYoungGen      total 3584K, used 3232K [0x00000000d5580000, 0x00000000d5a00000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000d5580000,0x00000000d5880000,0x00000000d5880000)
  from space 512K, 31% used [0x00000000d5980000,0x00000000d59a8000,0x00000000d5a00000)
  to   space 512K, 0% used [0x00000000d5900000,0x00000000d5900000,0x00000000d5980000)
 ParOldGen       total 483840K, used 483476K [0x0000000080000000, 0x000000009d880000, 0x00000000d5580000)
  object space 483840K, 99% used [0x0000000080000000,0x000000009d8252c8,0x000000009d880000)
 Metaspace       used 71962K, committed 73536K, reserved 1114112K
  class space    used 8681K, committed 9408K, reserved 1048576K
}
Event: 49.292 GC heap after
{Heap after GC invocations=754 (full 3):
 PSYoungGen      total 3584K, used 224K [0x00000000d5580000, 0x00000000d5a00000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5880000)
  from space 512K, 43% used [0x00000000d5900000,0x00000000d5938000,0x00000000d5980000)
  to   space 512K, 0% used [0x00000000d5980000,0x00000000d5980000,0x00000000d5a00000)
 ParOldGen       total 483840K, used 483476K [0x0000000080000000, 0x000000009d880000, 0x00000000d5580000)
  object space 483840K, 99% used [0x0000000080000000,0x000000009d8252c8,0x000000009d880000)
 Metaspace       used 71962K, committed 73536K, reserved 1114112K
  class space    used 8681K, committed 9408K, reserved 1048576K
}
Event: 49.314 GC heap before
{Heap before GC invocations=755 (full 3):
 PSYoungGen      total 3584K, used 3296K [0x00000000d5580000, 0x00000000d5a00000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000d5580000,0x00000000d5880000,0x00000000d5880000)
  from space 512K, 43% used [0x00000000d5900000,0x00000000d5938000,0x00000000d5980000)
  to   space 512K, 0% used [0x00000000d5980000,0x00000000d5980000,0x00000000d5a00000)
 ParOldGen       total 483840K, used 483476K [0x0000000080000000, 0x000000009d880000, 0x00000000d5580000)
  object space 483840K, 99% used [0x0000000080000000,0x000000009d8252c8,0x000000009d880000)
 Metaspace       used 71962K, committed 73536K, reserved 1114112K
  class space    used 8681K, committed 9408K, reserved 1048576K
}
Event: 49.315 GC heap after
{Heap after GC invocations=755 (full 3):
 PSYoungGen      total 3584K, used 128K [0x00000000d5580000, 0x00000000d5a00000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5880000)
  from space 512K, 25% used [0x00000000d5980000,0x00000000d59a0000,0x00000000d5a00000)
  to   space 512K, 0% used [0x00000000d5900000,0x00000000d5900000,0x00000000d5980000)
 ParOldGen       total 483840K, used 483476K [0x0000000080000000, 0x000000009d880000, 0x00000000d5580000)
  object space 483840K, 99% used [0x0000000080000000,0x000000009d8252c8,0x000000009d880000)
 Metaspace       used 71962K, committed 73536K, reserved 1114112K
  class space    used 8681K, committed 9408K, reserved 1048576K
}
Event: 49.421 GC heap before
{Heap before GC invocations=756 (full 3):
 PSYoungGen      total 3584K, used 3199K [0x00000000d5580000, 0x00000000d5a00000, 0x0000000100000000)
  eden space 3072K, 99% used [0x00000000d5580000,0x00000000d587ff88,0x00000000d5880000)
  from space 512K, 25% used [0x00000000d5980000,0x00000000d59a0000,0x00000000d5a00000)
  to   space 512K, 0% used [0x00000000d5900000,0x00000000d5900000,0x00000000d5980000)
 ParOldGen       total 483840K, used 483476K [0x0000000080000000, 0x000000009d880000, 0x00000000d5580000)
  object space 483840K, 99% used [0x0000000080000000,0x000000009d8252c8,0x000000009d880000)
 Metaspace       used 72100K, committed 73728K, reserved 1114112K
  class space    used 8687K, committed 9408K, reserved 1048576K
}
Event: 49.422 GC heap after
{Heap after GC invocations=756 (full 3):
 PSYoungGen      total 3584K, used 512K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5880000)
  from space 512K, 100% used [0x00000000d5900000,0x00000000d5980000,0x00000000d5980000)
  to   space 1024K, 0% used [0x00000000d5980000,0x00000000d5980000,0x00000000d5a80000)
 ParOldGen       total 483840K, used 483492K [0x0000000080000000, 0x000000009d880000, 0x00000000d5580000)
  object space 483840K, 99% used [0x0000000080000000,0x000000009d8292c8,0x000000009d880000)
 Metaspace       used 72100K, committed 73728K, reserved 1114112K
  class space    used 8687K, committed 9408K, reserved 1048576K
}
Event: 49.472 GC heap before
{Heap before GC invocations=757 (full 3):
 PSYoungGen      total 3584K, used 3584K [0x00000000d5580000, 0x00000000d5a80000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000d5580000,0x00000000d5880000,0x00000000d5880000)
  from space 512K, 100% used [0x00000000d5900000,0x00000000d5980000,0x00000000d5980000)
  to   space 1024K, 0% used [0x00000000d5980000,0x00000000d5980000,0x00000000d5a80000)
 ParOldGen       total 483840K, used 483492K [0x0000000080000000, 0x000000009d880000, 0x00000000d5580000)
  object space 483840K, 99% used [0x0000000080000000,0x000000009d8292c8,0x000000009d880000)
 Metaspace       used 72167K, committed 73792K, reserved 1114112K
  class space    used 8697K, committed 9408K, reserved 1048576K
}
Event: 49.473 GC heap after
{Heap after GC invocations=757 (full 3):
 PSYoungGen      total 3584K, used 1005K [0x00000000d5580000, 0x00000000d5b80000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5800000)
  from space 1024K, 98% used [0x00000000d5980000,0x00000000d5a7b630,0x00000000d5a80000)
  to   space 1536K, 0% used [0x00000000d5800000,0x00000000d5800000,0x00000000d5980000)
 ParOldGen       total 483840K, used 483700K [0x0000000080000000, 0x000000009d880000, 0x00000000d5580000)
  object space 483840K, 99% used [0x0000000080000000,0x000000009d85d2c8,0x000000009d880000)
 Metaspace       used 72167K, committed 73792K, reserved 1114112K
  class space    used 8697K, committed 9408K, reserved 1048576K
}
Event: 49.485 GC heap before
{Heap before GC invocations=758 (full 3):
 PSYoungGen      total 3584K, used 3565K [0x00000000d5580000, 0x00000000d5b80000, 0x0000000100000000)
  eden space 2560K, 100% used [0x00000000d5580000,0x00000000d5800000,0x00000000d5800000)
  from space 1024K, 98% used [0x00000000d5980000,0x00000000d5a7b630,0x00000000d5a80000)
  to   space 1536K, 0% used [0x00000000d5800000,0x00000000d5800000,0x00000000d5980000)
 ParOldGen       total 483840K, used 483700K [0x0000000080000000, 0x000000009d880000, 0x00000000d5580000)
  object space 483840K, 99% used [0x0000000080000000,0x000000009d85d2c8,0x000000009d880000)
 Metaspace       used 72167K, committed 73792K, reserved 1114112K
  class space    used 8697K, committed 9408K, reserved 1048576K
}
Event: 49.487 GC heap after
{Heap after GC invocations=758 (full 3):
 PSYoungGen      total 4096K, used 1536K [0x00000000d5580000, 0x00000000d6000000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5800000)
  from space 1536K, 100% used [0x00000000d5800000,0x00000000d5980000,0x00000000d5980000)
  to   space 4096K, 0% used [0x00000000d5c00000,0x00000000d5c00000,0x00000000d6000000)
 ParOldGen       total 485888K, used 485626K [0x0000000080000000, 0x000000009da80000, 0x00000000d5580000)
  object space 485888K, 99% used [0x0000000080000000,0x000000009da3e8f8,0x000000009da80000)
 Metaspace       used 72167K, committed 73792K, reserved 1114112K
  class space    used 8697K, committed 9408K, reserved 1048576K
}

Dll operation events (15 events):
Event: 0.006 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.024 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.069 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.072 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.073 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.075 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.088 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.134 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 1.241 Loaded shared library C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 4.656 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-54154080\jna1392549611744322343.dll
Event: 9.153 Loaded shared library D:\gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
Event: 11.181 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
Event: 11.185 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll
Event: 16.421 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\extnet.dll
Event: 19.199 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 307.955 Thread 0x000001e656ff15a0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001e6085ba2c0 relative=0x0000000000001c60
Event: 307.955 Thread 0x000001e656ff15a0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001e6085ba2c0 method=org.eclipse.core.internal.dtree.AbstractDataTreeNode.assembleWith([Lorg/eclipse/core/internal/dtree/AbstractDataTreeNode;[Lorg/eclipse/core/internal/dtree/AbstractDataTr
Event: 307.955 Thread 0x000001e656ff15a0 DEOPT PACKING pc=0x000001e6085ba2c0 sp=0x0000002a78afe830
Event: 307.955 Thread 0x000001e656ff15a0 DEOPT UNPACKING pc=0x000001e6074a3aa2 sp=0x0000002a78afe7f0 mode 2
Event: 307.955 Thread 0x000001e656ff15a0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001e6085ba2c0 relative=0x0000000000001c60
Event: 307.955 Thread 0x000001e656ff15a0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001e6085ba2c0 method=org.eclipse.core.internal.dtree.AbstractDataTreeNode.assembleWith([Lorg/eclipse/core/internal/dtree/AbstractDataTreeNode;[Lorg/eclipse/core/internal/dtree/AbstractDataTr
Event: 307.955 Thread 0x000001e656ff15a0 DEOPT PACKING pc=0x000001e6085ba2c0 sp=0x0000002a78afe640
Event: 307.955 Thread 0x000001e656ff15a0 DEOPT UNPACKING pc=0x000001e6074a3aa2 sp=0x0000002a78afe600 mode 2
Event: 307.955 Thread 0x000001e656ff15a0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001e6085ba2c0 relative=0x0000000000001c60
Event: 307.955 Thread 0x000001e656ff15a0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001e6085ba2c0 method=org.eclipse.core.internal.dtree.AbstractDataTreeNode.assembleWith([Lorg/eclipse/core/internal/dtree/AbstractDataTreeNode;[Lorg/eclipse/core/internal/dtree/AbstractDataTr
Event: 307.955 Thread 0x000001e656ff15a0 DEOPT PACKING pc=0x000001e6085ba2c0 sp=0x0000002a78afeb30
Event: 307.955 Thread 0x000001e656ff15a0 DEOPT UNPACKING pc=0x000001e6074a3aa2 sp=0x0000002a78afeaf0 mode 2
Event: 307.956 Thread 0x000001e656ff15a0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000001e6084c5d28 relative=0x0000000000000888
Event: 307.956 Thread 0x000001e656ff15a0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000001e6084c5d28 method=java.io.DataOutputStream.writeInt(I)V @ 19 c2
Event: 307.956 Thread 0x000001e656ff15a0 DEOPT PACKING pc=0x000001e6084c5d28 sp=0x0000002a78afef00
Event: 307.956 Thread 0x000001e656ff15a0 DEOPT UNPACKING pc=0x000001e6074a3aa2 sp=0x0000002a78afeed8 mode 2
Event: 307.956 Thread 0x000001e656ff15a0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000001e6084c5d28 relative=0x0000000000000888
Event: 307.956 Thread 0x000001e656ff15a0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000001e6084c5d28 method=java.io.DataOutputStream.writeInt(I)V @ 19 c2
Event: 307.956 Thread 0x000001e656ff15a0 DEOPT PACKING pc=0x000001e6084c5d28 sp=0x0000002a78afee90
Event: 307.956 Thread 0x000001e656ff15a0 DEOPT UNPACKING pc=0x000001e6074a3aa2 sp=0x0000002a78afee68 mode 2

Classes loaded (20 events):
Event: 45.245 Loading class java/util/concurrent/ConcurrentLinkedQueue$Itr
Event: 45.245 Loading class java/util/concurrent/ConcurrentLinkedQueue$Itr done
Event: 45.252 Loading class java/io/ObjectInputStream$Caches
Event: 45.252 Loading class java/io/ObjectInputStream$Caches done
Event: 45.252 Loading class java/io/ObjectInputStream$Caches$1
Event: 45.252 Loading class java/io/ObjectInputStream$Caches$1 done
Event: 48.187 Loading class sun/reflect/generics/tree/IntSignature
Event: 48.188 Loading class sun/reflect/generics/tree/IntSignature done
Event: 49.464 Loading class java/util/concurrent/ForkJoinTask$AdaptedCallable
Event: 49.464 Loading class java/util/concurrent/ForkJoinTask$AdaptedCallable done
Event: 307.762 Loading class java/util/stream/Nodes$CollectorTask$OfRef
Event: 307.778 Loading class java/util/stream/Nodes$CollectorTask
Event: 307.797 Loading class java/util/stream/Nodes$CollectorTask done
Event: 307.797 Loading class java/util/stream/Nodes$CollectorTask$OfRef done
Event: 307.809 Loading class java/util/function/LongFunction
Event: 307.809 Loading class java/util/function/LongFunction done
Event: 307.825 Loading class java/util/stream/Nodes$ConcNode
Event: 307.825 Loading class java/util/stream/Nodes$AbstractConcNode
Event: 307.826 Loading class java/util/stream/Nodes$AbstractConcNode done
Event: 307.826 Loading class java/util/stream/Nodes$ConcNode done

Classes unloaded (7 events):
Event: 5.023 Thread 0x000001e6797b9a80 Unloading class 0x000001e61019c400 'java/lang/invoke/LambdaForm$MH+0x000001e61019c400'
Event: 5.023 Thread 0x000001e6797b9a80 Unloading class 0x000001e61019c000 'java/lang/invoke/LambdaForm$MH+0x000001e61019c000'
Event: 5.023 Thread 0x000001e6797b9a80 Unloading class 0x000001e61019bc00 'java/lang/invoke/LambdaForm$MH+0x000001e61019bc00'
Event: 5.023 Thread 0x000001e6797b9a80 Unloading class 0x000001e61019b800 'java/lang/invoke/LambdaForm$MH+0x000001e61019b800'
Event: 5.023 Thread 0x000001e6797b9a80 Unloading class 0x000001e61019b400 'java/lang/invoke/LambdaForm$BMH+0x000001e61019b400'
Event: 5.023 Thread 0x000001e6797b9a80 Unloading class 0x000001e61019b000 'java/lang/invoke/LambdaForm$DMH+0x000001e61019b000'
Event: 5.023 Thread 0x000001e6797b9a80 Unloading class 0x000001e61019a000 'java/lang/invoke/LambdaForm$DMH+0x000001e61019a000'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 47.929 Thread 0x000001e655220060 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d57db980}: static Lorg/gradle/internal/build/event/types/DefaultTaskDescriptor;.<clinit>()V> (0x00000000d57db980) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1111]
Event: 47.935 Thread 0x000001e655220060 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d58199a0}: static Lorg/gradle/internal/build/event/types/DefaultTaskFinishedProgressEvent;.<clinit>()V> (0x00000000d58199a0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1111]
Event: 47.936 Thread 0x000001e655220060 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d58331b8}: static Lorg/gradle/internal/build/event/types/AbstractTaskResult;.<clinit>()V> (0x00000000d58331b8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1111]
Event: 47.937 Thread 0x000001e655220060 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5842610}: static Lorg/gradle/internal/build/event/types/DefaultTaskSuccessResult;.<clinit>()V> (0x00000000d5842610) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1111]
Event: 48.611 Thread 0x000001e67ce3d960 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5800788}> (0x00000000d5800788) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 48.611 Thread 0x000001e67ce3d960 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5801068}> (0x00000000d5801068) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 48.612 Thread 0x000001e67ce3d960 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5803158}> (0x00000000d5803158) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 48.612 Thread 0x000001e67ce3d960 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5803ce8}> (0x00000000d5803ce8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 48.616 Thread 0x000001e67ce3d960 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5844158}> (0x00000000d5844158) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 48.616 Thread 0x000001e67ce3d960 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5844a38}> (0x00000000d5844a38) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 48.617 Thread 0x000001e67ce3d960 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5846718}> (0x00000000d5846718) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 48.617 Thread 0x000001e67ce3d960 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d58472a8}> (0x00000000d58472a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 48.620 Thread 0x000001e67ce3d960 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d587ca50}> (0x00000000d587ca50) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 48.620 Thread 0x000001e67ce3d960 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d587d330}> (0x00000000d587d330) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 48.621 Thread 0x000001e67ce3d960 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d587f010}> (0x00000000d587f010) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 48.621 Thread 0x000001e67ce3d960 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d587fba0}> (0x00000000d587fba0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 48.987 Thread 0x000001e67e28c310 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d57ef788}> (0x00000000d57ef788) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 48.987 Thread 0x000001e67e28c310 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d57fce30}> (0x00000000d57fce30) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 49.451 Thread 0x000001e67ce3c5b0 Implicit null exception at 0x000001e6082b3793 to 0x000001e6082b5500
Event: 49.463 Thread 0x000001e656ff15a0 Implicit null exception at 0x000001e6082af0ac to 0x000001e6082b0aac

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 49.315 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 49.421 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 49.422 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 49.472 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 49.473 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 49.485 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 49.487 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 50.495 Executing VM operation: Cleanup
Event: 50.495 Executing VM operation: Cleanup done
Event: 60.178 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 60.178 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 60.180 Executing VM operation: RendezvousGCThreads
Event: 60.180 Executing VM operation: RendezvousGCThreads done
Event: 305.698 Executing VM operation: Cleanup
Event: 305.698 Executing VM operation: Cleanup done
Event: 307.713 Executing VM operation: Cleanup
Event: 307.714 Executing VM operation: Cleanup done
Event: 307.952 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 307.953 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 308.967 Executing VM operation: Cleanup

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 11.927 Thread 0x000001e6797b9a80 flushing  nmethod 0x000001e600984d10
Event: 11.927 Thread 0x000001e6797b9a80 flushing  nmethod 0x000001e600985c10
Event: 11.927 Thread 0x000001e6797b9a80 flushing  nmethod 0x000001e600986490
Event: 11.927 Thread 0x000001e6797b9a80 flushing  nmethod 0x000001e600988b90
Event: 11.927 Thread 0x000001e6797b9a80 flushing  nmethod 0x000001e600996a10
Event: 11.927 Thread 0x000001e6797b9a80 flushing  nmethod 0x000001e60099a490
Event: 11.927 Thread 0x000001e6797b9a80 flushing  nmethod 0x000001e6009c8910
Event: 11.927 Thread 0x000001e6797b9a80 flushing  nmethod 0x000001e6009e3010
Event: 11.927 Thread 0x000001e6797b9a80 flushing  nmethod 0x000001e6009f9610
Event: 11.927 Thread 0x000001e6797b9a80 flushing  nmethod 0x000001e6009fa010
Event: 11.927 Thread 0x000001e6797b9a80 flushing  nmethod 0x000001e6009fa490
Event: 11.927 Thread 0x000001e6797b9a80 flushing  nmethod 0x000001e6009fa890
Event: 11.927 Thread 0x000001e6797b9a80 flushing  nmethod 0x000001e6009fc390
Event: 11.927 Thread 0x000001e6797b9a80 flushing  nmethod 0x000001e6009fdc90
Event: 11.927 Thread 0x000001e6797b9a80 flushing  nmethod 0x000001e600a00410
Event: 11.927 Thread 0x000001e6797b9a80 flushing  nmethod 0x000001e600a03c90
Event: 11.927 Thread 0x000001e6797b9a80 flushing  nmethod 0x000001e600a0b290
Event: 11.927 Thread 0x000001e6797b9a80 flushing osr nmethod 0x000001e600a11610
Event: 11.928 Thread 0x000001e6797b9a80 flushing  nmethod 0x000001e600a1f090
Event: 11.928 Thread 0x000001e6797b9a80 flushing  nmethod 0x000001e600a44b90

Events (20 events):
Event: 49.429 Thread 0x000001e67c5dec30 Thread added: 0x000001e67c5db5b0
Event: 51.262 Thread 0x000001e67c5db5b0 Thread exited: 0x000001e67c5db5b0
Event: 51.262 Thread 0x000001e67c5dca20 Thread exited: 0x000001e67c5dca20
Event: 51.262 Thread 0x000001e67c5dc350 Thread exited: 0x000001e67c5dc350
Event: 51.262 Thread 0x000001e67c5dec30 Thread exited: 0x000001e67c5dec30
Event: 56.415 Thread 0x000001e67c5daee0 Thread exited: 0x000001e67c5daee0
Event: 77.547 Thread 0x000001e656ff0880 Thread exited: 0x000001e656ff0880
Event: 77.547 Thread 0x000001e656ff3d00 Thread exited: 0x000001e656ff3d00
Event: 77.547 Thread 0x000001e656ff01f0 Thread exited: 0x000001e656ff01f0
Event: 109.481 Thread 0x000001e67ce3d960 Thread exited: 0x000001e67ce3d960
Event: 109.481 Thread 0x000001e67e28c310 Thread exited: 0x000001e67e28c310
Event: 109.481 Thread 0x000001e67ce3c5b0 Thread exited: 0x000001e67ce3c5b0
Event: 109.481 Thread 0x000001e67ce3fa30 Thread exited: 0x000001e67ce3fa30
Event: 109.498 Thread 0x000001e656ff6460 Thread exited: 0x000001e656ff6460
Event: 169.513 Thread 0x000001e656ff4390 Thread exited: 0x000001e656ff4390
Event: 229.527 Thread 0x000001e656ff0f10 Thread exited: 0x000001e656ff0f10
Event: 289.531 Thread 0x000001e656ff7180 Thread exited: 0x000001e656ff7180
Event: 307.733 Thread 0x000001e656ff15a0 Thread added: 0x000001e65521cbe0
Event: 307.834 Thread 0x000001e65521cbe0 Thread exited: 0x000001e65521cbe0
Event: 307.957 Thread 0x000001e670253260 Thread added: 0x000001e67c5daee0


Dynamic libraries:
0x00007ff7daa60000 - 0x00007ff7daa6e000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff854d20000 - 0x00007ff854f88000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff853e10000 - 0x00007ff853ed9000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff8526d0000 - 0x00007ff852abd000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff8522a0000 - 0x00007ff8523eb000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff8331a0000 - 0x00007ff8331be000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff82fb20000 - 0x00007ff82fb38000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff853430000 - 0x00007ff8535fc000 	C:\WINDOWS\System32\USER32.dll
0x00007ff852040000 - 0x00007ff852067000 	C:\WINDOWS\System32\win32u.dll
0x00007ff831500000 - 0x00007ff83179a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3\COMCTL32.dll
0x00007ff854440000 - 0x00007ff85446b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff854290000 - 0x00007ff854339000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff851f00000 - 0x00007ff852037000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff852070000 - 0x00007ff852113000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff854340000 - 0x00007ff85436f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff84c870000 - 0x00007ff84c87c000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff823400000 - 0x00007ff82348d000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007fffbe5a0000 - 0x00007fffbf330000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff8541d0000 - 0x00007ff854284000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff854380000 - 0x00007ff854426000 	C:\WINDOWS\System32\sechost.dll
0x00007ff854470000 - 0x00007ff854588000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff853140000 - 0x00007ff8531b4000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff850a90000 - 0x00007ff850aee000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff842b20000 - 0x00007ff842b55000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff84b030000 - 0x00007ff84b03b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff850a70000 - 0x00007ff850a84000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff850d30000 - 0x00007ff850d4b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff84c860000 - 0x00007ff84c86a000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff84f070000 - 0x00007ff84f2b1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff853910000 - 0x00007ff853c96000 	C:\WINDOWS\System32\combase.dll
0x00007ff8537b0000 - 0x00007ff853890000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff823260000 - 0x00007ff8232a3000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff8523f0000 - 0x00007ff852489000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff839d50000 - 0x00007ff839d5f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff823980000 - 0x00007ff82399f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff854590000 - 0x00007ff854cda000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff852550000 - 0x00007ff8526c4000 	C:\WINDOWS\System32\wintypes.dll
0x00007ff84faf0000 - 0x00007ff85034b000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff8540a0000 - 0x00007ff854195000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff8538a0000 - 0x00007ff85390a000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff851d80000 - 0x00007ff851daf000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff8238b0000 - 0x00007ff8238c8000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff84c850000 - 0x00007ff84c860000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ff84bc80000 - 0x00007ff84bd9e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff8512c0000 - 0x00007ff85132a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff823890000 - 0x00007ff8238a6000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff839c90000 - 0x00007ff839ca0000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ff81e1c0000 - 0x00007ff81e205000 	C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ff853600000 - 0x00007ff85379e000 	C:\WINDOWS\System32\ole32.dll
0x00007ff851570000 - 0x00007ff85158b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff850c90000 - 0x00007ff850ccb000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff851360000 - 0x00007ff85138b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff851d50000 - 0x00007ff851d76000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ff851590000 - 0x00007ff85159c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff8506d0000 - 0x00007ff850703000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff853f10000 - 0x00007ff853f1a000 	C:\WINDOWS\System32\NSI.dll
0x00007ffffd510000 - 0x00007ffffd559000 	C:\Users\<USER>\AppData\Local\Temp\jna-54154080\jna1392549611744322343.dll
0x00007ff853ee0000 - 0x00007ff853ee8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff84c3c0000 - 0x00007ff84c3df000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ff84c390000 - 0x00007ff84c3b5000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007fffc9620000 - 0x00007fffc9647000 	D:\gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00007ff82ffc0000 - 0x00007ff82ffca000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
0x00007ff82fe40000 - 0x00007ff82fe4b000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll
0x00007ff82de40000 - 0x00007ff82de49000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\extnet.dll
0x00007ff82fcf0000 - 0x00007ff82fcfe000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\sunmscapi.dll
0x00007ff852120000 - 0x00007ff852297000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ff851790000 - 0x00007ff8517c0000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ff851740000 - 0x00007ff85177f000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ff82bb10000 - 0x00007ff82bb18000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3;c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-54154080;D:\gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-6700ee9bea00a418edc6e7a37df34d5b-sock
java_class_path (initial): c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\8c70b6a462cad5821dcba30696b69b6f\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-23
PATH=D:\Ӱ��;C;\Program Files\Java\jdk-23\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\ProgramData\chocolatey\bin;C:\Program Files\nodejs\;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;D:\΢�ſ�;�߹���\΢��web�����߹���\dll;;C:\Program Files\Git\cmd;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;d:\Trae��̹���\Trae CN\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\VSCOD\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Java\jdk-23\bin;C:\Users\<USER>\.android\platform-tools-latest-windows\platform-tools;
USERNAME=91668
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4484)
OS uptime: 0 days 14:31 hours

CPU: total 24 (initial active 24) (12 cores per cpu, 2 threads per core) family 6 model 151 stepping 2 microcode 0x2c, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, serialize, rdtscp, rdpid, fsrm, f16c, pku, cet_ibt, cet_ss
Processor Information for processor 0
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 1
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 2
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 3
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 4
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 5
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 6
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 7
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 8
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 9
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 10
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 11
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 12
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 13
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 14
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 15
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 16
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 17
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 18
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 19
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 20
  Max Mhz: 2300, Current Mhz: 1488, Mhz Limit: 2300
Processor Information for processor 21
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 22
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300
Processor Information for processor 23
  Max Mhz: 2300, Current Mhz: 2300, Mhz Limit: 2300

Memory: 4k page, system-wide physical 16121M (663M free)
TotalPageFile size 28921M (AvailPageFile size 448M)
current process WorkingSet (physical memory assigned to process): 114M, peak: 871M
current process commit charge ("private bytes"): 830M, peak: 864M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
