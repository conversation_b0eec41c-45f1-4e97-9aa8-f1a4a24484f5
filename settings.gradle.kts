/**
 * Gradle设置文件 - 配置插件管理和依赖解析
 * Gradle settings file - Configure plugin management and dependency resolution
 * 
 * 网络优化配置：
 * - 配置了阿里云和华为云镜像源以提高下载速度
 * - 添加了原始仓库作为备用选项
 * Network optimization configuration:
 * - Configured Alibaba Cloud and Huawei Cloud mirror sources for better download speed
 * - Added original repositories as backup options
 */

pluginManagement {
    repositories {
        // 阿里云插件镜像源（优先使用）
        // Alibaba Cloud plugin mirror sources (priority use)
        maven { 
            url = uri("https://maven.aliyun.com/repository/gradle-plugin")
            name = "Aliyun Gradle Plugin Repository"
        }
        maven { 
            url = uri("https://maven.aliyun.com/repository/google")
            name = "Aliyun Google Repository"
        }
        maven { 
            url = uri("https://maven.aliyun.com/repository/central")
            name = "Aliyun Central Repository"
        }
        
        // 华为镜像源（备用选项）
        // Huawei mirror sources (backup option)
        maven { 
            url = uri("https://repo.huaweicloud.com/repository/maven/")
            name = "Huawei Maven Repository"
        }
        
        // 原始仓库（备用选项）
        // Original repositories (backup options)
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        // 阿里云依赖镜像源（优先使用）
        // Alibaba Cloud dependency mirror sources (priority use)
        maven { 
            url = uri("https://maven.aliyun.com/repository/google")
            name = "Aliyun Google Repository"
        }
        maven { 
            url = uri("https://maven.aliyun.com/repository/central")
            name = "Aliyun Central Repository"
        }
        
        // 华为镜像源（备用选项）
        // Huawei mirror sources (backup option)
        maven { 
            url = uri("https://repo.huaweicloud.com/repository/maven/")
            name = "Huawei Maven Repository"
        }
        
        // 原始仓库（备用选项）
        // Original repositories (backup options)
        google()
        mavenCentral()
    }
}

rootProject.name = "My Application5"
include(":app")
