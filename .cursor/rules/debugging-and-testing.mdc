# 调试和测试指南

## 调试工具

### 1. 内置诊断工具
护眼应用内置了完整的诊断工具，位于主界面的"🔧 诊断和测试"部分：

- **权限测试**：检查系统权限状态
- **传感器测试**：检测光传感器功能
- **服务状态检查**：监控后台服务运行状态
- **系统冲突检测**：识别亮度调节冲突
- **六级防频闪系统诊断**：检测防频闪系统状态
- **户外可视性测试**：验证户外环境检测和亮度调节
- **干眼症模式测试**：测试极低亮度功能

### 2. 日志系统
应用使用Android Log系统记录详细日志：

```kotlin
// 日志标签定义
companion object {
    private const val TAG = "EyeCareApp"
    private const val TAG_SERVICE = "EyeCareService"
    private const val TAG_SENSOR = "LightSensor"
    private const val TAG_BRIGHTNESS = "BrightnessCtrl"
    private const val TAG_ANTI_FLICKER = "AntiFlickerSystem"
    private const val TAG_OUTDOOR = "OutdoorVisibility"
    private const val TAG_DRY_EYE = "DryEyeMode"
}

// 日志记录示例
Log.d(TAG, "护眼模式已开启：${mode.name}")
Log.e(TAG, "亮度调节失败", exception)
Log.i(TAG, "传感器数据：${lightLevel} lux")
Log.d(TAG_ANTI_FLICKER, "六级防频闪系统状态：${antiFlickerState}")
Log.i(TAG_OUTDOOR, "户外环境检测：${environment}")
Log.d(TAG_DRY_EYE, "干眼症极低亮度模式：${brightnessLevel}")
```

### 3. 状态监控
实时监控关键组件状态：

- **服务运行状态**：前台/后台/已停止
- **传感器状态**：可用/不可用/错误
- **权限状态**：已授予/未授予/部分授予
- **亮度调节状态**：成功/失败/冲突
- **六级防频闪系统状态**：级别1-6/异常/禁用
- **户外可视性状态**：室内/户外/检测中
- **干眼症模式状态**：正常/极低亮度/舒适度

## 测试方法

### 1. 六级防频闪系统测试

#### 防频闪算法验证
```kotlin
// 测试六级防频闪算法
fun testAntiFlickerAlgorithm() {
    val antiFlickerSystem = SixLevelAntiFlickerSystem()
    
    // 测试第一级：传感器数据平滑
    val rawData = listOf(100f, 105f, 98f, 102f, 99f)
    val smoothedData = rawData.map { antiFlickerSystem.smoothSensorData(it) }
    assert(smoothedData.all { it in 95f..110f })
    
    // 测试第二级：亮度计算平滑
    val calculatedBrightness = antiFlickerSystem.smoothBrightnessCalculation(0.5f, EyeCareMode.STANDARD)
    assert(calculatedBrightness in 0.45f..0.55f)
    
    // 测试第三级：微小变化过滤
    val shouldUpdate = antiFlickerSystem.filterTinyChanges(0.51f, 0.50f)
    assert(!shouldUpdate) // 1%变化应该被过滤
    
    val shouldUpdate2 = antiFlickerSystem.filterTinyChanges(0.60f, 0.50f)
    assert(shouldUpdate2) // 10%变化应该执行调节
    
    // 测试第四级：步长限制
    val limitedBrightness = antiFlickerSystem.limitStepSize(0.80f, 0.50f)
    assert(limitedBrightness == 0.60f) // 应该限制在10%步长内
    
    // 测试第五级：更新间隔控制
    val canUpdate = antiFlickerSystem.controlUpdateInterval()
    assert(canUpdate in listOf(true, false)) // 应该根据时间间隔决定
    
    // 测试第六级：环境自适应
    val adaptedParams = antiFlickerSystem.adaptToEnvironment(1000f)
    assert(adaptedParams.smoothingFactor in 0.02f..0.10f)
}
```

#### 防频闪效果测试
```kotlin
// 测试防频闪效果
fun testAntiFlickerEffect() {
    val brightnessController = BrightnessController(context)
    val testBrightness = mutableListOf<Float>()
    
    // 模拟频繁的亮度变化
    repeat(100) { i ->
        val targetBrightness = 0.3f + (i % 10) * 0.01f
        val success = brightnessController.updateBrightnessWithAntiFlicker(
            targetBrightness, EyeCareMode.STANDARD
        )
        if (success) {
            testBrightness.add(brightnessController.currentBrightness)
        }
        Thread.sleep(100)
    }
    
    // 验证亮度变化是否平滑
    val changes = testBrightness.zipWithNext { a, b -> abs(b - a) }
    val maxChange = changes.maxOrNull() ?: 0f
    assert(maxChange <= 0.1f) // 最大变化不应超过10%
    
    // 验证变化频率是否合理
    val changeCount = changes.count { it > 0.005f }
    assert(changeCount <= 20) // 有效变化次数不应过多
}
```

### 2. 户外可视性测试

#### 户外环境检测测试
```kotlin
// 测试户外环境检测
fun testOutdoorEnvironmentDetection() {
    val outdoorSystem = OutdoorVisibilitySystem()
    
    // 测试不同光照强度下的环境检测
    val testCases = listOf(
        50f to Environment.INDOOR_DARK,
        200f to Environment.INDOOR_BRIGHT,
        800f to Environment.OUTDOOR
    )
    
    testCases.forEach { (lightLevel, expectedEnvironment) ->
        val detectedEnvironment = outdoorSystem.detectEnvironment(lightLevel)
        assert(detectedEnvironment == expectedEnvironment)
    }
}
```

#### 户外亮度计算测试
```kotlin
// 测试户外亮度计算
fun testOutdoorBrightnessCalculation() {
    val outdoorSystem = OutdoorVisibilitySystem()
    
    // 测试标准模式户外亮度
    val standardBrightness = outdoorSystem.calculateOutdoorBrightness(1000f, EyeCareMode.STANDARD)
    assert(standardBrightness in 0.75f..0.95f)
    
    // 测试超敏感模式户外亮度
    val ultraSensitiveBrightness = outdoorSystem.calculateOutdoorBrightness(1000f, EyeCareMode.ULTRA_SENSITIVE)
    assert(ultraSensitiveBrightness in 0.40f..0.55f)
    
    // 测试夜间模式户外亮度
    val nightBrightness = outdoorSystem.calculateOutdoorBrightness(1000f, EyeCareMode.NIGHT)
    assert(nightBrightness in 0.20f..0.30f)
}
```

### 3. 干眼症极低亮度测试

#### 极低亮度功能测试
```kotlin
// 测试干眼症极低亮度功能
fun testDryEyeLowBrightness() {
    val dryEyeSystem = DryEyeLowBrightnessSystem()
    
    // 测试极低亮度计算
    val nightBrightness = dryEyeSystem.calculateUltraLowBrightness(EyeCareMode.NIGHT)
    assert(nightBrightness == 0.001f) // 0.1%
    
    val ultraSensitiveBrightness = dryEyeSystem.calculateUltraLowBrightness(EyeCareMode.ULTRA_SENSITIVE)
    assert(ultraSensitiveBrightness == 0.002f) // 0.2%
    
    val standardBrightness = dryEyeSystem.calculateUltraLowBrightness(EyeCareMode.STANDARD)
    assert(standardBrightness == 0.003f) // 0.3%
}
```

#### 渐进式调节测试
```kotlin
// 测试渐进式亮度调节
fun testGradualBrightnessReduction() {
    val dryEyeSystem = DryEyeLowBrightnessSystem()
    
    var currentBrightness = 0.5f
    val targetBrightness = 0.001f // 0.1%
    
    repeat(50) { // 最多50次调节
        currentBrightness = dryEyeSystem.gradualBrightnessReduction(targetBrightness, currentBrightness)
        if (currentBrightness <= targetBrightness) break
    }
    
    assert(currentBrightness <= targetBrightness)
    assert(currentBrightness >= targetBrightness - 0.01f) // 允许1%误差
}
```

### 4. 功能测试

#### 护眼模式测试
```kotlin
// 测试不同护眼模式
fun testEyeCareModes() {
    val modes = listOf(
        EyeCareMode.STANDARD,
        EyeCareMode.NIGHT,
        EyeCareMode.ULTRA_SENSITIVE
    )
    
    modes.forEach { mode ->
        brightnessController.setEyeCareMode(mode)
        // 验证亮度调节效果
        assert(brightnessController.currentMode == mode)
        
        // 验证六级防频闪系统状态
        assert(antiFlickerSystem.isActive)
        assert(antiFlickerSystem.currentLevel in 1..6)
    }
}
```

#### 传感器测试
```kotlin
// 测试光传感器功能
fun testLightSensor() {
    // 检查传感器可用性
    assert(lightSensorManager.isSensorAvailable())
    
    // 测试数据读取
    val lightLevel = lightSensorManager.getCurrentLightLevel()
    assert(lightLevel >= 0f)
    
    // 测试数据平滑处理
    val smoothedLevel = lightSensorManager.getSmoothedLightLevel()
    assert(smoothedLevel >= 0f)
    
    // 测试六级防频闪系统集成
    val antiFlickerLevel = lightSensorManager.getAntiFlickerProcessedLevel()
    assert(antiFlickerLevel >= 0f)
}
```

#### 权限测试
```kotlin
// 测试系统权限
fun testPermissions() {
    // 检查写入设置权限
    val hasWriteSettings = Settings.System.canWrite(context)
    assert(hasWriteSettings)
    
    // 检查应用类型
    val isSystemApp = isSystemApplication()
    val isDebugMode = isDebugMode()
    
    Log.i(TAG, "应用类型：系统应用=$isSystemApp, 调试模式=$isDebugMode")
}
```

### 5. 服务测试

#### 后台服务测试
```kotlin
// 测试后台服务功能
fun testBackgroundService() {
    // 启动服务
    val intent = Intent(context, EyeCareBackgroundService::class.java)
    context.startForegroundService(intent)
    
    // 检查服务状态
    assert(isServiceRunning(EyeCareBackgroundService::class.java))
    
    // 测试六级防频闪系统集成
    val antiFlickerActive = antiFlickerSystem.isActive
    assert(antiFlickerActive)
    
    // 测试服务重启
    context.stopService(intent)
    Thread.sleep(1000)
    context.startForegroundService(intent)
    assert(isServiceRunning(EyeCareBackgroundService::class.java))
}
```

#### 服务监控测试
```kotlin
// 测试服务监控功能
fun testServiceMonitoring() {
    val monitor = ServiceMonitor(context)
    
    // 启动监控
    monitor.startMonitoring()
    
    // 模拟服务异常
    context.stopService(Intent(context, EyeCareBackgroundService::class.java))
    
    // 等待自动重启
    Thread.sleep(5000)
    
    // 验证服务已重启
    assert(isServiceRunning(EyeCareBackgroundService::class.java))
    
    // 验证六级防频闪系统恢复
    assert(antiFlickerSystem.isActive)
}
```

### 6. 性能测试

#### 内存使用测试
```kotlin
// 测试内存使用情况
fun testMemoryUsage() {
    val runtime = Runtime.getRuntime()
    val initialMemory = runtime.totalMemory() - runtime.freeMemory()
    
    // 运行护眼功能
    brightnessController.startEyeCare()
    
    val finalMemory = runtime.totalMemory() - runtime.freeMemory()
    val memoryIncrease = finalMemory - initialMemory
    
    // 内存增长不应超过10MB
    assert(memoryIncrease < 10 * 1024 * 1024)
}
```

#### 电池消耗测试
```kotlin
// 测试电池消耗
fun testBatteryConsumption() {
    val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
    val initialLevel = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY)
    
    // 运行护眼功能1小时
    brightnessController.startEyeCare()
    Thread.sleep(3600000) // 1小时
    
    val finalLevel = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY)
    val batteryDrain = initialLevel - finalLevel
    
    // 电池消耗不应超过5%
    assert(batteryDrain < 5)
}
```

#### 六级防频闪系统性能测试
```kotlin
// 测试六级防频闪系统性能
fun testAntiFlickerPerformance() {
    val startTime = System.currentTimeMillis()
    
    // 执行100次防频闪亮度调节
    repeat(100) { i ->
        val targetBrightness = 0.3f + (i % 10) * 0.01f
        antiFlickerSystem.updateBrightnessWithAntiFlicker(
            targetBrightness, EyeCareMode.STANDARD, Environment.INDOOR_BRIGHT
        )
    }
    
    val endTime = System.currentTimeMillis()
    val totalTime = endTime - startTime
    
    // 100次调节应在1秒内完成
    assert(totalTime < 1000)
    
    // 验证CPU使用率
    val cpuUsage = getCpuUsage()
    assert(cpuUsage < 10.0) // CPU使用率不应超过10%
}
```

## 故障排除

### 1. 常见问题诊断

#### 六级防频闪系统问题
```kotlin
// 诊断六级防频闪系统问题
fun diagnoseAntiFlickerIssues() {
    val issues = mutableListOf<String>()
    
    // 检查防频闪系统状态
    if (!antiFlickerSystem.isActive) {
        issues.add("六级防频闪系统未激活")
    }
    
    // 检查防频闪级别
    if (antiFlickerSystem.currentLevel !in 1..6) {
        issues.add("防频闪级别异常：${antiFlickerSystem.currentLevel}")
    }
    
    // 检查传感器数据
    val sensorData = lightSensorManager.getCurrentLightLevel()
    if (sensorData < 0) {
        issues.add("传感器数据异常：$sensorData")
    }
    
    // 检查亮度调节权限
    if (!Settings.System.canWrite(context)) {
        issues.add("缺少亮度调节权限")
    }
    
    return issues
}
```

#### 户外可视性问题
```kotlin
// 诊断户外可视性问题
fun diagnoseOutdoorVisibilityIssues() {
    val issues = mutableListOf<String>()
    
    // 检查环境检测
    val environment = outdoorSystem.detectEnvironment(lightSensorManager.getCurrentLightLevel())
    if (environment == Environment.UNKNOWN) {
        issues.add("环境检测失败")
    }
    
    // 检查户外亮度计算
    val outdoorBrightness = outdoorSystem.calculateOutdoorBrightness(
        lightSensorManager.getCurrentLightLevel(), 
        brightnessController.currentMode
    )
    if (outdoorBrightness < 0.2f || outdoorBrightness > 1.0f) {
        issues.add("户外亮度计算异常：$outdoorBrightness")
    }
    
    return issues
}
```

#### 干眼症模式问题
```kotlin
// 诊断干眼症模式问题
fun diagnoseDryEyeModeIssues() {
    val issues = mutableListOf<String>()
    
    // 检查极低亮度功能
    val ultraLowBrightness = dryEyeSystem.calculateUltraLowBrightness(brightnessController.currentMode)
    if (ultraLowBrightness > 0.01f) {
        issues.add("极低亮度计算异常：$ultraLowBrightness")
    }
    
    // 检查渐进式调节
    val currentBrightness = brightnessController.currentBrightness
    val targetBrightness = 0.001f
    val gradualBrightness = dryEyeSystem.gradualBrightnessReduction(targetBrightness, currentBrightness)
    if (gradualBrightness < targetBrightness - 0.01f) {
        issues.add("渐进式调节异常")
    }
    
    return issues
}
```

#### 服务启动失败
```kotlin
// 诊断服务启动问题
fun diagnoseServiceStartup() {
    val issues = mutableListOf<String>()
    
    // 检查权限
    if (!Settings.System.canWrite(context)) {
        issues.add("缺少WRITE_SETTINGS权限")
    }
    
    // 检查前台服务权限
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
        if (!hasForegroundServicePermission()) {
            issues.add("缺少FOREGROUND_SERVICE权限")
        }
    }
    
    // 检查电池优化
    if (isBatteryOptimizationEnabled()) {
        issues.add("电池优化可能影响服务运行")
    }
    
    // 检查六级防频闪系统
    if (!antiFlickerSystem.isActive) {
        issues.add("六级防频闪系统未激活")
    }
    
    return issues
}
```

#### 传感器问题诊断
```kotlin
// 诊断传感器问题
fun diagnoseSensorIssues() {
    val issues = mutableListOf<String>()
    
    // 检查传感器可用性
    val sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
    val lightSensor = sensorManager.getDefaultSensor(Sensor.TYPE_LIGHT)
    
    if (lightSensor == null) {
        issues.add("设备不支持光传感器")
    } else {
        // 检查传感器精度
        if (lightSensor.maximumRange < 1000) {
            issues.add("光传感器精度较低")
        }
    }
    
    // 检查六级防频闪系统集成
    if (!lightSensorManager.isAntiFlickerEnabled()) {
        issues.add("传感器防频闪功能未启用")
    }
    
    return issues
}
```

### 2. 自动修复机制

#### 六级防频闪系统自动修复
```kotlin
// 自动修复六级防频闪系统
fun autoRepairAntiFlickerSystem() {
    if (!antiFlickerSystem.isActive) {
        Log.w(TAG_ANTI_FLICKER, "检测到六级防频闪系统异常，尝试自动修复")
        
        try {
            // 重新初始化防频闪系统
            antiFlickerSystem.initialize()
            
            // 验证修复结果
            if (antiFlickerSystem.isActive) {
                Log.i(TAG_ANTI_FLICKER, "六级防频闪系统自动修复成功")
            } else {
                Log.e(TAG_ANTI_FLICKER, "六级防频闪系统自动修复失败")
            }
        } catch (e: Exception) {
            Log.e(TAG_ANTI_FLICKER, "六级防频闪系统修复异常", e)
        }
    }
}
```

#### 户外可视性自动修复
```kotlin
// 自动修复户外可视性系统
fun autoRepairOutdoorVisibility() {
    val environment = outdoorSystem.detectEnvironment(lightSensorManager.getCurrentLightLevel())
    if (environment == Environment.UNKNOWN) {
        Log.w(TAG_OUTDOOR, "检测到户外环境检测异常，尝试自动修复")
        
        try {
            // 重新校准环境检测
            outdoorSystem.recalibrate()
            
            // 验证修复结果
            val newEnvironment = outdoorSystem.detectEnvironment(lightSensorManager.getCurrentLightLevel())
            if (newEnvironment != Environment.UNKNOWN) {
                Log.i(TAG_OUTDOOR, "户外可视性系统自动修复成功")
            } else {
                Log.e(TAG_OUTDOOR, "户外可视性系统自动修复失败")
            }
        } catch (e: Exception) {
            Log.e(TAG_OUTDOOR, "户外可视性系统修复异常", e)
        }
    }
}
```

#### 服务自动重启
```kotlin
// 自动重启服务
fun autoRestartService() {
    if (!isServiceRunning(EyeCareBackgroundService::class.java)) {
        Log.w(TAG, "检测到服务停止，尝试自动重启")
        
        try {
            val intent = Intent(context, EyeCareBackgroundService::class.java)
            context.startForegroundService(intent)
            
            // 等待服务启动
            Thread.sleep(2000)
            
            if (isServiceRunning(EyeCareBackgroundService::class.java)) {
                Log.i(TAG, "服务自动重启成功")
                
                // 验证六级防频闪系统恢复
                if (antiFlickerSystem.isActive) {
                    Log.i(TAG_ANTI_FLICKER, "六级防频闪系统已恢复")
                }
            } else {
                Log.e(TAG, "服务自动重启失败")
            }
        } catch (e: Exception) {
            Log.e(TAG, "服务重启异常", e)
        }
    }
}
```

#### 权限自动检查
```kotlin
// 自动检查权限状态
fun autoCheckPermissions() {
    val permissionStatus = checkPermissionStatus()
    
    when (permissionStatus) {
        PermissionStatus.GRANTED -> {
            Log.i(TAG, "所有权限已授予")
        }
        PermissionStatus.PARTIAL -> {
            Log.w(TAG, "部分权限缺失，尝试引导用户授权")
            showPermissionGuide()
        }
        PermissionStatus.DENIED -> {
            Log.e(TAG, "关键权限被拒绝，功能受限")
            showPermissionError()
        }
    }
}
```

### 3. 用户反馈收集

#### 错误报告生成
```kotlin
// 生成错误报告
fun generateErrorReport(): String {
    val report = StringBuilder()
    
    report.appendLine("=== 护眼应用错误报告 ===")
    report.appendLine("时间：${SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(Date())}")
    report.appendLine("设备：${Build.MANUFACTURER} ${Build.MODEL}")
    report.appendLine("Android版本：${Build.VERSION.RELEASE} (API ${Build.VERSION.SDK_INT})")
    report.appendLine("应用版本：${BuildConfig.VERSION_NAME}")
    
    // 权限状态
    report.appendLine("\n=== 权限状态 ===")
    report.appendLine("WRITE_SETTINGS：${Settings.System.canWrite(context)}")
    report.appendLine("FOREGROUND_SERVICE：${hasForegroundServicePermission()}")
    
    // 服务状态
    report.appendLine("\n=== 服务状态 ===")
    report.appendLine("后台服务：${isServiceRunning(EyeCareBackgroundService::class.java)}")
    report.appendLine("传感器状态：${lightSensorManager.getSensorStatus()}")
    
    // 六级防频闪系统状态
    report.appendLine("\n=== 六级防频闪系统状态 ===")
    report.appendLine("系统激活：${antiFlickerSystem.isActive}")
    report.appendLine("当前级别：${antiFlickerSystem.currentLevel}")
    report.appendLine("系统状态：${antiFlickerSystem.status}")
    
    // 户外可视性状态
    report.appendLine("\n=== 户外可视性状态 ===")
    report.appendLine("环境检测：${outdoorSystem.detectEnvironment(lightSensorManager.getCurrentLightLevel())}")
    report.appendLine("可视性保障：${outdoorSystem.getVisibilityLevel()}")
    
    // 干眼症模式状态
    report.appendLine("\n=== 干眼症模式状态 ===")
    report.appendLine("极低亮度模式：${dryEyeSystem.isActive}")
    report.appendLine("当前亮度：${brightnessController.currentBrightness}")
    report.appendLine("舒适度：${dryEyeSystem.getComfortLevel()}")
    
    // 系统设置
    report.appendLine("\n=== 系统设置 ===")
    report.appendLine("自动亮度：${isSystemAutoBrightnessEnabled()}")
    report.appendLine("电池优化：${isBatteryOptimizationEnabled()}")
    
    return report.toString()
}
```

## 测试环境

### 1. 模拟器测试
- 使用Android模拟器进行基础功能测试
- 模拟不同的Android版本和设备配置
- 测试权限申请流程
- 模拟六级防频闪系统效果

### 2. 真机测试
- 在不同品牌设备上测试
- 测试各种Android版本兼容性
- 验证传感器功能
- 测试户外环境下的实际效果

### 3. 压力测试
- 长时间运行测试
- 内存泄漏检测
- 电池消耗测试
- 六级防频闪系统稳定性测试

### 4. 用户体验测试
- 干眼症患者实际使用测试
- 户外环境下的可视性测试
- 防频闪效果主观评价
- 极低亮度模式舒适度测试
