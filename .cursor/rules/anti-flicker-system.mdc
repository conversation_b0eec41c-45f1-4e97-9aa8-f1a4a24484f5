# 六级防频闪系统实现指南

## 系统概述

六级防频闪系统是护眼应用的核心技术，确保用户在任何环境下都感受到丝滑般的亮度调节体验，完全消除频闪感，实现无感知的智能护眼。

### 核心目标
- **零频闪体验**：用户完全感受不到亮度调节过程
- **智能适应**：根据环境自动调整防频闪参数
- **性能优化**：最小化CPU和电池消耗
- **稳定可靠**：在各种环境下保持稳定运行

## 六级防频闪机制详解

### 第一级：传感器数据平滑
**目标**：消除光传感器噪声，提供稳定的环境光照数据

```kotlin
/**
 * 传感器数据平滑处理
 * 使用移动平均算法消除传感器噪声
 */
class SensorDataSmoothing {
    
    companion object {
        private const val WINDOW_SIZE = 10  // 移动平均窗口大小
        private const val MIN_VALID_RANGE = 0.8f  // 最小有效数据范围
        private const val MAX_VALID_RANGE = 1.2f  // 最大有效数据范围
    }
    
    private val dataWindow = mutableListOf<Float>()
    
    /**
     * 平滑传感器数据
     * @param rawData 原始传感器数据
     * @return 平滑后的数据
     */
    fun smoothSensorData(rawData: Float): Float {
        // 数据有效性检查
        if (!isValidData(rawData)) {
            Log.w(TAG, "传感器数据无效：$rawData")
            return getLastValidData()
        }
        
        // 添加到数据窗口
        dataWindow.add(rawData)
        if (dataWindow.size > WINDOW_SIZE) {
            dataWindow.removeAt(0)
        }
        
        // 计算移动平均
        val smoothedData = dataWindow.average().toFloat()
        
        // 异常值检测和处理
        return handleOutliers(smoothedData, rawData)
    }
    
    /**
     * 检查数据有效性
     */
    private fun isValidData(data: Float): Boolean {
        return data >= 0f && !data.isNaN() && !data.isInfinite()
    }
    
    /**
     * 异常值处理
     */
    private fun handleOutliers(smoothedData: Float, rawData: Float): Float {
        val ratio = rawData / smoothedData
        return if (ratio in MIN_VALID_RANGE..MAX_VALID_RANGE) {
            smoothedData
        } else {
            // 异常值使用历史平均值
            getLastValidData()
        }
    }
}
```

### 第二级：亮度计算平滑
**目标**：在亮度计算过程中应用智能平滑因子，防止计算误差

```kotlin
/**
 * 亮度计算平滑处理
 * 智能平滑因子2-10%，防止计算误差
 */
class BrightnessCalculationSmoothing {
    
    companion object {
        private const val MIN_SMOOTHING_FACTOR = 0.02f  // 2%最小平滑因子
        private const val MAX_SMOOTHING_FACTOR = 0.10f  // 10%最大平滑因子
        private const val BASE_SMOOTHING_FACTOR = 0.05f  // 5%基础平滑因子
    }
    
    private var lastCalculatedBrightness = 0.5f
    
    /**
     * 平滑亮度计算
     * @param targetBrightness 目标亮度
     * @param mode 护眼模式
     * @param environment 环境类型
     * @return 平滑后的亮度值
     */
    fun smoothBrightnessCalculation(
        targetBrightness: Float,
        mode: EyeCareMode,
        environment: Environment
    ): Float {
        // 计算智能平滑因子
        val smoothingFactor = calculateSmoothingFactor(mode, environment)
        
        // 应用平滑算法
        val smoothedBrightness = lastCalculatedBrightness + 
            (targetBrightness - lastCalculatedBrightness) * smoothingFactor
        
        // 更新历史值
        lastCalculatedBrightness = smoothedBrightness
        
        return smoothedBrightness.coerceIn(0f, 1f)
    }
    
    /**
     * 计算智能平滑因子
     */
    private fun calculateSmoothingFactor(mode: EyeCareMode, environment: Environment): Float {
        var factor = BASE_SMOOTHING_FACTOR
        
        // 根据护眼模式调整
        when (mode) {
            EyeCareMode.STANDARD -> factor *= 1.0f
            EyeCareMode.NIGHT -> factor *= 0.8f  // 夜间模式更平滑
            EyeCareMode.ULTRA_SENSITIVE -> factor *= 0.6f  // 超敏感模式最平滑
        }
        
        // 根据环境调整
        when (environment) {
            Environment.INDOOR_DARK -> factor *= 0.7f  // 暗环境更平滑
            Environment.INDOOR_BRIGHT -> factor *= 1.0f
            Environment.OUTDOOR -> factor *= 1.2f  // 户外环境稍快
        }
        
        return factor.coerceIn(MIN_SMOOTHING_FACTOR, MAX_SMOOTHING_FACTOR)
    }
}
```

### 第三级：微小变化过滤
**目标**：过滤0.5%以下的微小变化，避免频繁调节

```kotlin
/**
 * 微小变化过滤系统
 * 0.5%以下变化不执行调节
 */
class TinyChangeFilter {
    
    companion object {
        private const val MIN_CHANGE_THRESHOLD = 0.005f  // 0.5%最小变化阈值
        private const val HYSTERESIS_FACTOR = 1.2f  // 滞后因子，防止抖动
    }
    
    private var lastAppliedBrightness = 0.5f
    private var changeCount = 0
    
    /**
     * 检查是否需要执行亮度调节
     * @param newBrightness 新亮度值
     * @param currentBrightness 当前亮度值
     * @return 是否执行调节
     */
    fun shouldUpdateBrightness(newBrightness: Float, currentBrightness: Float): Boolean {
        val change = abs(newBrightness - currentBrightness)
        
        // 基础阈值检查
        if (change < MIN_CHANGE_THRESHOLD) {
            return false
        }
        
        // 滞后检查，防止在阈值附近抖动
        val hysteresisThreshold = MIN_CHANGE_THRESHOLD * HYSTERESIS_FACTOR
        if (change < hysteresisThreshold && changeCount > 0) {
            changeCount--
            return false
        }
        
        // 变化趋势检查
        val trend = newBrightness - lastAppliedBrightness
        if (abs(trend) < MIN_CHANGE_THRESHOLD) {
            return false
        }
        
        // 更新状态
        lastAppliedBrightness = newBrightness
        changeCount++
        
        return true
    }
    
    /**
     * 重置过滤器状态
     */
    fun reset() {
        lastAppliedBrightness = 0.5f
        changeCount = 0
    }
}
```

### 第四级：步长限制
**目标**：限制单次亮度调节的步长，确保平滑过渡

```kotlin
/**
 * 步长限制系统
 * 0.5-10%步长限制防止大幅跳动
 */
class StepSizeLimiter {
    
    companion object {
        private const val MIN_STEP_SIZE = 0.005f  // 0.5%最小步长
        private const val MAX_STEP_SIZE = 0.10f   // 10%最大步长
        private const val DEFAULT_STEP_SIZE = 0.05f  // 5%默认步长
    }
    
    /**
     * 限制步长
     * @param targetBrightness 目标亮度
     * @param currentBrightness 当前亮度
     * @param mode 护眼模式
     * @return 限制后的目标亮度
     */
    fun limitStepSize(
        targetBrightness: Float,
        currentBrightness: Float,
        mode: EyeCareMode
    ): Float {
        val change = targetBrightness - currentBrightness
        val absChange = abs(change)
        
        // 计算允许的步长
        val allowedStepSize = calculateAllowedStepSize(mode, absChange)
        
        // 应用步长限制
        return if (absChange > allowedStepSize) {
            val direction = if (change > 0) 1f else -1f
            currentBrightness + direction * allowedStepSize
        } else {
            targetBrightness
        }
    }
    
    /**
     * 计算允许的步长
     */
    private fun calculateAllowedStepSize(mode: EyeCareMode, currentChange: Float): Float {
        var stepSize = DEFAULT_STEP_SIZE
        
        // 根据护眼模式调整
        when (mode) {
            EyeCareMode.STANDARD -> stepSize *= 1.0f
            EyeCareMode.NIGHT -> stepSize *= 0.7f  // 夜间模式步长更小
            EyeCareMode.ULTRA_SENSITIVE -> stepSize *= 0.5f  // 超敏感模式步长最小
        }
        
        // 根据当前变化幅度调整
        if (currentChange > MAX_STEP_SIZE) {
            stepSize *= 1.5f  // 大幅变化时允许更大步长
        } else if (currentChange < MIN_STEP_SIZE) {
            stepSize *= 0.5f  // 小幅变化时使用更小步长
        }
        
        return stepSize.coerceIn(MIN_STEP_SIZE, MAX_STEP_SIZE)
    }
}
```

### 第五级：更新间隔控制
**目标**：控制亮度调节的频率，避免过于频繁的更新

```kotlin
/**
 * 更新间隔控制系统
 * 2.5-6秒稳定更新间隔
 */
class UpdateIntervalController {
    
    companion object {
        private const val MIN_UPDATE_INTERVAL = 2500L  // 2.5秒最小更新间隔
        private const val MAX_UPDATE_INTERVAL = 6000L  // 6秒最大更新间隔
        private const val DEFAULT_UPDATE_INTERVAL = 4000L  // 4秒默认更新间隔
    }
    
    private var lastUpdateTime = 0L
    private var currentInterval = DEFAULT_UPDATE_INTERVAL
    
    /**
     * 检查是否可以执行更新
     * @param mode 护眼模式
     * @param environment 环境类型
     * @return 是否可以更新
     */
    fun canUpdate(mode: EyeCareMode, environment: Environment): Boolean {
        val currentTime = System.currentTimeMillis()
        val timeSinceLastUpdate = currentTime - lastUpdateTime
        
        // 检查时间间隔
        if (timeSinceLastUpdate < currentInterval) {
            return false
        }
        
        // 更新间隔时间
        lastUpdateTime = currentTime
        
        // 动态调整更新间隔
        adjustUpdateInterval(mode, environment)
        
        return true
    }
    
    /**
     * 动态调整更新间隔
     */
    private fun adjustUpdateInterval(mode: EyeCareMode, environment: Environment) {
        var newInterval = DEFAULT_UPDATE_INTERVAL
        
        // 根据护眼模式调整
        when (mode) {
            EyeCareMode.STANDARD -> newInterval *= 1.0f
            EyeCareMode.NIGHT -> newInterval *= 1.3f  // 夜间模式更新更慢
            EyeCareMode.ULTRA_SENSITIVE -> newInterval *= 1.5f  // 超敏感模式更新最慢
        }
        
        // 根据环境调整
        when (environment) {
            Environment.INDOOR_DARK -> newInterval *= 1.2f  // 暗环境更新更慢
            Environment.INDOOR_BRIGHT -> newInterval *= 1.0f
            Environment.OUTDOOR -> newInterval *= 0.8f  // 户外环境更新稍快
        }
        
        currentInterval = newInterval.toLong().coerceIn(MIN_UPDATE_INTERVAL, MAX_UPDATE_INTERVAL)
    }
    
    /**
     * 重置控制器
     */
    fun reset() {
        lastUpdateTime = 0L
        currentInterval = DEFAULT_UPDATE_INTERVAL
    }
}
```

### 第六级：环境自适应
**目标**：根据环境变化自动调整防频闪参数

```kotlin
/**
 * 环境自适应系统
 * 根据光照变化调整参数
 */
class EnvironmentAdaptation {
    
    companion object {
        private const val ADAPTATION_THRESHOLD = 0.3f  // 30%变化阈值
        private const val LEARNING_RATE = 0.1f  // 学习率
    }
    
    private var lastEnvironment = Environment.INDOOR_BRIGHT
    private var adaptationHistory = mutableListOf<Environment>()
    
    /**
     * 环境自适应参数
     */
    data class AntiFlickerParams(
        val smoothingFactor: Float,
        val stepSize: Float,
        val updateInterval: Long,
        val changeThreshold: Float
    )
    
    /**
     * 根据环境调整防频闪参数
     * @param lightLevel 环境光照强度
     * @param mode 护眼模式
     * @return 调整后的参数
     */
    fun adaptToEnvironment(lightLevel: Float, mode: EyeCareMode): AntiFlickerParams {
        val currentEnvironment = detectEnvironment(lightLevel)
        
        // 环境变化检测
        if (currentEnvironment != lastEnvironment) {
            onEnvironmentChanged(currentEnvironment)
        }
        
        // 计算自适应参数
        val params = calculateAdaptiveParams(currentEnvironment, mode)
        
        // 更新历史
        adaptationHistory.add(currentEnvironment)
        if (adaptationHistory.size > 10) {
            adaptationHistory.removeAt(0)
        }
        
        lastEnvironment = currentEnvironment
        
        return params
    }
    
    /**
     * 检测环境类型
     */
    private fun detectEnvironment(lightLevel: Float): Environment {
        return when {
            lightLevel < 100 -> Environment.INDOOR_DARK
            lightLevel < 500 -> Environment.INDOOR_BRIGHT
            else -> Environment.OUTDOOR
        }
    }
    
    /**
     * 环境变化处理
     */
    private fun onEnvironmentChanged(newEnvironment: Environment) {
        Log.i(TAG, "环境变化：$lastEnvironment -> $newEnvironment")
        
        // 重置相关参数
        // 这里可以添加环境变化时的特殊处理逻辑
    }
    
    /**
     * 计算自适应参数
     */
    private fun calculateAdaptiveParams(environment: Environment, mode: EyeCareMode): AntiFlickerParams {
        var smoothingFactor = 0.05f
        var stepSize = 0.05f
        var updateInterval = 4000L
        var changeThreshold = 0.005f
        
        // 根据环境调整
        when (environment) {
            Environment.INDOOR_DARK -> {
                smoothingFactor *= 0.8f  // 暗环境更平滑
                stepSize *= 0.7f
                updateInterval = (updateInterval * 1.2).toLong()
                changeThreshold *= 1.2f
            }
            Environment.INDOOR_BRIGHT -> {
                // 使用默认参数
            }
            Environment.OUTDOOR -> {
                smoothingFactor *= 1.2f  // 户外环境稍快
                stepSize *= 1.1f
                updateInterval = (updateInterval * 0.8).toLong()
                changeThreshold *= 0.9f
            }
        }
        
        // 根据护眼模式调整
        when (mode) {
            EyeCareMode.STANDARD -> {
                // 使用计算出的参数
            }
            EyeCareMode.NIGHT -> {
                smoothingFactor *= 0.8f
                stepSize *= 0.7f
                updateInterval = (updateInterval * 1.3).toLong()
                changeThreshold *= 1.2f
            }
            EyeCareMode.ULTRA_SENSITIVE -> {
                smoothingFactor *= 0.6f
                stepSize *= 0.5f
                updateInterval = (updateInterval * 1.5).toLong()
                changeThreshold *= 1.5f
            }
        }
        
        return AntiFlickerParams(
            smoothingFactor = smoothingFactor.coerceIn(0.02f, 0.10f),
            stepSize = stepSize.coerceIn(0.005f, 0.10f),
            updateInterval = updateInterval.coerceIn(2500L, 6000L),
            changeThreshold = changeThreshold.coerceIn(0.003f, 0.008f)
        )
    }
}
```

## 六级防频闪系统集成

### 主控制器
```kotlin
/**
 * 六级防频闪系统主控制器
 * 整合所有六级防频闪机制
 */
class SixLevelAntiFlickerController {
    
    private val sensorSmoothing = SensorDataSmoothing()
    private val calculationSmoothing = BrightnessCalculationSmoothing()
    private val changeFilter = TinyChangeFilter()
    private val stepLimiter = StepSizeLimiter()
    private val intervalController = UpdateIntervalController()
    private val environmentAdaptation = EnvironmentAdaptation()
    
    companion object {
        private const val TAG = "AntiFlickerController"
    }
    
    /**
     * 六级防频闪亮度调节
     * @param targetBrightness 目标亮度
     * @param currentBrightness 当前亮度
     * @param mode 护眼模式
     * @param lightLevel 环境光照强度
     * @return 是否执行亮度调节
     */
    fun updateBrightnessWithAntiFlicker(
        targetBrightness: Float,
        currentBrightness: Float,
        mode: EyeCareMode,
        lightLevel: Float
    ): Boolean {
        try {
            Log.d(TAG, "开始六级防频闪亮度调节：目标=$targetBrightness, 当前=$currentBrightness")
            
            // 第一级：传感器数据平滑
            val smoothedTarget = sensorSmoothing.smoothSensorData(targetBrightness)
            Log.d(TAG, "第一级完成：传感器数据平滑 $targetBrightness -> $smoothedTarget")
            
            // 第二级：亮度计算平滑
            val environment = environmentAdaptation.detectEnvironment(lightLevel)
            val calculatedBrightness = calculationSmoothing.smoothBrightnessCalculation(
                smoothedTarget, mode, environment
            )
            Log.d(TAG, "第二级完成：亮度计算平滑 $smoothedTarget -> $calculatedBrightness")
            
            // 第三级：微小变化过滤
            if (!changeFilter.shouldUpdateBrightness(calculatedBrightness, currentBrightness)) {
                Log.d(TAG, "第三级过滤：变化太小，跳过调节")
                return false
            }
            Log.d(TAG, "第三级完成：微小变化过滤通过")
            
            // 第四级：步长限制
            val limitedBrightness = stepLimiter.limitStepSize(calculatedBrightness, currentBrightness, mode)
            Log.d(TAG, "第四级完成：步长限制 $calculatedBrightness -> $limitedBrightness")
            
            // 第五级：更新间隔控制
            if (!intervalController.canUpdate(mode, environment)) {
                Log.d(TAG, "第五级过滤：更新间隔太短，跳过调节")
                return false
            }
            Log.d(TAG, "第五级完成：更新间隔控制通过")
            
            // 第六级：环境自适应
            val adaptiveParams = environmentAdaptation.adaptToEnvironment(lightLevel, mode)
            Log.d(TAG, "第六级完成：环境自适应参数=$adaptiveParams")
            
            // 应用最终亮度
            applyBrightness(limitedBrightness)
            Log.i(TAG, "六级防频闪亮度调节完成：$currentBrightness -> $limitedBrightness")
            
            return true
            
        } catch (e: Exception) {
            Log.e(TAG, "六级防频闪系统异常", e)
            return false
        }
    }
    
    /**
     * 应用亮度调节
     */
    private fun applyBrightness(brightness: Float) {
        // 实际的亮度调节实现
        // 这里调用系统的亮度调节API
    }
    
    /**
     * 重置系统状态
     */
    fun reset() {
        sensorSmoothing.reset()
        changeFilter.reset()
        intervalController.reset()
        Log.i(TAG, "六级防频闪系统已重置")
    }
}
```

## 性能优化

### 1. 内存优化
- 使用对象池减少GC压力
- 及时释放不需要的资源
- 避免创建大量临时对象

### 2. CPU优化
- 使用缓存减少重复计算
- 优化算法复杂度
- 避免频繁的数学运算

### 3. 电池优化
- 减少不必要的传感器读取
- 优化更新频率
- 智能休眠机制

## 测试规范

### 1. 单元测试
- 测试每个级别的防频闪机制
- 验证参数计算准确性
- 测试异常情况处理

### 2. 集成测试
- 测试六级系统整体效果
- 验证性能指标
- 测试稳定性

### 3. 用户体验测试
- 主观频闪感受测试
- 不同环境下的效果测试
- 长时间使用稳定性测试

## 监控和诊断

### 1. 性能监控
- CPU使用率监控
- 内存使用监控
- 响应时间监控

### 2. 效果监控
- 频闪检测
- 亮度变化平滑度
- 用户满意度

### 3. 故障诊断
- 系统状态检查
- 异常情况处理
- 自动修复机制
description:
globs:
alwaysApply: false
---
