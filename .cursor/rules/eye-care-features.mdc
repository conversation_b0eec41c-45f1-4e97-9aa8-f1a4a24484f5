# 护眼功能特性指南

## 核心功能架构

### 1. 护眼模式系统
护眼应用提供三种护眼模式，每种模式有不同的亮度和色彩调节策略：

- **标准模式**：适合日常使用，平衡护眼效果和视觉体验
- **夜间模式**：适合夜间使用，降低蓝光，提供暖色调
- **超敏感模式**：适合干眼症严重患者，最大程度保护眼睛

### 2. 六级防频闪系统
亮度控制是护眼应用的核心功能，采用六级防频闪系统确保丝滑体验：

- [BrightnessController.kt](mdc:app/src/main/java/com/example/myapplication5/BrightnessController.kt) - 主要亮度控制逻辑（六级防频闪）
- [LightSensorManager.kt](mdc:app/src/main/java/com/example/myapplication5/LightSensorManager.kt) - 光传感器管理（数据平滑处理）
- [EyeCareBackgroundService.kt](mdc:app/src/main/java/com/example/myapplication5/EyeCareBackgroundService.kt) - 后台亮度调节服务（防频闪控制）

#### 六级防频闪机制
1. **传感器数据平滑**：消除传感器噪声，使用移动平均算法
2. **亮度计算平滑**：智能平滑因子2-10%，防止计算误差
3. **后台服务防频闪**：微小变化0.5%过滤，避免频繁调节
4. **步长限制**：0.5-10%防止大幅跳动，确保平滑过渡
5. **更新间隔控制**：2.5-6秒稳定更新，避免频繁触发
6. **环境自适应**：根据光照变化调整参数，智能适应环境

### 3. 户外可视性保障系统
专门解决户外环境下的可视性问题：

- **标准模式户外**：75-95%亮度确保户外清晰可见
- **超敏感模式户外**：40-55%亮度平衡护眼和可视性
- **夜间模式强光**：20%亮度防止强光刺激
- **智能环境检测**：自动识别室内外环境变化
- **动态更新频率**：根据环境调整传感器采样频率

### 4. 干眼症极低亮度支持
专为干眼症患者设计的极低亮度模式：

- **深夜模式**：0.1%超低亮度（适合深夜使用）
- **超极低模式**：0.2%极低亮度（适合干眼症严重患者）
- **极低模式**：0.3%低亮度（适合敏感用户）
- **渐进式调节**：从正常亮度逐步降低，避免突然变化

### 5. 色彩主题系统
提供两种护眼色彩主题：

- **绿色主题**：舒缓护眼，减少视觉疲劳
- **暖色调主题**：温暖舒适，适合夜间使用

## 重要设计原则

### 1. 护眼模式默认关闭
- 所有护眼功能默认关闭状态
- 用户必须主动开启才能激活护眼保护
- 提供更好的用户选择权和控制权

### 2. 六级防频闪优先
- 所有亮度调节必须通过六级防频闪系统
- 确保用户在任何环境下都感受到丝滑般的亮度调节体验
- 完全消除频闪感，实现无感知的智能护眼

### 3. 户外可视性优先
- 在户外环境下优先保证可视性
- 平衡护眼效果和视觉清晰度
- 智能识别环境变化并自动调整

### 4. 干眼症患者关怀
- 提供极低亮度支持敏感用户
- 渐进式亮度调节避免刺激
- 专门针对干眼症症状优化

### 5. 传感器诊断功能
- 内置完整的光传感器诊断
- 检测传感器可用性和精度
- 提供详细的诊断日志和状态信息

### 6. 系统冲突检测
- 自动检测系统自动亮度设置
- 识别亮度调节冲突
- 提供解决方案和用户引导

### 7. 服务监控和恢复
- 自动监控后台服务状态
- 检测服务异常并自动重启
- 确保护眼保护持续运行

## 权限和安全

### 系统权限要求
- `WRITE_SETTINGS`：调节系统亮度
- `WRITE_SECURE_SETTINGS`：调节安全设置
- `FOREGROUND_SERVICE`：前台服务权限
- `RECEIVE_BOOT_COMPLETED`：开机自启动

### 权限处理策略
1. **权限检查**：操作前检查权限状态
2. **用户引导**：提供清晰的权限申请流程
3. **降级处理**：权限不足时提供替代方案
4. **诊断工具**：内置权限诊断功能

## 用户体验设计

### 1. 大字体大按钮界面
- 专为干眼症患者设计
- 减少视觉疲劳
- 提高操作便利性

### 2. 实时状态反馈
- 显示当前护眼模式
- 实时亮度百分比
- 环境光照强度
- 服务运行状态
- 防频闪系统状态

### 3. 一键快速调节
- 快速切换护眼模式
- 一键开启/关闭护眼保护
- 紧急亮度调节
- 户外模式快速切换

### 4. 智能建议系统
- 根据环境提供护眼建议
- 实时护眼评级
- 个性化调节建议
- 户外可视性提醒

## 技术实现要点

### 1. 六级防频闪算法
```kotlin
/**
 * 六级防频闪亮度调节系统
 * 确保用户在任何环境下都感受到丝滑般的亮度调节体验
 */
class SixLevelAntiFlickerSystem {
    
    /**
     * 第一级：传感器数据平滑处理
     * 使用移动平均算法消除传感器噪声
     */
    private fun smoothSensorData(rawLevel: Float): Float {
        // 移动平均算法实现
        // 避免亮度频繁跳动
    }
    
    /**
     * 第二级：亮度计算平滑
     * 智能平滑因子2-10%
     */
    private fun smoothBrightnessCalculation(
        lightLevel: Float, 
        mode: EyeCareMode
    ): Float {
        // 根据护眼模式和光照强度计算最佳亮度
        // 应用智能平滑因子
    }
    
    /**
     * 第三级：微小变化过滤
     * 0.5%以下变化不执行调节
     */
    private fun filterTinyChanges(newBrightness: Float, currentBrightness: Float): Boolean {
        val change = abs(newBrightness - currentBrightness)
        return change >= 0.005f // 0.5%阈值
    }
    
    /**
     * 第四级：步长限制
     * 0.5-10%步长限制防止大幅跳动
     */
    private fun limitStepSize(newBrightness: Float, currentBrightness: Float): Float {
        val maxStep = 0.1f // 10%最大步长
        val change = newBrightness - currentBrightness
        return if (abs(change) > maxStep) {
            currentBrightness + (if (change > 0) maxStep else -maxStep)
        } else {
            newBrightness
        }
    }
    
    /**
     * 第五级：更新间隔控制
     * 2.5-6秒稳定更新间隔
     */
    private fun controlUpdateInterval(): Boolean {
        // 控制更新频率，避免频繁触发
    }
    
    /**
     * 第六级：环境自适应
     * 根据光照变化调整参数
     */
    private fun adaptToEnvironment(lightLevel: Float): AntiFlickerParams {
        // 根据环境智能调整防频闪参数
    }
}
```

### 2. 户外可视性算法
```kotlin
/**
 * 户外可视性保障系统
 * 确保户外环境下的清晰可见性
 */
class OutdoorVisibilitySystem {
    
    /**
     * 户外亮度计算
     * 根据护眼模式和环境光照计算户外最佳亮度
     */
    private fun calculateOutdoorBrightness(
        lightLevel: Float, 
        mode: EyeCareMode
    ): Float {
        return when (mode) {
            EyeCareMode.STANDARD -> {
                // 标准模式户外：75-95%亮度
                max(0.75f, min(0.95f, lightLevel / 1000f))
            }
            EyeCareMode.ULTRA_SENSITIVE -> {
                // 超敏感模式户外：40-55%亮度
                max(0.40f, min(0.55f, lightLevel / 2000f))
            }
            EyeCareMode.NIGHT -> {
                // 夜间模式强光：20%亮度
                max(0.20f, min(0.30f, lightLevel / 5000f))
            }
        }
    }
    
    /**
     * 环境检测
     * 自动识别室内外环境变化
     */
    private fun detectEnvironment(lightLevel: Float): Environment {
        return when {
            lightLevel > 500 -> Environment.OUTDOOR
            lightLevel > 100 -> Environment.INDOOR_BRIGHT
            else -> Environment.INDOOR_DARK
        }
    }
}
```

### 3. 干眼症极低亮度算法
```kotlin
/**
 * 干眼症极低亮度支持系统
 * 专为干眼症患者设计的极低亮度模式
 */
class DryEyeLowBrightnessSystem {
    
    /**
     * 极低亮度计算
     * 提供0.1%-0.3%的超低亮度支持
     */
    private fun calculateUltraLowBrightness(mode: EyeCareMode): Float {
        return when (mode) {
            EyeCareMode.NIGHT -> 0.001f  // 0.1%深夜模式
            EyeCareMode.ULTRA_SENSITIVE -> 0.002f  // 0.2%超极低模式
            EyeCareMode.STANDARD -> 0.003f  // 0.3%极低模式
        }
    }
    
    /**
     * 渐进式亮度调节
     * 从正常亮度逐步降低，避免突然变化
     */
    private fun gradualBrightnessReduction(
        targetBrightness: Float, 
        currentBrightness: Float
    ): Float {
        val step = 0.01f // 1%步长
        return if (currentBrightness > targetBrightness) {
            max(targetBrightness, currentBrightness - step)
        } else {
            targetBrightness
        }
    }
}
```

### 4. 服务生命周期管理
```kotlin
/**
 * 服务状态监控
 * 定期检查服务状态，自动重启异常服务
 */
private fun monitorServiceStatus() {
    // 定期检查服务状态
    // 自动重启异常服务
    // 记录服务运行日志
    // 六级防频闪系统状态监控
}
```

## 测试和调试

### 1. 防频闪系统测试
- 六级防频闪算法验证
- 频闪检测和消除测试
- 平滑调节效果验证
- 环境自适应测试

### 2. 户外可视性测试
- 户外环境模拟测试
- 可视性保障验证
- 环境检测准确性测试
- 亮度调节合理性验证

### 3. 干眼症模式测试
- 极低亮度功能测试
- 渐进式调节验证
- 用户舒适度测试
- 敏感用户适应性验证

### 4. 诊断工具
- 权限状态检查
- 传感器功能测试
- 服务运行状态检查
- 系统冲突检测
- 防频闪系统诊断

### 5. 日志系统
- 详细的操作日志
- 错误和异常记录
- 性能监控数据
- 用户行为分析
- 防频闪系统日志

### 6. 测试模式
- 模拟传感器数据
- 强制服务重启
- 权限测试模式
- 性能压力测试
- 防频闪效果测试
description:
globs:
alwaysApply: false
---
